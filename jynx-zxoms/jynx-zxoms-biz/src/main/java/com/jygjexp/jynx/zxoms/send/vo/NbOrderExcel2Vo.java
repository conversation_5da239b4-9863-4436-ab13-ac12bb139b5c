package com.jygjexp.jynx.zxoms.send.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: chenchang
 * @Description: 派送单导出实体对象2
 * @Date: 2024/12/7 15:07
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@Schema(description = "派送单导出实体对象2")
public class NbOrderExcel2Vo {

    @ColumnWidth(10)
    @ExcelProperty("Order ID")
    @Schema(description="NB订单ID")
    private Integer orderId;

    @Schema(description="(sorting center ID)分拣中心ID")
    private Integer scId;



    @Schema(description="(multiple package shipment)是否为一票多件")
    private Boolean isMps;

    @ColumnWidth(15)
    @ExcelProperty("(Sorting center)分拣中心")
    @Schema(description="分拣中心")
    private String sortingCenterName;

    @ColumnWidth(20)
    @ExcelProperty("(Package number)包裹编号")
    @Schema(description = "包裹编号")
    private String pkgNo;

    @ColumnWidth(15)
    @ExcelProperty("(client)客户")
    @Schema(description="客户")
    private String subChannel;

    @ColumnWidth(10)
    @ExcelProperty("(DriverId)司机ID")
    @Schema(description="司机ID")
    private Integer driverId;

    @ColumnWidth(12)
    @ExcelProperty("(Driver)司机Lastname")
    @Schema(description="姓")
    private String lastName;

    @ColumnWidth(14)
    @ExcelProperty("(Driver phone)司机电话")
    @Schema(description="手机号")
    private String mobile;


    @ColumnWidth(10)
    @ExcelProperty("(Order status Code)订单状态码")
    @Schema(description="(order Status Code)订单状态码")
    private Integer orderStatus;

    @ColumnWidth(20)
    @ExcelProperty("(Order status)订单状态")
    @Schema(description = "订单状态")
    private String orderStatusTitle;


    @Schema(description = "订单状态")
    private Integer useOrderStatus;

    @ColumnWidth(15)
    @ExcelProperty("(subbatch)子批次")
    @Schema(description="子批次")
    private String subBatchNo;

    @ColumnWidth(15)
    @ExcelProperty("(Customer order number)客户单号")
    @Schema(description="客户单号")
    private String customerOrderNo;

    @ColumnWidth(20)
    @ExcelProperty("Labling Time")
    @Schema(description="Labling Time")
    private String lablingTime;

    @ColumnWidth(20)
    @ExcelProperty("Gateway In Time")
    @Schema(description="Gateway In Time")
    private String gatewayInTime;

    @ColumnWidth(20)
    @ExcelProperty("(Blind scan time)盲扫时间")
    @Schema(description="盲扫时间")
    private String scanTime;

    @ColumnWidth(20)
    @ExcelProperty("in-transit time")
    @Schema(description="in-transit time")
    private String inTransitTime;

    @ColumnWidth(20)
    @ExcelProperty("F1 time")
    @Schema(description="F1 time")
    private String with211Time;

    @ColumnWidth(20)
    @ExcelProperty("Delivered Time")
    @Schema(description="Delivered Time")
    private String deliveredTime;

    @ColumnWidth(8)
    @ExcelProperty("Labeling-Gatewayin时效")
    @Schema(description="Labeling-Gatewayin时效")
    private String labelingGatewayinPrescription;

    @ColumnWidth(8)
    @ExcelProperty("Gatewayin-parcel scanned时效")
    @Schema(description="Gatewayin-parcel scanned时效")
    private String gatewayinParcelScannedPrescription;

    @ColumnWidth(8)
    @ExcelProperty("Parcel scanned-in transit时效")
    @Schema(description="Parcel scanned-in transit时效")
    private String parcelScannedInTransitPrescription;

    @ColumnWidth(8)
    @ExcelProperty("parcel scanned-delivered时效")
    @Schema(description="parcel scanned-delivered时效")
    private String parcelScannedDeliveredPrescription;

    @ColumnWidth(15)
    @ExcelProperty("bag Number")
    @Schema(description="bagNumber")
    private String bagNumber;

    @ColumnWidth(10)
    @ExcelProperty("(signer)签收人")
    @Schema(description="签收人")
    private String destName;

    @ColumnWidth(14)
    @ExcelProperty("(phone)电话")
    @Schema(description="电话")
    private String destTel;

    @ColumnWidth(14)
    @ExcelProperty("(email)邮箱")
    @Schema(description="邮箱")
    private String destEmail;

    @ColumnWidth(6)
    @ExcelProperty("(nation)国家")
    @Schema(description="国家")
    private String destCountry;

    @ColumnWidth(10)
    @ExcelProperty("(province)省")
    @Schema(description="省")
    private String destProvince;

    @ColumnWidth(10)
    @ExcelProperty("(city)市")
    @Schema(description="市")
    private String destCity;

    @ColumnWidth(30)
    @ExcelProperty("(Address 1)地址1")
    @Schema(description="地址1")
    private String destAddress1;

    @ColumnWidth(12)
    @ExcelProperty("(postcode)邮编")
    @Schema(description="邮编")
    private String destPostalCode;

    @ColumnWidth(8)
    @ExcelProperty("(Weight)重量KG")
    @Schema(description="重量KG")
    private BigDecimal pkgWeight;

    @ColumnWidth(8)
    @ExcelProperty("(long)长")
    @Schema(description="长(cm)")
    private BigDecimal pkgLength;

    @ColumnWidth(8)
    @ExcelProperty("(wide)宽")
    @Schema(description="宽")
    private BigDecimal pkgWidth;

    @ColumnWidth(8)
    @ExcelProperty("(high)高")
    @Schema(description="高")
    private BigDecimal pkgHeight;

    @ColumnWidth(20)
    @ExcelProperty("(Order creation time)订单创建时间")
    @Schema(description="订单创建时间")
    private Date addTime;

    @ColumnWidth(15)
    @ExcelProperty("(lot)批次")
    @Schema(description="批次")
    private String batchNo;

    @ColumnWidth(20)
    @ExcelProperty("eta")
    @Schema(description="预计到达")
    private Date eta;

    @ColumnWidth(20)
    @ExcelProperty("etd")
    @Schema(description="预计送出")
    private Date etd;

    @ColumnWidth(8)
    @ExcelProperty("ExpressType")
    @Schema(description="快递类型")
    private String expressType;

    @ColumnWidth(8)
    @ExcelProperty("(Road area code)路区号")
    @Schema(description="路区号")
    private String routeNo;

    @ColumnWidth(8)
    @ExcelProperty("(Merchant ID)商家ID")
    @Schema(description="商家ID")
    private Integer merchantId;

    @ColumnWidth(8)
    @ExcelProperty("(Declared price)申报价格")
    @Schema(description="申报价格")
    private BigDecimal pkgValue;

    @ColumnWidth(8)
    @ExcelProperty("Transport Type")
    @Schema(description="Transport Type")
    private String transportType;

    @ColumnWidth(8)
    @ExcelProperty("线路号")
    @Schema(description="pickNo")
    private String pickNo;

    @ColumnWidth(8)
    @ExcelProperty("物品描述")
    @Schema(description="goodsDesc")
    private String goodsDesc;

}
