package com.jygjexp.jynx.basic.back.api;


import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.replacement.ups.service.UpsService;
import com.jygjexp.jynx.basic.back.service.CityService;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.IOException;
import java.math.BigDecimal;

@RestController
@RequiredArgsConstructor
@RequestMapping("/zt/api")
public class OrderToNb {
    private final RemoteTmsService remoteTmsService;


    @Operation(summary = "支付接口调试", description = "支付接口调试")
    @PostMapping("/order/price")
    @Inner(value = false)
    public R getPrice(BigDecimal money) {
        remoteTmsService.pay(money, "WX_NATIVE", 1L);
        return R.ok();
    }

}

