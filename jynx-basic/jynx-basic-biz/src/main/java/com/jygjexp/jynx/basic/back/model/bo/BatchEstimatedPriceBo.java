package com.jygjexp.jynx.basic.back.model.bo;


import lombok.Data;

import java.util.List;

/**
 * 批量计算价格请求参数(询价接口)
 */
@Data
public class BatchEstimatedPriceBo {

    /**
     * 发货人邮编
     * 示例：L6Y 5X5
     */
    private String shipperPostalCode;


    /**
     * 收货人邮编
     * 示例：V1M 2C9
     */
    private String destPostalCode;

    /**
     * 批量计算价格参数
     */
    private List<PriceParaBo> priceParas;



}
