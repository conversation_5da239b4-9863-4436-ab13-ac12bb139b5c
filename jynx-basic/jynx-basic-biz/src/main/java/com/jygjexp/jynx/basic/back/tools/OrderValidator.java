package com.jygjexp.jynx.basic.back.tools;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jygjexp.jynx.basic.back.entity.SubOrderEntity;
import com.jygjexp.jynx.basic.back.model.bo.OrderItem;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OrderValidator {

    // 定义常量，避免硬编码
    private static final BigDecimal MIN_VALUE = BigDecimal.ZERO;
    private static final BigDecimal MAX_VALUE = new BigDecimal("10000.000");
    private static final BigDecimal WEIGHT_MAX_VALUE = new BigDecimal("68");
    private static final int DECIMAL_SCALE = 3;

    /**
     * 校验订单列表参数（静态方法）
     */
    public static String[] validateOrderList(List<SubOrderEntity> apiOrderVolumeList,List<OrderItem> apiOrderItemList) {
        String[] message1 = validateOrderForSubOrderEntity(apiOrderVolumeList);
        String[] message2 = validateOrderForOrderItem(apiOrderItemList);
        return OrderTools.copyArrayToEnd(message1, message2);
       // String[] message3 = LineValidator.validateLineData(apiOrderVolumeList);
      // return OrderTools.copyArrayToEnd(OrderTools.copyArrayToEnd(message1, message2),message3);
    }



    /**
     * 校验订单参数（静态方法）
     */
    public static String[] validateOrderForSubOrderEntity(List<SubOrderEntity> apiOrderVolumeList) {
        if (apiOrderVolumeList == null || apiOrderVolumeList.isEmpty()) {
            throw new IllegalArgumentException("apiOrderVolumeList不能为空！");
        }
        return apiOrderVolumeList.stream()
                .map(entity -> validateSubOrderEntity(entity, apiOrderVolumeList.indexOf(entity) + 1))
                .filter(msg -> !msg.isEmpty())
                .toArray(String[]::new);
    }

    /**
     * 校验订单参数（静态方法）
     */
    public static String[] validateOrderForOrderItem(List<OrderItem> apiOrderItemList) {
        if (apiOrderItemList == null || apiOrderItemList.isEmpty()) {
            throw new IllegalArgumentException("apiOrderItemList不能为空！");
        }
        return apiOrderItemList.stream()
                .map(entity -> validateOrderForOrderItem(entity, apiOrderItemList.indexOf(entity) + 1))
                .filter(msg -> !msg.isEmpty())
                .toArray(String[]::new);
    }

    /**
     * 校验 SubOrderEntity
     */
    private static String validateSubOrderEntity(SubOrderEntity entity, int index) {
        StringBuilder errorMsg = new StringBuilder();

        validateBigDecimalField(entity.getLength(), MIN_VALUE, MAX_VALUE, DECIMAL_SCALE,
                "长度", index, "apiOrderVolumeList", errorMsg);
        validateBigDecimalField(entity.getWidth(), MIN_VALUE, MAX_VALUE, DECIMAL_SCALE,
                "宽度", index, "apiOrderVolumeList", errorMsg);
        validateBigDecimalField(entity.getHeight(), MIN_VALUE, MAX_VALUE, DECIMAL_SCALE,
                "高度", index, "apiOrderVolumeList", errorMsg);
        validateBigDecimalField(entity.getWeight(), MIN_VALUE, WEIGHT_MAX_VALUE, DECIMAL_SCALE,
                "重量", index, "apiOrderVolumeList", errorMsg);


        //箱号长度如果不为空，长度应该在5-50之间
        if (StringUtils.isNotBlank(entity.getBagNum())) {
            int length = entity.getBagNum().length();
            if (length < 5 || length > 50) {
                errorMsg.append("第" + index + " apiOrderVolumeList箱号长度应在5到50之间！");
            }
        }

        if (StringUtils.isNotBlank(entity.getCargoDescription())) {
            int length = entity.getCargoDescription().length();
            if (length > 255) {
                errorMsg.append("第" + index + " apiOrderVolumeList货物描述长度不能大于255！");
            }
        }

//        if (entity.getQuantity() == null || entity.getQuantity() <= 0) {
//            errorMsg.append("第" + index + " apiOrderVolumeList货物数量不能为空且必须大于0！");
//        }

        return errorMsg.toString();
    }

    /**
     * 校验 OrderItem
     */
    private static String validateOrderForOrderItem(OrderItem entity, int index) {
        StringBuilder errorMsg = new StringBuilder();

        validateRequiredStringField(entity.getEname(), 100,
                "英文名", index, "apiOrderItemList", errorMsg);
        validateRequiredStringField(entity.getSku(), 255,
                "Sku", index, "apiOrderItemList", errorMsg);

        validateBigDecimalField(entity.getPrice(), MIN_VALUE, MAX_VALUE, DECIMAL_SCALE,
                "商品单价", index, "apiOrderItemList", errorMsg);
        validateBigDecimalField(entity.getWeight(), MIN_VALUE, MAX_VALUE, DECIMAL_SCALE,
                "商品重量", index, "apiOrderItemList", errorMsg);

        validateStringField(entity.getCname(), 255,
                "中文名长度不能大于255！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getUnitCode(), 20,
                "申报单位单位长度不能大于20！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getHsCode(), 50,
                "海关编码长度不能大于50！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getImageUrl(), 150,
                "图片地址长度不能大于150！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getBrand(), 50,
                "品牌长度不能大于50！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getSpecifications(), 50,
                "规格型号长度不能大于50！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getMaterial(), 50,
                "材质长度不能大于50！", index, "apiOrderItemList", errorMsg);
        validateStringField(entity.getUsed(), 150,
                "用途长度不能大于150！", index, "apiOrderItemList", errorMsg);

        return errorMsg.toString();
    }

    /**
     * 校验 BigDecimal 值
     */
    private static void validateBigDecimalField(BigDecimal value, BigDecimal min, BigDecimal max, int scale,
                                                String fieldName, int index, String listName, StringBuilder errorMsg) {
        if (value == null) {
            errorMsg.append(String.format("第%d %s%s不能为空！", index, listName, fieldName));
        } else if (!isValidBigDecimal(value, min, max, scale)) {
            errorMsg.append(String.format("第%d %s%s应大于%s，小于等于%s，范围内最多保留%d位小数！",
                    index, listName, fieldName, min, max, scale));
        }
    }

    /**
     * 校验非必填字符串长度
     */
    private static void validateStringField(String value, int maxLength,
                                            String errorMsgTemplate, int index, String listName, StringBuilder errorMsg) {
        if (StringUtils.isNotBlank(value) && value.length() > maxLength) {
            errorMsg.append(String.format("第%d %s%s", index, listName, errorMsgTemplate));
        }
    }

    /**
     * 校验必填字符串长度
     */
    private static void validateRequiredStringField(String value, int maxLength,
                                                    String fieldName, int index, String listName, StringBuilder errorMsg) {
        if (StringUtils.isBlank(value)) {
            errorMsg.append(String.format("第%d %s%s不能为空！", index, listName, fieldName));
        } else if (value.length() > maxLength) {
            errorMsg.append(String.format("第%d %s%s长度不能大于%d！", index, listName, fieldName, maxLength));
        }
    }

    /**
     * 校验 BigDecimal 范围和精度
     */
    private static boolean isValidBigDecimal(BigDecimal value, BigDecimal min, BigDecimal max, int scale) {
        return value != null
                && value.compareTo(min) > 0
                && value.compareTo(max) <= 0
                && value.scale() <= scale;
    }
}
