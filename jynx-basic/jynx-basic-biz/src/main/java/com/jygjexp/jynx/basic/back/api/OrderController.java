package com.jygjexp.jynx.basic.back.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.entity.PushEntity;
import com.jygjexp.jynx.basic.back.entity.SheinCodeEntity;
import com.jygjexp.jynx.basic.back.entity.TmsCustomerEntity;
import com.jygjexp.jynx.basic.back.model.bo.*;
import com.jygjexp.jynx.basic.back.model.vo.*;
import com.jygjexp.jynx.basic.back.replacement.ups.service.UpsService;
import com.jygjexp.jynx.basic.back.request.PaymentCallbackRequest;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.basic.back.service.PushService;
import com.jygjexp.jynx.basic.back.tools.EstimatedPriceBoValidator;
import com.jygjexp.jynx.basic.back.tools.HttpKit;
import com.jygjexp.jynx.basic.back.tools.ThrottleLock;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;


/**
 * 订单处理接口类
 *
 * <AUTHOR>
 * @date 2024-10-11 16:51:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zt/api")
@Tag(description = "SheinCode", name = "单号处理")
public class OrderController {

    private final OrderService orderService;
    private final RemoteTmsService remoteTmsService;
    private final UpsService upsService;

    //TMS排队（暂时措施)
    private final ThrottleLock throttleLockTMS = new ThrottleLock(500);
    //YSP排队（暂时措施)
    private final ThrottleLock throttleLockYSP = new ThrottleLock(1000);

    @Inner(value = false)
    @Operation(summary = "创建希音单号", description = "创建单号")
    @PostMapping("/order/create")
    public JSONObject create(@ModelAttribute @Valid CreateSheinOrderBo bo) {
        OldResult oldResult = orderService.checkOrder(bo);
        if (oldResult.getCode().equals("ok")) {
            // 走退件流程
            return orderService.handleOrder(bo);
        }
        JSONObject jo = new JSONObject();
        jo.put("code", oldResult.getCode());
        jo.put("errmsg", oldResult.getErrmsg());
        return jo;
    }

    @Inner(value = false)
    @Operation(summary = "创建优时派单号", description = "创建优时派单号")
    @PostMapping("/return/yspCreate")
    public JSONObject yspCreate(@ModelAttribute @Valid YspBo bo) {
        return throttleLockYSP.call(() -> orderService.handleOrder(bo));
    }

    @Inner(value = false)
    @Operation(summary = "创建顺丰单号", description = "创建顺丰单号")
    @PostMapping("/shipment/create")
    public JSONObject createSfOrder(@ModelAttribute CreateSfOrderBo bo) {
        return orderService.handleOrder(bo);
    }

    @Inner(value = false)
    @Operation(summary = "优时派查询轨迹", description = "优时派查询轨迹")
    @PostMapping("/return/tracking")
    public JSONObject tracking(@RequestParam String nbLogiNo) {
        return orderService.trackYsp(nbLogiNo);
    }



    @Inner(value = false)
    @Operation(summary = "小包查询轨迹", description = "小包查询轨迹")
    @PostMapping("/order/route")
    public OldResult route(@RequestParam String logiNo) {
        SheinCodeEntity sheinCode = orderService.getOrderByOrderNo(logiNo);
        if (sheinCode == null) {
            return OldResult.fail("40063", "订单不存在");
        }
        if ("NR2024156395".equals(logiNo)){
            System.out.println("经过了小包查询轨迹接口...");
        }
        return orderService.sheinCodeRoute(sheinCode, true);
    }

    @Inner(value = false)
    @Operation(summary = "小包推送订单状态", description = "小包推送订单状态")
    @PostMapping("/order/receive")
    public JSONObject receive(@ModelAttribute OrderStatusBo statusBo) {
        return orderService.receiveOrderStatus(statusBo);
    }


    @Inner(value = false)
    @Operation(summary = "小包获取订单", description = "小包获取订单")
    @PostMapping("/order/getOrderData")
    public Object getOrderData(String startTime, String endTime, String type) {
        return orderService.getOrderData(startTime, endTime, type);
    }


    @Inner(value = false)
    @Operation(summary = "PUD订单信息推送", description = "PUD订单信息推送")
    @PostMapping("/order/pushPudOrder")
    public Object pushPudOrder(@RequestBody PudOrderBo pudOrderBo) {
        return orderService.pushPudOrderData(pudOrderBo);
    }


    /**
     * 删除订单
     */
    @Inner(value = false)
    @PostMapping("/order/delete")
    public JSONObject delete(@RequestParam String logiNo, @RequestParam String token) {
        return orderService.deleteOrder(logiNo, token);
    }

    //NB订单推送接口(新)
    @Inner(value = false)
    @Operation(summary = "NB订单推送接口(新)", description = "NB订单推送接口(新)")
    @PostMapping("/order/push")
    public R pushOrderToNb(@RequestBody @Valid ApiOrder order,
                           @RequestHeader("apiKey") String apiKey,
                           @RequestHeader("timestamp") String timestamp) {
        return throttleLockTMS.call(() -> orderService.pushOrderToNb(order, apiKey, timestamp));
    }

    //NB订单拦截接口
    @Inner(value = false)
    @Operation(summary = "NB订单拦截接口", description = "NB订单拦截接口")
    @PostMapping("/order/block")
    public R blockOrder(@RequestBody BlockOrderRequestBo blockOrderRequestBo,
                        @RequestHeader("apiKey") String apiKey) {
        return orderService.blockOrder(blockOrderRequestBo.getTrackingNo(), apiKey);
    }


    //NB订单删除接口(新)
    @Inner(value = false)
    @Operation(summary = "NB订单删除接口", description = "NB订单删除接口")
    @PostMapping("/order/del")
    public R deleteOrder(@RequestBody BlockOrderRequestBo blockOrderRequestBo,
                         @RequestHeader("apiKey") String apiKey) {
        return orderService.delOrder(blockOrderRequestBo.getTrackingNo(), apiKey);
    }


    //NB订单查询轨迹接口(新)
    @Inner(value = false)
    @Operation(summary = "NB订单查询轨迹接口", description = "NB订单查询轨迹接口")
    @PostMapping("/order/track")
    public R trackOrder(@RequestBody BlockOrderRequestBo blockOrderRequestBo,
                        @RequestHeader("apiKey") String apiKey) {
        return orderService.trackOrder(blockOrderRequestBo.getTrackingNo(), apiKey);
    }


    //NB换单接口
    @Inner(value = false)
    @Operation(summary = "NB换单接口", description = "NB换单接口")
    @PostMapping("/order/exchangeOrder")
    public R exchangeOrder(@RequestBody  ExchangeVo exchangeVo,
                        @RequestHeader("apiKey") String apiKey) {
        return orderService.exchangeOrder(exchangeVo, apiKey);
    }


    //接收轨迹推送接口（模拟客户）
    @Inner(value = false)
    @Operation(summary = "接收轨迹推送接口", description = "接收轨迹推送接口")
    @PostMapping("/order/receiveTrack")
    public String receiveTrack(@RequestBody BlockOrderRequestBo blockOrderRequestBo) {
        return "OK";
    }


    //轨迹推送接口
    @Inner(value = false)
    @Operation(summary = "轨迹推送接口", description = "轨迹推送接口")
    @PostMapping("/order/trackPush")
    public R trackPush() {
        BlockOrderRequestBo blockOrderRequestBo = new BlockOrderRequestBo();
        blockOrderRequestBo.setTrackingNo("KHDH20250319047424");
        String post = HttpKit.ping("http://************:6063/zt/api/order/receiveTrack", JSON.toJSONString(blockOrderRequestBo));
        return R.ok(post);

    }

    //获取全部有效邮编
    @Inner(value = false)
    @Operation(summary = "获取全部有效邮编", description = "获取全部有效邮编")
    @GetMapping("/order/getAllValidZip")
    public R getAllValidZip() {
        return R.ok(remoteTmsService.getZipCode());
    }


    //获取中大件派送PUD信息
    @Inner(value = false)
    @Operation(summary = "获取中大件派送POD信息", description = "获取中大件派送POD信息")
    @PostMapping("/order/getPud")
    public R getPud(@RequestBody BlockOrderRequestBo requestBo, @RequestHeader("apiKey") String apiKey) {
        if (StringUtils.isBlank(requestBo.getTrackingNo())) {
            return R.failed("trackingNo不能为空");
        }
        if (StringUtils.isBlank(apiKey)) {
            return R.failed("apiKey不能为空");
        }
        return orderService.getPud(requestBo.getTrackingNo(), apiKey);
    }


    /**
     * 删除订单接口(USPEED专用，兼容UPS和VK)
     */
    @Inner(value = false)
    @PostMapping("/order/cancel")
    public JSONObject cancel(@RequestParam String logiNo,  @RequestHeader("token") String token) {
        return orderService.cancel(logiNo, token);
    }


    @Inner(value = false)
    @Operation(summary = "接收支付回调", description = "接收支付中心支付结果回调")
    @PostMapping(value = "/payment/callback", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String handlePaymentCallback(HttpServletRequest request) {
        PaymentCallbackRequest callbackRequest = new PaymentCallbackRequest();
        callbackRequest.setPayOrderId(request.getParameter("payOrderId"));
        callbackRequest.setMchId(request.getParameter("mchId"));
        callbackRequest.setMchOrderNo(request.getParameter("mchOrderNo"));
        callbackRequest.setChannelId(request.getParameter("channelId"));
        callbackRequest.setCurrency(request.getParameter("currency"));
        callbackRequest.setAmount(request.getParameter("amount") == null ? 0 : Integer.parseInt(request.getParameter("amount")));
        callbackRequest.setStatus(request.getParameter("status") == null ? 0 : Integer.parseInt(request.getParameter("status")));
        callbackRequest.setClientIp(request.getParameter("clientIp"));
        callbackRequest.setDevice(request.getParameter("device"));
        callbackRequest.setSubject(request.getParameter("subject"));
        callbackRequest.setBody(request.getParameter("body"));
        callbackRequest.setChannelOrderNo(request.getParameter("channelOrderNo"));
        callbackRequest.setParam1(request.getParameter("param1"));
        callbackRequest.setParam2(request.getParameter("param2"));
        callbackRequest.setPaySuccTime(request.getParameter("paySuccTime") == null ? 0L : Long.parseLong(request.getParameter("paySuccTime")));
        callbackRequest.setBackType(request.getParameter("backType") == null ? 0 : Integer.parseInt(request.getParameter("backType")));
        callbackRequest.setSign(request.getParameter("sign"));

        if (callbackRequest.getStatus() != 2) {
            return "fail";
        }

        System.out.println(callbackRequest);
        orderService.payResultHandel(callbackRequest);
        return "success";
    }



    //中大件询价接口
    @Inner(value = false)
    @Operation(summary = "中大件询价", description = "中大件询价")
    @PostMapping("/order/estimatedPrice")
    public R getPrice(@RequestBody EstimatedPriceBo bo,@RequestHeader("apiKey") String apiKey) {
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        if (tmsCustomerByToken == null) {
            return R.failed("apiKey无效");
        }
        String message = EstimatedPriceBoValidator.validate(bo);
        if (StringUtils.isNotBlank(message)) {
            return R.failed(message);
        }
        TmsOrderPriceCalculationVo order = new TmsOrderPriceCalculationVo();
        BeanUtils.copyProperties(bo, order);
        order.setCustomerId(bo.getCustomerId()>10000?remoteTmsService.getTmsCustomerByUserId(bo.getCustomerId()).getId():bo.getCustomerId());
        PriceCalculationRequestVo para = new PriceCalculationRequestVo();
        para.setOrders(Collections.singletonList(order));
        PriceCalculationResultVo priceCalculationResultVo = remoteTmsService.calculatePrice(para);
        PriceCalculationDetailVo priceCalculationDetailVo = priceCalculationResultVo.getDetails().get(0);
        if (priceCalculationResultVo.getSuccessCount()>0){
            PriceDetailVo priceDetailVo = new PriceDetailVo();
            priceDetailVo.setBasePrice(priceCalculationDetailVo.getBasePrice());
            priceDetailVo.setSurcharge(BigDecimal.ZERO);
            priceDetailVo.setFinalPrice(priceDetailVo.getBasePrice().add(priceDetailVo.getSurcharge()));
            return R.ok(priceDetailVo);
        }else {
            return R.failed(priceCalculationDetailVo.getErrorMessage());
        }
    }
}
