package com.jygjexp.jynx.basic.back.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户充值支付订单表
 *
 * <AUTHOR>
 * @date 2025-07-25 17:23:22
 */
@Data
@Builder
@TableName("tms_pay_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户充值订单表")
@RequiredArgsConstructor
@AllArgsConstructor
public class TmsPayOrderEntity extends Model<TmsPayOrderEntity> {

	public TmsPayOrderEntity(String orderNo, Long userId, BigDecimal amount, String payType, String payOrderId) {
		this.orderNo = orderNo;
		this.userId = userId;
		this.amount = amount;
		this.payType = payType;
		this.payOrderId = payOrderId;
	}


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 本地支付订单号（如：NB12345678）
	*/
    @Schema(description="本地支付订单号（如：NB12345678）")
    private String orderNo;

	/**
	* 用户ID
	*/
    @Schema(description="用户ID")
    private Long userId;

	/**
	* 充值金额（单位：分）
	*/
    @Schema(description="充值金额（单位：分）")
    private BigDecimal amount;

	/**
	* 支付渠道（如：alipay、wechat、unionpay）
	*/
    @Schema(description="支付渠道（如：alipay、wechat、unionpay）")
    private String payType;

	/**
	* 第三方平台支付订单号
	*/
    @Schema(description="第三方平台支付订单号")
    private String payOrderId;

	/**
	* 支付状态（0:待支付, 1:已支付, 2:支付失败, 3:已关闭）
	*/
    @Schema(description="支付状态（0:待支付, 1:已支付, 2:支付失败, 3:已关闭）")
    private Integer status;

	/**
	* 第三方支付成功的时间（回调时间）
	*/
    @Schema(description="第三方支付成功的时间（回调时间）")
    private LocalDateTime notifyTime;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
}