package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.entity.SysDept;
import com.jygjexp.jynx.admin.api.entity.SysPost;
import com.jygjexp.jynx.admin.api.entity.SysRole;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteDeptService;
import com.jygjexp.jynx.admin.api.feign.RemotePostService;
import com.jygjexp.jynx.admin.api.feign.RemoteRoleService;
import com.jygjexp.jynx.common.core.constant.CommonConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.dto.TmsStoreEmployeeDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreEmployeeEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreUserEntity;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsStoreEmployeeMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreEmployeeService;
import com.jygjexp.jynx.tms.service.TmsStoreUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店员工表
 *
 * <AUTHOR>
 * @date 2025-07-11 14:00:19
 */
@Service
@RequiredArgsConstructor
public class TmsStoreEmployeeServiceImpl extends ServiceImpl<TmsStoreEmployeeMapper, TmsStoreEmployeeEntity> implements TmsStoreEmployeeService {
    private final TmsStoreUserService tmsStoreUserService;
    private final RemoteTmsUpmsService remoteTmsUpmsService;
    private final RemoteDeptService remoteDeptService;
    private final RemoteRoleService remoteRoleService;
    private final RemotePostService remotePostService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveEmployee(TmsStoreEmployeeDTO tmsStoreEmployee) {
        // 判断用户名和手机号是否已存在
        if (remoteTmsUpmsService.getCustomerUserByPhoneOrUsername(tmsStoreEmployee.getEmployeePhone(), tmsStoreEmployee.getEmployeeName()).getData()) {
            return LocalizedR.failed("tms.store.name.phone.already.exists", "");
        }
        // 保存用户信息
        SysUser sysUser = saveUserInfo(tmsStoreEmployee.getEmployeeName(), tmsStoreEmployee.getEmployeePhone());
        if (sysUser == null){
            return LocalizedR.failed("tms.store.employee.create.fail", tmsStoreEmployee.getEmployeeName());
        }
        Long storeId = null;
        if (tmsStoreEmployee.getStoreId() == null){
            // 由门店管理员创建员工，获取对应的门店ID
            Long userId = SecurityUtils.getUser().getId();
            TmsStoreUserEntity userServiceOne = tmsStoreUserService.getOne(Wrappers.<TmsStoreUserEntity>lambdaQuery().eq(TmsStoreUserEntity::getUserId, userId));
            storeId = userServiceOne.getStoreId();
        }else {
            storeId = tmsStoreEmployee.getStoreId();
        }
        try {
            // 保存员工信息
            TmsStoreEmployeeEntity tmsStoreEmployeeEntity = new TmsStoreEmployeeEntity();
            BeanUtils.copyProperties(tmsStoreEmployee, tmsStoreEmployeeEntity);
            tmsStoreEmployeeEntity.setUserId(sysUser.getUserId());
            baseMapper.insert(tmsStoreEmployeeEntity);
            // 设置员工和门店的关联
            TmsStoreUserEntity tmsStoreUserEntity = new TmsStoreUserEntity();
            tmsStoreUserEntity.setUserId(sysUser.getUserId());
            tmsStoreUserEntity.setStoreId(storeId);
            tmsStoreUserService.save(tmsStoreUserEntity);
            return R.ok();
        }catch (Exception e){
            remoteTmsUpmsService.userDelNoToken(new Long[]{sysUser.getUserId()});
            return R.failed();
        }
    }

    @Override
    public R updateEmployee(TmsStoreEmployeeEntity tmsStoreEmployee) {
        this.updateById(tmsStoreEmployee);
        // todo 同步修改用户信息
        R<UserInfo> info = remoteTmsUpmsService.info(tmsStoreEmployee.getEmployeeName());
        if (info.getData() == null) {
            return R.failed("Account does not exist");
        }
        // 修改系统后台用户账号
        Long userId = info.getData().getSysUser().getUserId();
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(userId);
        userDTO.setUsername(tmsStoreEmployee.getEmployeeName());
        userDTO.setPhone(tmsStoreEmployee.getEmployeePhone());
        remoteTmsUpmsService.updateUser(userDTO);
        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R removeBatchEmployee(Long[] ids) {
        // 查询对应的员工信息
        List<TmsStoreEmployeeEntity> employeeList = this.listByIds(Arrays.asList(ids));
        List<Long> userIds = employeeList.stream().map(TmsStoreEmployeeEntity::getUserId).collect(Collectors.toList());
        // 删除员工数据
        this.removeBatchByIds(Arrays.asList(ids));
        // 删除用户数据
        remoteTmsUpmsService.userDelNoToken(userIds.toArray(new Long[0]));
        return R.ok();
    }

    @Override
    @Transactional(rollbackFor =  Exception.class)
    public R updateStatus(TmsStoreEmployeeEntity tmsStore) {
        // 更新员工状态
        boolean updated = this.updateById(tmsStore);
        if (updated) {
            // 通过员工ID获取对应的用户ID
            TmsStoreEmployeeEntity employeeEntity = this.getOne(Wrappers.<TmsStoreEmployeeEntity>lambdaQuery().eq(TmsStoreEmployeeEntity::getId, tmsStore.getId()));
            Long userId = employeeEntity.getUserId();
            if (userId != null){
                // 更新用户状态
                if (tmsStore.getStatus().equals(1)){
                    // 启用用户状态
                    remoteTmsUpmsService.userEnables(Arrays.asList(userId));
                }else if (tmsStore.getStatus().equals(0)){
                    // 禁用用户
                    remoteTmsUpmsService.userLocks(Arrays.asList(userId));
                }
            }
            return R.ok();
        }
        return R.failed();
    }

    @Override
    public IPage<TmsStoreEmployeeEntity> getPage(Page page, TmsStoreEmployeeEntity tmsStoreEmployee) {
        // 获取当前用户
        Long userId = SecurityUtils.getUser().getId();
        // 获取当前用户归属门店ID
        TmsStoreUserEntity storeUser = tmsStoreUserService.getOne(Wrappers.<TmsStoreUserEntity>lambdaQuery()
                .eq(TmsStoreUserEntity::getUserId, userId));
        Long storeId = storeUser != null ? storeUser.getStoreId() : null;
        if (storeId == null) {
            // 当前用户没有门店
            return new Page<>();
        }

        // 查该门店下的所有employeeId
        List<Long> employeeIds = tmsStoreUserService.listObjs(
                Wrappers.<TmsStoreUserEntity>lambdaQuery()
                        .eq(TmsStoreUserEntity::getStoreId, storeId)
                        .select(TmsStoreUserEntity::getUserId),
                o -> (Long)o
        );
        if (employeeIds == null || employeeIds.isEmpty()) {
            return new Page<>();
        }

        // 构造分页条件
        LambdaQueryWrapper<TmsStoreEmployeeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(TmsStoreEmployeeEntity::getUserId, employeeIds)
                .like(StrUtil.isNotBlank(tmsStoreEmployee.getEmployeeName()), TmsStoreEmployeeEntity::getEmployeeName, tmsStoreEmployee.getEmployeeName())
                .like(StrUtil.isNotBlank(tmsStoreEmployee.getEmployeePhone()), TmsStoreEmployeeEntity::getEmployeePhone, tmsStoreEmployee.getEmployeePhone())
                .eq(tmsStoreEmployee.getStatus() != null, TmsStoreEmployeeEntity::getStatus, tmsStoreEmployee.getStatus())
                .orderByAsc(TmsStoreEmployeeEntity::getEmployeeType)
                .orderByDesc(TmsStoreEmployeeEntity::getCreateTime);

        return this.baseMapper.selectPage(page, wrapper);
    }

    private SysUser saveUserInfo(String contactName, String contactPhone) {
        UserDTO sysUser = new UserDTO();
        // 设置部门信息
        SysDept dept = remoteDeptService.getDeptIdByName(CommonConstants.STORE_MANAGER_DEPT_NAME).getData();
        if (dept != null){
            sysUser.setDeptId(dept.getDeptId());
        }
        // 获取角色信息
        List<SysRole> sysRoles = remoteRoleService.getAllRole().getData();
        SysRole sysRole = Optional.ofNullable(sysRoles)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> CommonConstants.STORE_EMPLOYEE_ROLE_CODE.equals(item.getRoleCode()))
                .findFirst()
                .orElse(null);
        if (sysRole != null){
            sysUser.setRole(Arrays.asList(sysRole.getRoleId()));
        }
        // 获取岗位信息
        List<SysPost> sysPosts = remotePostService.getAllPost().getData();
        SysPost sysPost = Optional.ofNullable(sysPosts)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> CommonConstants.STORE_MANAGER_POST_CODE.equals(item.getPostCode()))
                .findFirst()
                .orElse(null);
        if (sysPost != null){
            sysUser.setPost(Arrays.asList(sysPost.getPostId()));
        }
        // 设置用户基本信息
        sysUser.setUsername(contactName);
        sysUser.setName(contactName);
        sysUser.setPhone(contactPhone);
        sysUser.setPassword("123456");
        sysUser.setLockFlag("0");
        remoteTmsUpmsService.addUserNoToken(sysUser);

        // 获取新增加的用户
        return remoteTmsUpmsService.getCustomerUserByPhone(contactPhone).getData();
    }
}