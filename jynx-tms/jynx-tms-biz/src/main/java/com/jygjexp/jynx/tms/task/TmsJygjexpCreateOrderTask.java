package com.jygjexp.jynx.tms.task;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsPickupEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * 商家客户端导入订单推送小包定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TmsJygjexpCreateOrderTask {

    private final TmsCustomerOrderService customerOrderService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final static Integer ISPUSH=1;   // 表示推送UNI

    @SneakyThrows
    @XxlJob("tmsJygjexpCreateOrderTask")
    public void Job() {
        log.info("定时任务于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        try {

            MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.selectAll(TmsCustomerOrderEntity.class)
                    .eq(TmsCustomerOrderEntity::getSubFlag, false)
                    .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                    .isNull(TmsCustomerOrderEntity::getPushLabel)
                    .gt(TmsCustomerOrderEntity::getCreateTime, DateUtil.offsetDay(new Date(), -1))
                    .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                    //.le(TmsCustomerOrderEntity::getTotalWeight, 30)
                    .lt(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode())
                    .like(TmsCustomerEntity::getIsPush, ISPUSH);  // .apply("FIND_IN_SET({0}, t.is_push)", ISPUSH)
            List<TmsCustomerOrderEntity> list = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class,wrapper);

            //测试数据
/*            LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, false);
            queryWrapper.eq(TmsCustomerOrderEntity::getDelFlag, false);
            queryWrapper.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, "N2501D0000573","N2501D0000574");
            //queryWrapper.le(TmsCustomerOrderEntity::getTotalWeight, 30);
            List<TmsCustomerOrderEntity> list = customerOrderService.list(queryWrapper);*/
            if (CollUtil.isNotEmpty(list)){
                // 推送订单
                customerOrderService.syncCreateOrder(list);
            }
            //合并一票多久PDF
            customerOrderService.getOrderByJobCondition();
            XxlJobHelper.handleSuccess(); // 设置任务结果
            XxlJobHelper.log("定时任务：【商家客户端导入订单推送小包】执行结束，时间: {}", LocalDateTime.now());
        } catch (Exception e) {
            log.error("【商家客户端导入订单推送小包】定时任务执行失败：", e);
            XxlJobHelper.log("任务失败，原因：{}", e.getMessage());
            XxlJobHelper.handleFail();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "请求过程中发生错误：" + e.getMessage(), e);
        }
    }

}