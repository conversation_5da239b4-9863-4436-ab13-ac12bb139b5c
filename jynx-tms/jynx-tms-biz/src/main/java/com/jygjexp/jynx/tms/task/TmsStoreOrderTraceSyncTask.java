package com.jygjexp.jynx.tms.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderTraceEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderTraceService;
import com.jygjexp.jynx.tms.utils.PostBusinessUtils;
import com.jygjexp.jynx.tms.vo.ResponseResult;
import com.jygjexp.jynx.tms.vo.api.PackageOrderTraceDataItem;
import com.jygjexp.jynx.tms.vo.api.PackageOrderTraceItem;
import com.jygjexp.jynx.tms.vo.api.PackageOrderTraceRoot;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class TmsStoreOrderTraceSyncTask {
    private static final Logger logger = LoggerFactory.getLogger(TmsStoreOrderTraceSyncTask.class);

    private final TmsStoreOrderService storeOrderService;
    private final TmsStoreOrderTraceService storeOrderTraceService;
    private final TmsOrderTrackService orderTrackService;

    // NB状态映射配置
    private static final Map<String, OrderStatusMapping> NB_ORDER_STATUS_MAPPING = new HashMap<>();

    // Package 小包系统状态映射配置
    private static final Map<String, OrderStatusMapping> PACKAGE_ORDER_STATUS_MAPPING = new HashMap<>();

    static {
        // 待运输 -> 服务商已经取货
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.AWAITING_TRANSPORTATION.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.PROVIDER_PICKUP.getValue(), StoreEnums.StoreOrder.OrderStatus.PRINT_SCRIPT.getEName()));

        // 运输中相关状态 -> 运输中
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.IN_TRANSIT.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue(), StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getEName()));
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.WAREHOUSE_RECEIVE.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue(), StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getEName()));
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.AWAITING_DELIVERY.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue(), StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getEName()));

        // 已完成 -> 已完成
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.COMPLETED.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.COMPLETED.getValue(), StoreEnums.StoreOrder.OrderStatus.COMPLETED.getEName()));

        // 异常状态 -> 订单异常
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.FAILED_DELIVERY.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue(),
                                 StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getEName()));
        NB_ORDER_STATUS_MAPPING.put(NewOrderStatus.RETURN_TO_SEND.getValue(),
            new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue(), StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getEName()));
    }

    static {
        // 末端提取(500) -> 服务商已经取货
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_500.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.PROVIDER_PICKUP.getValue(), StoreEnums.StoreOrder.OrderStatus.PRINT_SCRIPT.getEName()));

        // 末端转运(501) / 开始派送(510) /  到达待取(512) -> 运输中
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_501.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue(), StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getEName()));
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_510.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue(), StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getEName()));
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_512.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue(), StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getEName()));

        // 签收(520) -> 已完成
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_520.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.COMPLETED.getValue(), StoreEnums.StoreOrder.OrderStatus.COMPLETED.getEName()));


        // 派送失败(511) / 地址错误(535) / 退件到仓(542) / 包裹丢失(548) -> 已完成
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_511.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue(), StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getEName()));
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_535.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue(), StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getEName()));
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_542.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue(), StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getEName()));
        PACKAGE_ORDER_STATUS_MAPPING.put(StoreEnums.PackageSystem.PackageOrderStatus.PACKAGE_548.getValue(),
                new OrderStatusMapping(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue(), StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getEName()));
    }


    @SneakyThrows
    @XxlJob("tmsStoreOrderTraceSyncTask")
    public void executeTask() {
        try {
            LocalDateTime syncTime = getSyncTime();
            List<Integer> orderStatuses = getTargetOrderStatuses();

            List<TmsStoreOrderEntity> storeOrderEntities = storeOrderService.selectSubStoreOrderByPushTime(orderStatuses, syncTime);

            if (CollUtil.isEmpty(storeOrderEntities)) {
                logger.info("[快递订单][轨迹跟踪] 没有需要同步的订单数据, syncTime={}", syncTime);
                return;
            }
            logger.info("[快递订单][轨迹跟踪] 开始同步快递子单轨迹映射, syncTime={}, 订单数量={}", syncTime, storeOrderEntities.size());

            processStoreOrders(storeOrderEntities);

            logger.info("[快递订单][轨迹跟踪] 同步完成");

        } catch (Exception e) {
            logger.error("[快递订单][轨迹跟踪] 同步失败", e);
            throw e;
        }
    }

    /**
     * 获取同步时间
     */
    private LocalDateTime getSyncTime() {
        String jobParam = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(jobParam)) {
            return LocalDate.parse(jobParam.trim()).atStartOfDay();
        } else {
            return DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -3)).toLocalDateTime();
        }
    }

    /**
     * 获取目标订单状态列表
     */
    private List<Integer> getTargetOrderStatuses() {
        return Arrays.asList(
            StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue(),
            StoreEnums.StoreOrder.OrderStatus.PRINT_SCRIPT.getValue(),
            StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue()
        );
    }

    /**
     * 处理门店快递订单
     */
    private void processStoreOrders(List<TmsStoreOrderEntity> storeOrderEntities) {
        Map<String, List<TmsStoreOrderEntity>> storeOrderGroup = storeOrderEntities
                .stream()
                .collect(Collectors.groupingBy(TmsStoreOrderEntity::getMainEntrustedOrder));

        for (Map.Entry<String, List<TmsStoreOrderEntity>> entry : storeOrderGroup.entrySet()) {
            String mainEntrustedOrder = entry.getKey();
            List<TmsStoreOrderEntity> storeOrderEntityList = entry.getValue();
            if (CollUtil.isEmpty(storeOrderEntityList)) {
                continue;
            }
            // 处理每个子单
            for (TmsStoreOrderEntity storeOrder : storeOrderEntityList) {
                processSingleStoreOrder(storeOrder);
            }
            // 更新主单状态
            updateMainOrderStatus(mainEntrustedOrder, storeOrderEntityList);
        }
    }

    /**
     * 更新主单状态
     */
    private void updateMainOrderStatus(String mainEntrustedOrder, List<TmsStoreOrderEntity> storeOrderEntityList) {
        try {
            // 获取所有子单的状态
            Set<Integer> subOrderStatuses = storeOrderEntityList.stream()
                    .map(TmsStoreOrderEntity::getOrderStatus)
                    .collect(Collectors.toSet());

            Integer mainOrderStatus = determineMainOrderStatus(subOrderStatuses);

            if (mainOrderStatus != null) {
                storeOrderService.updateOrderStatusByEntrustedOrderNumber(mainEntrustedOrder, mainOrderStatus);
                if (logger.isInfoEnabled()) {
                    logger.info("[快递订单][轨迹跟踪] 主单状态更新: mainOrderNo={}, status={}, subOrderStatuses={}",
                            mainEntrustedOrder, mainOrderStatus, subOrderStatuses);
                }
            }
        } catch (Exception e) {
            logger.error("[快递订单][轨迹跟踪] 更新主单状态失败: mainOrderNo={}", mainEntrustedOrder, e);
        }
    }

    /**
     * 根据子单状态确定主单状态
     */
    private Integer determineMainOrderStatus(Set<Integer> subOrderStatuses) {
        // 如果存在异常状态，主单状态为异常
        if (subOrderStatuses.contains(StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue())) {
            return StoreEnums.StoreOrder.OrderStatus.EXCEPTION.getValue();
        }

        // 如果存在运输中状态，主单状态为运输中
        if (subOrderStatuses.contains(StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue())) {
            return StoreEnums.StoreOrder.OrderStatus.IN_TRANSIT.getValue();
        }

        // 如果所有子单都已完成，主单状态为已完成
        if (subOrderStatuses.stream().allMatch(status ->
                StoreEnums.StoreOrder.OrderStatus.COMPLETED.getValue().equals(status))) {
            return StoreEnums.StoreOrder.OrderStatus.COMPLETED.getValue();
        }

        // 如果所有子单都是服务商已取货状态，主单状态为服务商已取货
        if (subOrderStatuses.stream().allMatch(status ->
                StoreEnums.StoreOrder.OrderStatus.PROVIDER_PICKUP.getValue().equals(status))) {
            return StoreEnums.StoreOrder.OrderStatus.PROVIDER_PICKUP.getValue();
        }

        // 其他情况，保持当前状态不变
        return null;
    }

    /**
     * 处理单个门店订单轨迹
     */
    private void processSingleStoreOrder(TmsStoreOrderEntity storeOrder) {
        String externalOrderNumber = storeOrder.getExternalOrderNumber();
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        String mainEntrustedOrder = storeOrder.getMainEntrustedOrder();
        TmsStoreOrderTraceEntity traceMaxSyncTime = storeOrderTraceService.getTraceMaxSycTime(entrustedOrderNumber);
        if (StoreEnums.StoreOrder.ExternalType.NB.getValue().equals(storeOrder.getExternalType())) {
            if (null == traceMaxSyncTime) {
                // 首次同步，处理所有轨迹
                processAllOrderTracks(externalOrderNumber, entrustedOrderNumber, mainEntrustedOrder);
            } else {
                // 增量同步，只处理新轨迹
                processIncrementalOrderTracks(externalOrderNumber, entrustedOrderNumber, mainEntrustedOrder, traceMaxSyncTime.getSyncTime());
            }
        }else if(StoreEnums.StoreOrder.ExternalType.PACKET.getValue().equals(storeOrder.getExternalType())){
            if(null == traceMaxSyncTime){
                // 处理所有小包的轨迹
                processPackageOrderTracks(externalOrderNumber,entrustedOrderNumber,mainEntrustedOrder,null);
            }else{
                // 处理小包的轨迹增量
                processPackageOrderTracks(externalOrderNumber,entrustedOrderNumber,mainEntrustedOrder,traceMaxSyncTime.getSyncTime());
            }
        }

    }

    /**
     * 处理所有订单轨迹（首次同步）
     */
    private void processAllOrderTracks(String externalOrderNumber, String entrustedOrderNumber, String mainEntrustedOrder) {
        List<TmsOrderTrackEntity> orderTrackEntities = orderTrackService.selectAllByOrderNo(externalOrderNumber, null);

        if (CollUtil.isEmpty(orderTrackEntities)) {
            return;
        }

        for (TmsOrderTrackEntity orderTrackEntity : orderTrackEntities) {
            processOrderTrack(orderTrackEntity, entrustedOrderNumber, mainEntrustedOrder);
        }
    }

    /**
     * 处理增量订单轨迹
     */
    private void processIncrementalOrderTracks(String externalOrderNumber, String entrustedOrderNumber,
                                             String mainEntrustedOrder, LocalDateTime maxSyncTime) {
        List<TmsOrderTrackEntity> orderTrackEntities = orderTrackService.selectAllByOrderNo(externalOrderNumber, maxSyncTime);

        if (CollUtil.isEmpty(orderTrackEntities)) {
            return;
        }

        // 只处理最新的轨迹
        TmsOrderTrackEntity latestTrack = orderTrackEntities.get(0);
        processOrderTrack(latestTrack, entrustedOrderNumber, mainEntrustedOrder);

    }

    /**
     * 处理小包的订单轨迹
     */
    private void processPackageOrderTracks(String externalOrderNumber, String entrustedOrderNumber, String mainEntrustedOrder, LocalDateTime maxSyncTime){
        ResponseResult<PackageOrderTraceRoot> packageOrderTrace = PostBusinessUtils.getPackageOrderTrace(externalOrderNumber);
        if(StoreConstants.ONE == packageOrderTrace.getCode()){
            PackageOrderTraceRoot traceRoot = packageOrderTrace.getData();
            if(null != traceRoot){
                List<PackageOrderTraceDataItem> traceRootData = traceRoot.getData();

                if(CollUtil.isEmpty(traceRootData)){
                    logger.warn("[快递订单][轨迹跟踪] 小包轨迹数据为空: orderNo={}", entrustedOrderNumber);
                    return;
                }

                // 收集所有轨迹项
                List<PackageOrderTraceItem> allTraceItems = new ArrayList<>();
                for (PackageOrderTraceDataItem dataItem : traceRootData) {
                    if (dataItem.getFromDetail() != null) {
                        allTraceItems.addAll(dataItem.getFromDetail());
                    }
                }

                if(CollUtil.isEmpty(allTraceItems)){
                    logger.warn("[快递订单][轨迹跟踪] 小包轨迹详情为空: orderNo={}", entrustedOrderNumber);
                    return;
                }

                // 按时间排序，最新的在前面
                allTraceItems.sort((a, b) -> {
                    LocalDateTime timeA = DateUtil.parseDateTime(a.getPathTime()).toLocalDateTime();
                    LocalDateTime timeB = DateUtil.parseDateTime(b.getPathTime()).toLocalDateTime();
                    return timeB.compareTo(timeA); // 降序排列
                });

                // 过滤需要处理的轨迹
                List<PackageOrderTraceItem> filteredTraces = new ArrayList<>();
                for (PackageOrderTraceItem traceItem : allTraceItems) {
                    LocalDateTime traceTime = DateUtil.parseDateTime(traceItem.getPathTime()).toLocalDateTime();

                    if (maxSyncTime == null) {
                        // 首次同步，处理所有轨迹
                        filteredTraces.add(traceItem);
                    } else {
                        // 增量同步，只处理 maxSyncTime 之后的轨迹
                        if (traceTime.isAfter(maxSyncTime)) {
                            filteredTraces.add(traceItem);
                        }
                    }
                }
                if (CollUtil.isEmpty(filteredTraces)) {
                    logger.info("[快递订单][轨迹跟踪] 没有需要同步的小包轨迹: orderNo={}, maxSyncTime={}", entrustedOrderNumber, maxSyncTime);
                    return;
                }
                // 处理过滤后的轨迹
                for (PackageOrderTraceItem traceItem : filteredTraces) {
                    processPackageOrderTrack(traceItem, entrustedOrderNumber, mainEntrustedOrder);
                }

                logger.info("[快递订单][轨迹跟踪] 小包轨迹同步完成: orderNo={}, 处理轨迹数量={}", entrustedOrderNumber, filteredTraces.size());
            }
        } else {
            logger.error("[快递订单][轨迹跟踪] 获取小包轨迹失败: orderNo={}, response={}", entrustedOrderNumber, packageOrderTrace);
        }
    }

    /**
     * 处理单个小包轨迹
     */
    private void processPackageOrderTrack(PackageOrderTraceItem traceItem, String entrustedOrderNumber, String mainEntrustedOrder) {
        String pathCode = traceItem.getPathCode();
        OrderStatusMapping mapping = PACKAGE_ORDER_STATUS_MAPPING.get(pathCode);

        if (mapping == null) {
            if(logger.isWarnEnabled()){
                logger.warn("[快递订单][轨迹跟踪] 未知的小包轨迹状态码: pathCode={}, orderNo={}", pathCode, entrustedOrderNumber);
            }
            return;
        }

        TmsStoreOrderTraceEntity storeOrderTraceEntity = createPackageTraceEntity(
            traceItem, entrustedOrderNumber, mainEntrustedOrder, mapping);

        try {
            storeOrderTraceService.save(storeOrderTraceEntity);
            storeOrderService.updateOrderStatusByEntrustedOrderNumber(entrustedOrderNumber, mapping.getTargetStatus());

            if(logger.isInfoEnabled()){
                logger.info("[快递订单][轨迹跟踪] 成功处理小包轨迹: orderNo={}, pathCode={}->{}",
                        entrustedOrderNumber, pathCode, mapping.getTargetStatus());
            }
        } catch (Exception e) {
            logger.error("[快递订单][轨迹跟踪] 保存小包轨迹失败: orderNo={}, pathCode={}", entrustedOrderNumber, pathCode, e);
        }
    }

    /**
     * 创建小包轨迹实体
     */
    private TmsStoreOrderTraceEntity createPackageTraceEntity(PackageOrderTraceItem traceItem,
                                                             String entrustedOrderNumber, String mainEntrustedOrder,
                                                             OrderStatusMapping mapping) {
        TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
        traceEntity.setSubEntrustedOrder(entrustedOrderNumber);
        traceEntity.setMainEntrustedOrder(mainEntrustedOrder);

        // 解析轨迹时间
        LocalDateTime traceTime = DateUtil.parseDateTime(traceItem.getPathTime()).toLocalDateTime();
        traceEntity.setOperateTime(traceTime);
        traceEntity.setSyncTime(traceTime);

        traceEntity.setOrderStatus(mapping.getTargetStatus());

        // 设置外部扩展信息，包含轨迹时间和轨迹信息
        ExternalContext externalContext = new ExternalContext(traceTime, traceItem.getPathInfo());
        traceEntity.setExternalExtend(externalContext.toString());
        traceEntity.setOrderStatusContext(mapping.getTargetStatusContext());

        return traceEntity;
    }


    /**
     * 处理单个订单轨迹
     */
    private void processOrderTrack(TmsOrderTrackEntity orderTrackEntity, String entrustedOrderNumber, String mainEntrustedOrder) {
        String orderStatusDes = orderTrackEntity.getOrderStatus();
        OrderStatusMapping mapping = NB_ORDER_STATUS_MAPPING.get(orderStatusDes);

        if (mapping == null) {
            if(logger.isWarnEnabled()){
                logger.warn("[快递订单][轨迹跟踪] 未知的订单状态: {}", orderStatusDes);
            }
            return;
        }

        TmsStoreOrderTraceEntity storeOrderTraceEntity = createTraceEntity(
            orderTrackEntity, entrustedOrderNumber, mainEntrustedOrder, mapping);

        try {
            storeOrderTraceService.save(storeOrderTraceEntity);
            storeOrderService.updateOrderStatusByEntrustedOrderNumber(entrustedOrderNumber, mapping.getTargetStatus());

            if(logger.isInfoEnabled()){
                logger.info("[快递订单][轨迹跟踪] 成功处理订单轨迹: orderNo={}, status={}->{}",
                        entrustedOrderNumber, orderStatusDes, mapping.getTargetStatus());
            }
        } catch (Exception e) {
            logger.error("[快递订单][轨迹跟踪] 保存轨迹失败: orderNo={}, status={}", entrustedOrderNumber, orderStatusDes, e);
        }
    }

    /**
     * 创建轨迹实体
     */
    private TmsStoreOrderTraceEntity createTraceEntity(TmsOrderTrackEntity orderTrackEntity,
                                                      String entrustedOrderNumber, String mainEntrustedOrder,
                                                      OrderStatusMapping mapping) {
        TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
        traceEntity.setSubEntrustedOrder(entrustedOrderNumber);
        traceEntity.setMainEntrustedOrder(mainEntrustedOrder);
        traceEntity.setOperateTime(orderTrackEntity.getCreateTime());
        traceEntity.setSyncTime(orderTrackEntity.getCreateTime());
        traceEntity.setOrderStatus(mapping.getTargetStatus());
        ExternalContext externalContext = new ExternalContext(orderTrackEntity.getCreateTime(), orderTrackEntity.getExternalDescription());
        traceEntity.setExternalExtend(externalContext.toString());
        traceEntity.setOrderStatusContext(mapping.getTargetStatusContext());
        return traceEntity;
    }

    /**
     * 订单状态映射内部类
     */
    private static class OrderStatusMapping {
        private final Integer targetStatus;
        private final String targetStatusContext;

        public OrderStatusMapping(Integer targetStatus, String targetStatusContext) {
            this.targetStatus = targetStatus;
            this.targetStatusContext = targetStatusContext;
        }

        public Integer getTargetStatus() {
            return targetStatus;
        }

        public String getTargetStatusContext() {
            return targetStatusContext;
        }
    }

    /**
     * 外部上下文内部类
     */
    private static class ExternalContext {
        private LocalDateTime traceTime;
        private String traceInfo;
        public ExternalContext() {}
        public ExternalContext(LocalDateTime traceTime, String traceInfo) {
            this.traceTime = traceTime;
            this.traceInfo = traceInfo;
        }
        @Override
        public String toString() {
            return "{" +
                    "traceTime='" + traceTime + '\'' +
                    ", traceInfo='" + traceInfo + '\'' +
                    '}';
        }
    }





}
