package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderGoodsEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderGoodsMapper;
import com.jygjexp.jynx.tms.service.TmsStoreOrderGoodsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店订单货物信息
 *
 * <AUTHOR>
 * @date 2025-07-14 17:39:58
 */
@Service
public class TmsStoreOrderGoodsServiceImpl extends ServiceImpl<TmsStoreOrderGoodsMapper, TmsStoreOrderGoodsEntity> implements TmsStoreOrderGoodsService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStoreOrderGoods(List<TmsStoreOrderGoodsEntity> tmsStoreOrderGoods) {
        if(CollUtil.isEmpty(tmsStoreOrderGoods)){
            return;
        }
        baseMapper.insert(tmsStoreOrderGoods);
    }

    @Override
    public List<TmsStoreOrderGoodsEntity> getByMainEntrustedOrder(String mainEntrustedOrder) {
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TmsStoreOrderGoodsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderGoodsEntity::getMainEntrustedOrder,mainEntrustedOrder);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMainEntrustedOrder(String mainEntrustedOrder) {
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return;
        }
        LambdaUpdateWrapper<TmsStoreOrderGoodsEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(TmsStoreOrderGoodsEntity::getMainEntrustedOrder,mainEntrustedOrder);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public Map<String, TmsStoreOrderGoodsEntity> getMapByMainEntrustedOrder(String mainEntrustedOrder) {
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return null;
        }
        LambdaQueryWrapper<TmsStoreOrderGoodsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderGoodsEntity::getMainEntrustedOrder,mainEntrustedOrder);
        List<TmsStoreOrderGoodsEntity> storeOrderGoodsEntities = baseMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(storeOrderGoodsEntities)){
            return null;
        }
        return  storeOrderGoodsEntities
                .stream()
                .collect(Collectors.toMap(TmsStoreOrderGoodsEntity::getSubEntrustedOrder, Function.identity(),(v1, v2)->v1));
    }
}
