package com.jygjexp.jynx.tms.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
public interface TmsStoreBalanceMapper extends JynxBaseMapper<TmsStoreBalanceEntity> {

    // 执行扣款
    int exchangeDeductAmount(@Param("storeCustomerId") Long storeCustomerId,
                       @Param("changeAmount") BigDecimal changeAmount,
                       @Param("amountHash") String amountHash);
}
