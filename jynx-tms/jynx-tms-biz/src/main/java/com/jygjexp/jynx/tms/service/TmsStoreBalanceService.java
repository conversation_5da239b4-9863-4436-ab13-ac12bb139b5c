package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceOfflineRecharge;

import java.math.BigDecimal;

public interface TmsStoreBalanceService extends IService<TmsStoreBalanceEntity> {

    /**
     * 线下充值
     *
     * @param offlineRecharge
     * @return
     */
    Boolean offlineRecharge(Long id, TmsStoreBalanceOfflineRecharge offlineRecharge);

    /**
     * 查询当前用户的余额
     *
     * @return
     */
    TmsStoreBalanceEntity getCurrent();

    /**
     * 根据门店客户id-获取余额
     * @param storeCustomerId
     * @return
     */
    TmsStoreBalanceEntity getByStoreCustomerId(Long storeCustomerId);


    /**
     * 下单扣款-变更客户金额
     * @param storeBalance - 金额哈希
     * @param entrustedOrderNumber - 流水单号
     * @return
     */
    void executeOrderDeduction(TmsStoreBalanceEntity storeBalance,BigDecimal deductAmount,String entrustedOrderNumber);

    /**
     * 初始化所有客户的余额
     *
     * @return
     */
    Boolean init();

}
