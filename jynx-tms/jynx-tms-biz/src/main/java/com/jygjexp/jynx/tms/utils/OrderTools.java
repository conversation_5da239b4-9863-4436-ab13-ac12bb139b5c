package com.jygjexp.jynx.tms.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.zone.ZoneRules;
import java.util.*;

/**
 * 订单工具类
 */
public class OrderTools {

    //客户单号
    private static final String[] CUSTOMER_LABEL_PREFIXES = {"GV", "JY", "U9999"};
    public static String getMD5(String input) {
        try {
            // 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 将输入字符串转换为字节数组并计算哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为16进制格式的字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                // 每个字节转换为2位16进制数
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }


    public static String mergeAndUpload(List<String> urls, String orderNo) throws Exception {
        if (urls == null || urls.isEmpty()) {
            throw new IllegalArgumentException("URL 列表不能为空");
        }

        ByteArrayOutputStream mergedOut = new ByteArrayOutputStream();
        PDFMergerUtility merger = new PDFMergerUtility();
        merger.setDestinationStream(mergedOut);

        // 1. 不立即关闭流，而是先保存下来，最后一起关闭
        List<InputStream> inputStreams = new ArrayList<>();

        try {
            for (String url : urls) {
                InputStream input = new URL(url).openStream();
                merger.addSource(input);
                inputStreams.add(input);
            }

            // 2. 合并 PDF
            merger.mergeDocuments(null);

            // 3. 获取 PDF 字节数组
            byte[] pdfBytes = mergedOut.toByteArray();
            Random random = new Random();
            int num = 1000 + random.nextInt(9000);
            // 4. 上传
            String resultUrl = AliYunOSS.sendToOssFromByteArray(pdfBytes, "huandan_file", orderNo +"_"+num +".pdf");
            System.out.println("上传成功，URL: " + resultUrl);
            return resultUrl;

        } finally {
            // 5. 最后关闭所有流
            for (InputStream in : inputStreams) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    //是否使用客户单号作为面单
    public static boolean isCustomerLabel(String orderNumber) {
        if (orderNumber == null) return false;
        for (String prefix : CUSTOMER_LABEL_PREFIXES) {
            if (orderNumber.startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将list集合转字符串，中间逗号隔开
     */
    public static String buildListToString(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return JSON.toJSONString(list).replaceAll("\"", "").replaceAll("]", "").replaceAll("\\[", "");

    }


    public static LocalDateTime transformLocalDateTime(String time) {

        // 定义对应的时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);

        // 解析为 ZonedDateTime
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(time, formatter);

        // 转换为 LocalDateTime
        return zonedDateTime.toLocalDateTime();

    }


    public static boolean isNull(String checkValue) {
        if (StringUtils.isBlank(checkValue)) {
            return true;
        }
        return false;
    }

    //获取昨天开始时间
    public static LocalDateTime getYesterdayStart() {
        return LocalDateTime.now().minusDays(1).with(LocalTime.MIN);
    }

    //获取昨天结束时间
    public static LocalDateTime getYesterdayEnd() {
        return LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
    }

    //获取当天时间精确到天
    public static String getFormattedDateMinusDays(String format, int day) {
        LocalDate currentDate = LocalDate.now().minusDays(day);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return currentDate.format(formatter);
    }

    //获取特定格式日期间隔
    public static Long getIntervalDays(String time) {
        // 拆分字符串
        String[] dates = time.split("-");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        LocalDate startDate = LocalDate.parse(dates[0], formatter);
        LocalDate endDate = LocalDate.parse(dates[1], formatter);
        // 计算天数差
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    //获取正确的单号
    public static String safeSubstringOrder(String orderNo) {
        if (orderNo == null || orderNo.length() <= 3) {
            return "";
        }
        if (orderNo.length() <= 15) {
            return orderNo;
        }
        return orderNo.substring(0, orderNo.length() - 3);
    }

    //判断是否批量查询系统单号
    public static boolean isBatchEntrustedOrder(String entrustedOrderNumber) {
        if (entrustedOrderNumber == null || entrustedOrderNumber.isEmpty()) {
            return false;
        }
        if (entrustedOrderNumber.length() <= 20) {
            return false;
        }
        String[] batch = entrustedOrderNumber.split("N");
        return batch.length > 1;
    }

    //判断是否批量查询条件
    public static boolean isBatchConditions(String paras) {
        if (paras == null || paras.isEmpty()) {
            return false;
        }
        return paras.contains(",");
    }


    /**
     * 单号处理
     */
    public static String processEntrustedOrderNumber(String entrustedOrderNumber) {
        if (entrustedOrderNumber == null) {
            return null;
        }
        int length = entrustedOrderNumber.length();
        if (length > 15 && length < 20) {
            return entrustedOrderNumber.substring(0, length - 3);
        }
        return entrustedOrderNumber;
    }


    //获取多伦多时区
    public static String getTimezone() {
        ZoneId torontoZone = ZoneId.of("America/Toronto");
        // 当前时间
        ZonedDateTime now = ZonedDateTime.now(torontoZone);
        // 获取时区规则
        ZoneRules rules = torontoZone.getRules();
        // 判断是否为夏令时
        boolean isDaylightSavingTime = rules.isDaylightSavings(now.toInstant());
        if (isDaylightSavingTime) {
            return "-04:00";
        } else {
            return "-05:00";
        }
    }

    //校验重复数据
    public static List<String> check(List<String> input) {
        Set<String> seen = new HashSet<>();
        List<String> result = new ArrayList<>();
        for (String str : input) {
            if (seen.add(str)) result.add(str);
        }
        return result;
    }


    public static boolean isWithin120Seconds(String timeStr) {
        DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            // 将时间字符串转换为 LocalDateTime
            LocalDateTime inputTime = LocalDateTime.parse(timeStr, FORMATTER);
            // 获取当前系统时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 计算时间差，单位为秒
            long secondsDiff = Math.abs(ChronoUnit.SECONDS.between(inputTime, currentTime));

            // 判断时间差是否在120秒之内
            return secondsDiff > 120;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    //获取当天开始时间
    public static LocalDateTime getTodayStart() {
        return LocalDateTime.now().with(LocalTime.MIN);
    }

    //获取当天结束时间
    public static LocalDateTime getTodayEnd() {
        return LocalDateTime.now().with(LocalTime.MAX);
    }

    //获取本周开始时间
    public static LocalDateTime getThisWeekStart() {
        LocalDate currentDate = LocalDate.now();
        DayOfWeek firstDayOfWeek = DayOfWeek.MONDAY;
        LocalDate firstDayOfWeekDate = currentDate.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
        return firstDayOfWeekDate.atStartOfDay();
    }

    //获取本月开始时间
    public static LocalDateTime getThisMonthStart() {
        LocalDate currentDate = LocalDate.now();
        return currentDate.withDayOfMonth(1).atStartOfDay();
    }


    //计算两个时间之间的时间差（工作日除外）
    public static int TimeDifferenceChecker(LocalDateTime startTime, LocalDateTime endTime) {
        int totalHours = 0;
        LocalDateTime current = startTime;
        while (current.isBefore(endTime)) {
            if (current.getDayOfWeek() != DayOfWeek.SATURDAY && current.getDayOfWeek() != DayOfWeek.SUNDAY) {
                LocalDateTime nextDay = current.plusDays(1).truncatedTo(ChronoUnit.DAYS);
                if (nextDay.isAfter(endTime)) {
                    totalHours += ChronoUnit.HOURS.between(current, endTime);
                } else {
                    totalHours += ChronoUnit.HOURS.between(current, nextDay);
                }
            }
            current = current.plusDays(1).truncatedTo(ChronoUnit.DAYS);
        }
        return totalHours;
    }

    //获取昨天开始时间
    public static LocalDateTime getYesterdayStartTime() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return LocalDateTime.of(yesterday, LocalTime.MIN);
    }

    //获取昨天结束时间
    public static LocalDateTime getYesterdayEndTime() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return LocalDateTime.of(yesterday, LocalTime.MAX);
    }

    //获取指定格式当天时间
    public static Date getCurrentDateTime(String format) {
        // 定义时间格式
        SimpleDateFormat formatter = new SimpleDateFormat(format);

        // 获取当前时间字符串
        String formattedDate = formatter.format(new Date());

        // 解析回 Date 类型
        try {
            return formatter.parse(formattedDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 将 source 数组的内容拷贝到 target 数组的末尾
     *
     * @param target 目标数组
     * @param source 源数组
     * @return 返回合并后的新数组
     */
    public static String[] copyArrayToEnd(String[] target, String[] source) {
        // 创建一个新的数组，大小为 target 数组和 source 数组的总和
        String[] result = new String[target.length + source.length];

        // 将 target 数组的内容拷贝到 result 数组
        System.arraycopy(target, 0, result, 0, target.length);

        // 将 source 数组的内容拷贝到 result 数组的后面
        System.arraycopy(source, 0, result, target.length, source.length);

        // 清除数组中的 null 元素
        List<String> nonNullList = new ArrayList<>();
        for (String element : result) {
            if (element != null) {
                nonNullList.add(element);
            }
        }
        // 将 List 转换为数组
        return nonNullList.toArray(new String[0]);
    }

    //下单校验


}
