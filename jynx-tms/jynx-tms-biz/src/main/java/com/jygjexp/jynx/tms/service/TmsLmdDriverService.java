package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsLmdDriverEntity;
import com.jygjexp.jynx.tms.entity.TmsReportManagementEntity;
import com.jygjexp.jynx.tms.entity.TmsWarehouseEmployeeEntity;
import com.jygjexp.jynx.tms.model.bo.SendReviewBo;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.app.*;
import com.jygjexp.jynx.tms.vo.excel.TmsLmdDriverExcelVo;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface TmsLmdDriverService extends IService<TmsLmdDriverEntity> {

    // 分页查询
    Page<TmsLmdDriverPageVo> search(Page page , TmsLmdDriverPageVo vo);

    // 新增司机
    R addLmdDriver(TmsLmdDriverEntity tmsLmdDriverEntity);

    // 修改司机
    R updateLmdDriver(TmsLmdDriverEntity tmsLmdDriverEntity);

    // 司机启用停用
    R LmdbBusinessSwitch(Long driverId, Integer isValid);

    // 司机注册
    R register(TmsAppLmdDriverVo driverVo);

    // 根据手机号获取信息
    R getPhone(String phone);

    // 司机修改个人信息
    R driverInfoUpdate(TmsAppDriverPersonVo driverInfo);

    // 仓库修改个人信息
    R warehouseInfoUpdate(TmsAppWarehousePersonVo warehouseInfo);

    // 司机审核
    R audit(TmsLmdDriverEntity tmsLmdDriverEntity);

    // 中大件司机列表导出
    List<TmsLmdDriverExcelVo> getDriverExcel(TmsLmdDriverPageVo vo, Long[] ids);

    /**
     * 上传文件
     * @param file 文件流
     * @param dir 文件夹
     * @param groupId 分组ID
     * @param type 类型
     * @return
     */
    R uploadFile(MultipartFile file, String dir, Long groupId, String type);

    // web端司机揽收上传取货证明
    R webUploadPickupProof(TmsAppDriverDeliveryVo vo);

    // web端司机派送上传取货证明
    R webDeliveryUploadPickupProof(TmsAppDriverDeliveryVo vo);

    // 根据单号获取揽收/派送订单POD
    R getOrderPOD(String orderNo);

    // 根据状态查询揽收/派送订单
    R getOrderListByStatus(Integer isTask,Integer status,Long driverId);

    // 查询派送配送失败列表
    R getDeliveryFailedList(TmsAppDeliveryFailedListVo vo);

    // 派送、揽收异常上报
    R reporting(TmsAppExceptionManagementVo tmsExceptionManagement);

    // 根据任务单号查询详情
    R getOrderDetailByTaskNo(String taskNo);

    // 根据跟踪单号查询详情
    R getOrderNoDetai(String entrustedOrder,Integer isTask);

    // 派送根据跟踪单号查询主子单详情
    R getDeliveryOrderNoDetail(String entrustedOrder);

    // 派送地图根据批次号查询全部主子单详情
    R getDeliveryOrderNoAll(String taskNo);

    // 揽收-消息通知获取详情
    R getMessageOrderNoDetai(String orderNo);

    // 根据任务单号查询跟踪单详情
    R getDetailByTaskNo(String taskNo,Integer isTask);

    /**
     * 查询所有司机信息
     */
    R getDriverinfo(Page page, TmsScheduleDriverDto tmsScheduleDriverDto);
    // 司机送达失败
    //R deliveryFailed(String entrustedOrderNumber);

    // 司机扫描取货
    R scanPick(String entrustedOrderNo, Long driverId, Integer isTask);

    // 单个主单查询子单号（查询扫描列表）
    R getSubFlag(String entrustedOrderNos);

    // 根据派送任务号查询子单号
    R getDeliverySubFlag(String taskNo);

    // 揽收-根据司机id和状态查询子单号（查询扫描列表）
    R getPickUpSubFlag(Integer driverId,Integer isTask);

    // 司机上传取货证明
    R uploadPickupProof(String entrustedOrderNumber,  String pickupProof ,Integer isTask);

    // 司机送货成功
    R deliverySuccess(String entrustedOrderNumber,String deliveryProof,Integer isTask);

    //判断当前单号是否是客户单号，如果是客户单号，则返回跟踪单号
     String isCustomerOrder(String entrustedOrderNumber,Boolean needEntrustedOrderNumberFlag);

    // app-派送司机（子单纬度）配送成功
    R subOrderListSuccess(TmsAppDriverSubOrderDeliveryVo  vo);

    // 司机揽收生成报告(送货成功)
//    R collectSuccess(Long driverId);

    // 揽收提交审核(确认送货完成)
//    R collectReview(String reportOrderNo);

    // 揽收成功-批量上传送货证明
    R batchUploadCollectionProof(TmsAppCollectionDeliveryVo vo);

    // 派送提交审核(取货完成)
    R sendReview(SendReviewBo reviewBo);

    // 派送生成报告(完成取货)
    R generateDeliveryReport(Long driverId, List<String> entrustedOrderNos,List<String> returnOrderNos);

    // app-校验所有面单是否全部已扫描(完成取货)
    R checkAllScanned(Long driverId, List<String> entrustedOrderNos);

    /**
     * 配送失败
     * @param request
     * @return
     */
    R deliveryFailed(DeliveryFailedRequest request);

    // 揽收批量上传取货证明
    R batchUploadPickupProof(TmsAppCollectionPickupVo vo);

    // 获取待扫描数量
    R getWaitScanCount(Long driverId);

    // app-入库扫描单号
    R ibScan(String orderNo,Long warehouseId);

    // app-仓库扫描入库
    R ibScanWarehouse(TmsStorageRecordAddVo vo);

    // app-出库扫描单号
    R outScan(String orderNo, Long warehouseId);

    // app-仓库扫描出库
    R outScanWarehouse(TmsOutboundRecordDto vo);

    // app-笼车扫描
    R cageScan(String cageCode);

    // app-笼车扫描单号
    R orderScan(String orderNo,String labelCode);

    // app扫描入笼
    R scanOrderAndCage(TmsAppCageAndOrderVo vo);

    // 根据状态查询干线单列表
    R getLineHaulOrderListByStatus(Integer status, Long driverId);

    // 干线上传提货凭证
    R uploadLinePickupProof(String lineHaulOrderNo, String pickupProof);
    // app干线上传送货凭证
    R uploadLineDeliveryProof(String lineHaulOrderNo, String deliveryProof);

    // app-根据干线任务单查询详情
    R getLineHaulOrderDetail(String lineHaulOrderNo);
    //app干线任务单异常上报
    R appUploadException(TmsAppExceptionUploadDto tmsAppExceptionUploadDto);

    // app-干线司机待扫描标签列表
    R getDriverTaskWaitScanLabel(Long driverId);
    //app-干线扫码提货（扫描后将其任务设置为派送中）
    R scanPickup(String labelNo);
    //app-干线确认扫码提货（确认后将其任务设置为派送中）
    R confirmScanPickup(Long driverId);


    // app-仓库人工分拣
    R manualSorting(TmsManualSortingRecordDto vo);

    /**
     * 上传任务维度的司机车辆经纬度（任务开始到任务结束的经纬度记录）
     */
    R uploadDriverCarLocation(TmsLargeDriverRealTimeLocationDto tmsLargeDriverRealTimeLocationDto);

    /**
     * 根据任务单号和任务类型查询任务单车辆轨迹
     */
    R getVehicleTrailByTaskNo(String taskNo, Integer taskType);

    // 地图揽收任务运输中和待提货列表
    R getCollectionOrderList(Long driverId);

    // 地图派送运输中和待提货列表
    R getDeliveryOrderList(Long driverId);

}