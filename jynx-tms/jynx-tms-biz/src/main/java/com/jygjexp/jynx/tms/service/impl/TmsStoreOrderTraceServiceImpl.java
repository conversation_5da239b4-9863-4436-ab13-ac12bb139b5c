package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderTraceEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderTraceMapper;
import com.jygjexp.jynx.tms.service.TmsStoreOrderTraceService;
import com.jygjexp.jynx.tms.vo.StoreOrderTracesVO;
import com.jygjexp.jynx.tms.vo.StoreOrderMainTracesVO;
import com.jygjexp.jynx.tms.vo.StoreTracesDetailVO;
import com.jygjexp.jynx.tms.vo.StoreTracesSubDetailVO;
import com.jygjexp.jynx.tms.vo.TracesOrderStatusVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店订单轨迹跟踪
 *
 * <AUTHOR>
 * @date 2025-07-31 15:12:18
 */
@Service
public class TmsStoreOrderTraceServiceImpl extends ServiceImpl<TmsStoreOrderTraceMapper, TmsStoreOrderTraceEntity> implements TmsStoreOrderTraceService {

    @Autowired
    private TmsStoreOrderMapper tmsStoreOrderMapper;

    @Override
    public StoreOrderTracesVO getStoreOrderTraces(String entrustedOrderNumber) {
        if (StrUtil.isBlank(entrustedOrderNumber)) {
            return new StoreOrderTracesVO();
        }
        // 1. 解析所有主单号
        List<String> mainEntrustedOrders = parseEntrustedOrderNumbers(entrustedOrderNumber);
        if (CollUtil.isEmpty(mainEntrustedOrders)) {
            return new StoreOrderTracesVO();
        }
        // 2. 构建主单轨迹信息
        List<StoreOrderMainTracesVO> mainTracesList = buildMainTracesList(mainEntrustedOrders);
        // 3. 统计所有子单状态
        List<TmsStoreOrderEntity> allSubOrders = getAllSubOrders(mainEntrustedOrders);
        List<TracesOrderStatusVO> orderStatus = buildOrderStatusStatistics(allSubOrders);
        // 4. 汇总所有子单数量
        int orderCounts = allSubOrders.size();
        // 5. 组装VO
        StoreOrderTracesVO vo = new StoreOrderTracesVO();
        vo.setOrderTraces(mainTracesList);
        vo.setOrderStatus(orderStatus);
        vo.setOrderCounts(orderCounts);
        return vo;
    }

    /**
     * 解析跟踪单号，获取所有主跟踪单号
     * @param entrustedOrderNumber 逗号分割的跟踪单号
     * @return 主跟踪单号列表
     */
    private List<String> parseEntrustedOrderNumbers(String entrustedOrderNumber) {
        if (StrUtil.isBlank(entrustedOrderNumber)) {
            return Collections.emptyList();
        }

        String[] orderNumbers = entrustedOrderNumber.split(",");
        Set<String> mainEntrustedOrders = new HashSet<>();

        for (String orderNumber : orderNumbers) {
            String trimmedOrderNumber = orderNumber.trim();
            if (StrUtil.isBlank(trimmedOrderNumber)) {
                continue;
            }
            // 判断是主单号还是子单号
            if (trimmedOrderNumber.length() == StoreConstants.MAIN_ORDER_LENGTH) {
                // 13位是主跟踪单号
                mainEntrustedOrders.add(trimmedOrderNumber);
            } else if (trimmedOrderNumber.length() == StoreConstants.SUB_ORDER_LENGTH) {
                // 16位是子跟踪单号，需要找到对应的主跟踪单号
                String mainOrderNumber = findMainEntrustedOrderBySubOrder(trimmedOrderNumber);
                if (StrUtil.isNotBlank(mainOrderNumber)) {
                    mainEntrustedOrders.add(mainOrderNumber);
                }
            }
        }

        return new ArrayList<>(mainEntrustedOrders);
    }

    /**
     * 根据子单号查找主单号
     * @param subEntrustedOrder 子跟踪单号
     * @return 主跟踪单号
     */
    private String findMainEntrustedOrderBySubOrder(String subEntrustedOrder) {
        if (StrUtil.isBlank(subEntrustedOrder)) {
            return null;
        }
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getEntrustedOrderNumber, subEntrustedOrder);
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag, StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        queryWrapper.last("limit 1");
        TmsStoreOrderEntity orderEntity = tmsStoreOrderMapper.selectOne(queryWrapper);

        return orderEntity != null ? orderEntity.getMainEntrustedOrder() : null;
    }

    /**
     * 构建主单轨迹信息列表
     * @param mainEntrustedOrders 主单号列表
     * @return 主单轨迹信息列表
     */
    private List<StoreOrderMainTracesVO> buildMainTracesList(List<String> mainEntrustedOrders) {
        List<StoreOrderMainTracesVO> mainTracesList = new ArrayList<>();
        for (String mainEntrustedOrder : mainEntrustedOrders) {
            StoreOrderMainTracesVO mainTracesVO = buildMainTracesVO(mainEntrustedOrder);
            if (mainTracesVO != null) {
                mainTracesList.add(mainTracesVO);
            }
        }
        return mainTracesList;
    }

    /**
     * 构建单个主单轨迹信息
     * @param mainEntrustedOrder 主单号
     * @return 主单轨迹信息
     */
    private StoreOrderMainTracesVO buildMainTracesVO(String mainEntrustedOrder) {
        // 查询该主单下所有子单
        List<TmsStoreOrderEntity> subOrders = tmsStoreOrderMapper.getSubOrderByMainEntrustedOrder(mainEntrustedOrder);
        if (CollUtil.isEmpty(subOrders)) {
            return null;
        }
        // 构建子单轨迹详情
        List<StoreTracesSubDetailVO> subTracesDetailList = buildSubTracesDetailList(subOrders);
        // 组装主单轨迹VO
        StoreOrderMainTracesVO mainTracesVO = new StoreOrderMainTracesVO();
        mainTracesVO.setEntrustedOrderNumber(mainEntrustedOrder);
        mainTracesVO.setSubTracesDetail(subTracesDetailList);
        return mainTracesVO;
    }

    /**
     * 构建子单轨迹详情列表
     * @param subOrders 子单列表
     * @return 子单轨迹详情列表
     */
    private List<StoreTracesSubDetailVO> buildSubTracesDetailList(List<TmsStoreOrderEntity> subOrders) {
        List<StoreTracesSubDetailVO> subTracesDetailList = new ArrayList<>();
        for (TmsStoreOrderEntity subOrder : subOrders) {
            StoreTracesSubDetailVO subDetailVO = buildSubTracesDetailVO(subOrder);
            subTracesDetailList.add(subDetailVO);
        }
        return subTracesDetailList;
    }

    /**
     * 构建单个子单轨迹详情
     * @param subOrder 子单信息
     * @return 子单轨迹详情
     */
    private StoreTracesSubDetailVO buildSubTracesDetailVO(TmsStoreOrderEntity subOrder) {
        String subEntrustedOrder = subOrder.getEntrustedOrderNumber();
        String mainEntrustedOrder = subOrder.getMainEntrustedOrder();

        // 查询子单轨迹
        List<TmsStoreOrderTraceEntity> traces = selectAllTraceBySubEntrustedOrder(subEntrustedOrder);
        // 按operate_time倒序
        traces.sort(Comparator.comparing(TmsStoreOrderTraceEntity::getOperateTime, Comparator.nullsLast(Comparator.reverseOrder())));

        // 构建轨迹详情列表
        List<StoreTracesDetailVO> tracesDetail = buildTracesDetailList(subEntrustedOrder, mainEntrustedOrder, traces, subOrder);

        // 组装子单轨迹详情VO
        StoreTracesSubDetailVO subDetailVO = new StoreTracesSubDetailVO();
        subDetailVO.setEntrustedOrderNumber(subEntrustedOrder);
        subDetailVO.setTracesDetail(tracesDetail);
        return subDetailVO;
    }

    /**
     * 构建轨迹详情列表
     * @param subEntrustedOrder 子单号
     * @param mainEntrustedOrder 主单号
     * @param traces 轨迹数据
     * @param subOrder 子单信息
     * @return 轨迹详情列表
     */
    private List<StoreTracesDetailVO> buildTracesDetailList(String subEntrustedOrder, String mainEntrustedOrder,
                                                           List<TmsStoreOrderTraceEntity> traces, TmsStoreOrderEntity subOrder) {
        List<StoreTracesDetailVO> tracesDetail = new ArrayList<>();

        if (CollUtil.isNotEmpty(traces)) {
            // 有轨迹数据，构建轨迹详情
            for (TmsStoreOrderTraceEntity trace : traces) {
                StoreTracesDetailVO detailVO = buildTracesDetailVO(subEntrustedOrder, mainEntrustedOrder, trace);
                tracesDetail.add(detailVO);
            }
        } else {
            // 没有轨迹数据，创建默认轨迹详情
            StoreTracesDetailVO detailVO = buildDefaultTracesDetailVO(subEntrustedOrder, mainEntrustedOrder, subOrder);
            tracesDetail.add(detailVO);
        }

        return tracesDetail;
    }

    /**
     * 构建轨迹详情VO
     * @param subEntrustedOrder 子单号
     * @param mainEntrustedOrder 主单号
     * @param trace 轨迹数据
     * @return 轨迹详情VO
     */
    private StoreTracesDetailVO buildTracesDetailVO(String subEntrustedOrder, String mainEntrustedOrder, TmsStoreOrderTraceEntity trace) {
        StoreTracesDetailVO detailVO = new StoreTracesDetailVO();
        detailVO.setSubEntrustedOrder(subEntrustedOrder);
        detailVO.setMainEntrustedOrder(mainEntrustedOrder);
        detailVO.setOrderStatus(trace.getOrderStatus());
        detailVO.setOrderStatusContext(trace.getOrderStatusContext());
        detailVO.setOperateTime(trace.getOperateTime());
        return detailVO;
    }

    /**
     * 构建默认轨迹详情VO（当没有轨迹数据时）
     * @param subEntrustedOrder 子单号
     * @param mainEntrustedOrder 主单号
     * @param subOrder 子单信息
     * @return 默认轨迹详情VO
     */
    private StoreTracesDetailVO buildDefaultTracesDetailVO(String subEntrustedOrder, String mainEntrustedOrder, TmsStoreOrderEntity subOrder) {
        StoreTracesDetailVO detailVO = new StoreTracesDetailVO();
        detailVO.setSubEntrustedOrder(subEntrustedOrder);
        detailVO.setMainEntrustedOrder(mainEntrustedOrder);
        detailVO.setOrderStatus(subOrder.getOrderStatus());
        detailVO.setOrderStatusContext(getOrderStatusName(subOrder.getOrderStatus()));
        detailVO.setOperateTime(subOrder.getUpdateTime() != null ? subOrder.getUpdateTime() : subOrder.getCreateTime());
        return detailVO;
    }

    /**
     * 获取所有子单信息
     * @param mainEntrustedOrders 主单号列表
     * @return 所有子单信息列表
     */
    private List<TmsStoreOrderEntity> getAllSubOrders(List<String> mainEntrustedOrders) {
        List<TmsStoreOrderEntity> allSubOrders = new ArrayList<>();
        for (String mainEntrustedOrder : mainEntrustedOrders) {
            List<TmsStoreOrderEntity> subOrders = tmsStoreOrderMapper.getSubOrderByMainEntrustedOrder(mainEntrustedOrder);
            if (CollUtil.isNotEmpty(subOrders)) {
                allSubOrders.addAll(subOrders);
            }
        }
        return allSubOrders;
    }

    /**
     * 构建订单状态统计
     * @param subOrders 子单列表
     * @return 订单状态统计列表
     */
    private List<TracesOrderStatusVO> buildOrderStatusStatistics(List<TmsStoreOrderEntity> subOrders) {
        // 按状态分组统计
        Map<Integer, Long> statusCountMap = subOrders.stream()
                .collect(Collectors.groupingBy(TmsStoreOrderEntity::getOrderStatus, Collectors.counting()));

        List<TracesOrderStatusVO> result = new ArrayList<>();

        for (Map.Entry<Integer, Long> entry : statusCountMap.entrySet()) {
            TracesOrderStatusVO statusVO = new TracesOrderStatusVO();
            statusVO.setOrderStatus(entry.getKey());
            statusVO.setOrderStatusCount(entry.getValue().intValue());

            // 设置状态名称
            String statusName = getOrderStatusName(entry.getKey());
            statusVO.setOrderStatusName(statusName);

            result.add(statusVO);
        }

        // 按状态排序
        result.sort(Comparator.comparing(TracesOrderStatusVO::getOrderStatus));

        return result;
    }

    /**
     * 获取订单状态名称
     * @param orderStatus 订单状态值
     * @return 状态名称
     */
    private String getOrderStatusName(Integer orderStatus) {
        if (orderStatus == null) {
            return "未知状态";
        }

        for (StoreEnums.StoreOrder.OrderStatus status : StoreEnums.StoreOrder.OrderStatus.values()) {
            if (status.getValue().equals(orderStatus)) {
                return status.getName();
            }
        }

        return "未知状态";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStoreOrderTrace(List<TmsStoreOrderTraceEntity> storeOrderTraces) {
        if(CollUtil.isEmpty(storeOrderTraces)){
            return;
        }
        storeOrderTraces.forEach(e->{
            LocalDateTime operateTime = e.getOperateTime();
            if(null == operateTime){
                e.setOperateTime(LocalDateTime.now());
            }
        });
        baseMapper.insert(storeOrderTraces);
    }
    @Override
    public List<TmsStoreOrderTraceEntity> selectAllTraceBySubEntrustedOrder(String subEntrustedOrder) {
        if(StrUtil.isBlank(subEntrustedOrder)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TmsStoreOrderTraceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderTraceEntity::getSubEntrustedOrder, subEntrustedOrder);
        queryWrapper.orderByDesc(TmsStoreOrderTraceEntity::getOperateTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public TmsStoreOrderTraceEntity getTraceMaxSycTime(String subEntrustedOrder) {
        if(StrUtil.isBlank(subEntrustedOrder)){
            return null;
        }
        LambdaQueryWrapper<TmsStoreOrderTraceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderTraceEntity::getSubEntrustedOrder, subEntrustedOrder);
        queryWrapper.isNotNull(TmsStoreOrderTraceEntity::getSyncTime);
        queryWrapper.orderByDesc(TmsStoreOrderTraceEntity::getSyncTime);
        queryWrapper.last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }
}
