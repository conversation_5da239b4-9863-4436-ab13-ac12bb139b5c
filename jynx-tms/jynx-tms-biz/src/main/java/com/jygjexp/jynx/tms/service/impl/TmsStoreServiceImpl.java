package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.entity.SysDept;
import com.jygjexp.jynx.admin.api.entity.SysPost;
import com.jygjexp.jynx.admin.api.entity.SysRole;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteDeptService;
import com.jygjexp.jynx.admin.api.feign.RemotePostService;
import com.jygjexp.jynx.admin.api.feign.RemoteRoleService;
import com.jygjexp.jynx.admin.api.vo.UserVO;
import com.jygjexp.jynx.common.core.constant.CommonConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.dto.TmsStoreDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreBankInfoEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreEmployeeEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreUserEntity;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsStoreMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreBankInfoService;
import com.jygjexp.jynx.tms.service.TmsStoreEmployeeService;
import com.jygjexp.jynx.tms.service.TmsStoreService;
import com.jygjexp.jynx.tms.service.TmsStoreUserService;
import com.jygjexp.jynx.tms.vo.TmsStoreBankInfoVo;
import com.jygjexp.jynx.tms.vo.TmsStoreDetailVo;
import com.jygjexp.jynx.tms.vo.TmsStoreExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStorePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 门店信息表
 *
 * <AUTHOR>
 * @date 2025-07-08 16:42:43
 */
@Service
@RequiredArgsConstructor
public class TmsStoreServiceImpl extends ServiceImpl<TmsStoreMapper, TmsStoreEntity> implements TmsStoreService {
    private final RemoteTmsUpmsService remoteTmsUpmsService;
    private final RemoteDeptService remoteDeptService;
    private final RemoteRoleService remoteRoleService;
    private final RemotePostService remotePostService;
    private final TmsStoreUserService tmsStoreUserService;
    private final TmsStoreBankInfoService tmsStoreBankInfoService;
    private final TmsStoreEmployeeService tmsStoreEmployeeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveStroe(TmsStoreDTO tmsStore) {
        // 判断用户名或手机号是否已存在
        if (remoteTmsUpmsService.getCustomerUserByPhoneOrUsername(tmsStore.getContactPhone(), tmsStore.getContactName()).getData()) {
            return LocalizedR.failed("tms.store.name.phone.already.exists", "");
        }
        // 判断门店代码是否存在
        if (this.count(new LambdaQueryWrapper<TmsStoreEntity>()
                .eq(TmsStoreEntity::getStoreCode, tmsStore.getStoreCode())) > 0) {
            return LocalizedR.failed("tms.store.code.already.exists", tmsStore.getStoreCode());
        }
        // 保存用户信息
        SysUser sysUser = saveUserInfo(tmsStore.getContactName(), tmsStore.getContactPhone());
        try {
            // 保存门店信息
            TmsStoreEntity tmsStoreEntity = new TmsStoreEntity();
            BeanUtils.copyProperties(tmsStore, tmsStoreEntity);
            // 设置门店管理员信息
            tmsStoreEntity.setUserId(sysUser.getUserId());
            baseMapper.insert(tmsStoreEntity);
            // 保存门店和用户信息
            TmsStoreUserEntity tmsStoreUserEntity = new TmsStoreUserEntity();
            tmsStoreUserEntity.setUserId(sysUser.getUserId());
            tmsStoreUserEntity.setStoreId(tmsStoreEntity.getId());
            tmsStoreUserService.save(tmsStoreUserEntity);
            // 设置商家收款信息
            tmsStore.getTmsStoreBankInfoList().forEach(tmsStoreBankInfo -> {
                tmsStoreBankInfo.setStoreId(tmsStoreEntity.getId());
            });
            // 保存商家收款信息
            tmsStoreBankInfoService.saveBatch(tmsStore.getTmsStoreBankInfoList());
            // 保存到店员工信息
            TmsStoreEmployeeEntity tmsStoreEmployee = new TmsStoreEmployeeEntity();
            tmsStoreEmployee.setUserId(sysUser.getUserId());
            tmsStoreEmployee.setEmployeeName(tmsStore.getContactName());
            tmsStoreEmployee.setEmployeePhone(tmsStore.getContactPhone());
            tmsStoreEmployee.setEmployeeType("0");
            tmsStoreEmployeeService.save(tmsStoreEmployee);
            return R.ok();
        } catch (Exception e) {
            remoteTmsUpmsService.userDel(new Long[]{sysUser.getUserId()});
            return R.failed();
        }
    }

    @Override
    public R getStoreInfo() {
        Map<String, Object> map = new HashMap<>();
        Long id = SecurityUtils.getUser().getId();
        // 获取用户基本信息
        UserVO user = remoteTmsUpmsService.userDetail(id).getData();
        map.put("user", user);
        // 获取门店信息
        TmsStoreUserEntity storeUser = tmsStoreUserService.getOne(new LambdaQueryWrapper<TmsStoreUserEntity>()
                .eq(TmsStoreUserEntity::getUserId, id), false);
        if (storeUser != null) {
            TmsStoreEntity store = this.getById(storeUser.getStoreId());
            map.put("store", store);
        }
        return R.ok(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R remoteStore(Long[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return LocalizedR.failed("tms.store.id.empty", "");
        }

        try {
            // 获取门店关联的用户列表
            List<TmsStoreUserEntity> storeUsers = tmsStoreUserService.list(new LambdaQueryWrapper<TmsStoreUserEntity>()
                    .in(TmsStoreUserEntity::getStoreId, ids)
            );

            // 先删除门店（如果门店删除失败，不应该删除用户）
            boolean storeDeleted = this.removeBatchByIds(Arrays.asList(ids));
            if (!storeDeleted) {
                return LocalizedR.failed("tms.store.delete.failed", "");
            }

            // 删除关联用户（门店删除成功后再删除用户）
            if (CollUtil.isNotEmpty(storeUsers)) {
                // 提取所有用户ID进行批量删除
                Long[] userIds = storeUsers.stream()
                        .map(TmsStoreUserEntity::getUserId)
                        .toArray(Long[]::new);

                remoteTmsUpmsService.userDel(userIds);
            }
        } catch (Exception e) {
            return LocalizedR.failed("tms.store.delete.failed", "");
        }
        return R.ok();
    }

    @Override
    public R updateStatus(TmsStoreEntity tmsStore) {
        // 更新门店状态
        boolean updated = this.updateById(tmsStore);
        if (updated) {
            // 获取门店下的所有用户
            List<TmsStoreUserEntity> storeUsers = tmsStoreUserService.list(new LambdaQueryWrapper<TmsStoreUserEntity>()
                    .eq(TmsStoreUserEntity::getStoreId, tmsStore.getId()));
            if (CollUtil.isNotEmpty(storeUsers)) {
                // 提取所有用户ID
                Long[] userIds = storeUsers.stream()
                        .map(TmsStoreUserEntity::getUserId)
                        .toArray(Long[]::new);
                // 禁用用户状态
                if (tmsStore.getStatus().equals(0)){
                    // 禁用用户状态
                    remoteTmsUpmsService.userLocks(Arrays.asList(userIds));
                }else if (tmsStore.getStatus().equals(1)){
                    // 启用用户
                    remoteTmsUpmsService.userEnables(Arrays.asList(userIds));
                }
            }
            return R.ok();
        }
        return R.failed();
    }

    @Override
    public R getStoreIdByUser(Long userId) {
        // 获取门店用户对应的门店ID
        TmsStoreUserEntity storeUser = tmsStoreUserService.getOne(new LambdaQueryWrapper<TmsStoreUserEntity>()
                .eq(TmsStoreUserEntity::getUserId, userId), false);
        if (storeUser != null) {
            return R.ok(storeUser.getStoreId());
        }
        return null;
    }

    @Override
    public TmsStoreDetailVo getStoreDetailById(Long id) {
        if (id == null){
            return new TmsStoreDetailVo();
        }
        TmsStoreEntity store = this.getById(id);
        if (store != null) {
            TmsStoreDetailVo detailVo = new TmsStoreDetailVo();
            BeanUtils.copyProperties(store, detailVo);
            detailVo.setTmsStoreBankInfoVo(new ArrayList<>());
            tmsStoreBankInfoService.list(new LambdaQueryWrapper<TmsStoreBankInfoEntity>()
                    .eq(TmsStoreBankInfoEntity::getStoreId, id))
                    .forEach(tmsStoreBankInfo -> {
                        TmsStoreBankInfoVo bankInfoVo = new TmsStoreBankInfoVo();
                        BeanUtils.copyProperties(tmsStoreBankInfo, bankInfoVo);
                        detailVo.getTmsStoreBankInfoVo().add(bankInfoVo);
                    });
            return detailVo;
        }
        return null;
    }

    @Override
    @Transactional
    public Boolean updateStoreById(TmsStoreDTO tmsStore) {
        if (tmsStore != null) {
            TmsStoreEntity tmsStoreEntity = new TmsStoreEntity();
            BeanUtils.copyProperties(tmsStore, tmsStoreEntity);
            List<TmsStoreBankInfoEntity> tmsStoreBankInfoList = tmsStore.getTmsStoreBankInfoList();
            if (CollUtil.isNotEmpty(tmsStoreBankInfoList)) {
                tmsStoreBankInfoService.updateBatchById(tmsStoreBankInfoList);
            }
            return this.updateById(tmsStoreEntity);
        }
        return Boolean.FALSE;
    }

    @Override
    public List<TmsStoreExcelVo> exportStore(TmsStorePageVo tmsStore) {
        MPJLambdaWrapper<TmsStoreEntity> wrapper = new MPJLambdaWrapper<TmsStoreEntity>()
                .selectAll(TmsStoreEntity.class)
                .like(StrUtil.isNotBlank(tmsStore.getStoreName()), TmsStoreEntity::getStoreName, tmsStore.getStoreName())
                .like(StrUtil.isNotBlank(tmsStore.getStoreCode()), TmsStoreEntity::getStoreCode, tmsStore.getStoreCode())
                .eq((tmsStore.getStatus() != null), TmsStoreEntity::getStatus, tmsStore.getStatus());
        return this.baseMapper.selectJoinList(TmsStoreExcelVo.class, wrapper);
    }

    @Override
    public List<TmsStoreEntity> getAllStoreEntity() {
        return baseMapper.getAllStoreEntity();
    }

    private SysUser saveUserInfo(String contactName, String contactPhone) {
        UserDTO sysUser = new UserDTO();
        // 设置部门信息
        SysDept dept = remoteDeptService.getDeptIdByName(CommonConstants.STORE_MANAGER_DEPT_NAME).getData();
        if (dept != null) {
            sysUser.setDeptId(dept.getDeptId());
        }
        // 获取角色信息
        List<SysRole> sysRoles = remoteRoleService.getAllRole().getData();
        SysRole sysRole = Optional.ofNullable(sysRoles)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> CommonConstants.STORE_MANAGER_ROLE_CODE.equals(item.getRoleCode()))
                .findFirst()
                .orElse(null);
        if (sysRole != null) {
            sysUser.setRole(Arrays.asList(sysRole.getRoleId()));
        }
        // 获取岗位信息
        List<SysPost> sysPosts = remotePostService.getAllPost().getData();
        SysPost sysPost = Optional.ofNullable(sysPosts)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> CommonConstants.STORE_MANAGER_POST_CODE.equals(item.getPostCode()))
                .findFirst()
                .orElse(null);
        if (sysPost != null) {
            sysUser.setPost(Arrays.asList(sysPost.getPostId()));
        }
        // 设置用户基本信息
        sysUser.setUsername(contactName);
        sysUser.setName(contactName);
        sysUser.setPhone(contactPhone);
        sysUser.setPassword("123456");
        sysUser.setLockFlag("0");
        remoteTmsUpmsService.addUser(sysUser);

        // 获取新增加的用户
        return remoteTmsUpmsService.getCustomerUserByPhone(contactPhone).getData();
    }
}
