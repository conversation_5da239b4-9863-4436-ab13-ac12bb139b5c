package com.jygjexp.jynx.tms.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsPayOrderEntity;
import com.jygjexp.jynx.tms.service.TmsOrderLogService;
import com.jygjexp.jynx.tms.service.TmsPayOrderService;
import com.jygjexp.jynx.tms.vo.PaymentResponseVo;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * IOT支付接口
 */
@Service
public class IOTPayQrOrder {


    @Autowired
    private TmsOrderLogService tmsOrderLogService;
    @Autowired
    private TmsPayOrderService tmsPayOrderService;

    private static final String MID = "11112637";
    private static final String KEY = "3CGmY08vbeBjer2iNamkrelswYQtyUzr";
    private static final String USERNAME = "Neighbexp";



    /**
     * 创建支付订单
     *
     * @param amount
     * @param payType
     * @param userId
     * @throws Exception
     */
    public  R payCreate(BigDecimal amount, String payType, Long userId) throws Exception {
        if (!isAtMostTwoDecimalPlaces(amount)) {
            return R.failed("金额不能小于等于0，不能超过两位小数");
        }
        //构建请求参数
        String url = "https://api.iotpaycloud.com/v1/create_order";
        String orderNo = "NB" + userId + "_" + System.currentTimeMillis();
        System.out.println("订单号：" + orderNo);
        Map<String, Object> map = new HashMap<>();
        map.put("mchId", MID);
        map.put("mchOrderNo", orderNo);
        map.put("channelId", payType);
        map.put("currency", "CAD");
        map.put("amount", amount.multiply(BigDecimal.valueOf(100)).intValue());
        map.put("clientIp", "127.0.0.1");
        map.put("device", "WEB");
        map.put("notifyUrl", "http://api.neighbourexpress.com/zt/api/payment/callback");
        map.put("subject", "积分");
        map.put("body", "积分充值");
        Map<String, Object> extra = new HashMap<>();
        extra.put("productId", "");
        map.put("extra", extra.toString());
        String sign = getSign(map, KEY);
        map.put("sign", sign);
        map.put("subject", URLEncoder.encode((String) map.get("subject"), "UTF-8"));
        map.put("body", URLEncoder.encode((String) map.get("body"), "UTF-8"));
        String params = JSON.toJSONString(map);
        String fullParams = "params=" + params;
        OkHttpClient client = new OkHttpClient();
        RequestBody formBody = RequestBody.create(fullParams, MediaType.get("application/x-www-form-urlencoded; charset=utf-8"));
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        tmsOrderLogService.saveLog(orderNo,1001,formBody.toString(),userId.toString());
        //返回响应结果
        try (Response response = client.newCall(request).execute()) {
            if (response.body() != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                String result = response.body().string();
                PaymentResponseVo paymentResponse = objectMapper.readValue(result, PaymentResponseVo.class);
                System.out.println("响应结果："+paymentResponse.toString());
                tmsPayOrderService.createPayOrder(new TmsPayOrderEntity(paymentResponse.getMchOrderNo(), userId, amount, payType,paymentResponse.getPayOrderId()));
                tmsOrderLogService.saveLog(orderNo,1001,result.toString(),userId.toString());
                return R.ok(result);
            } else {
                return R.failed("响应为空");
            }
        } catch (IOException e) {
            tmsOrderLogService.saveLog(orderNo,1001,"请求支付异常",userId.toString());
            return R.failed("请求失败: " + e.getMessage());
        }
    }


    public static void query() {
        // 构建参数 Map（不含 sign）
        Map<String, Object> params = new HashMap<>();
        params.put("mchId", MID);
        params.put("payOrderId", "WN20250725062306502816758348");

        // 生成签名
        String sign = getSign(params, KEY);
        params.put("sign", sign);

        // 将整个 params Map 序列化为 JSON 字符串
        String jsonParams = JSON.toJSONString(params);

        // 构建 formBody，参数名是 "params"，值是 JSON 字符串
        FormBody formBody = new FormBody.Builder()
                .add("params", jsonParams)
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url("https://api.iotpaycloud.com/v1/query_order")
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(formBody)
                .build();

        OkHttpClient client = new OkHttpClient();
        // 异步请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.err.println("请求失败: " + e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.body() != null) {
                    System.out.println("响应内容：" + response.body().string());
                } else {
                    System.out.println("响应为空");
                }
            }
        });
    }


    // === 以下是官方提供的签名方法整合 ===

    public static String getSign(Map<String, Object> map, String key) {
        ArrayList<String> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        String[] arrayToSort = list.toArray(new String[0]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (String s : arrayToSort) sb.append(s);
        sb.append("key=").append(key);
        return md5(sb.toString(), "UTF-8").toUpperCase();
    }

    public static String md5(String input, String charset) {
        try {
            byte[] data = input.getBytes(charset);
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(data);
            StringBuilder hex = new StringBuilder();
            for (byte b : digest) {
                String hexStr = Integer.toHexString(b & 0xff);
                if (hexStr.length() == 1) hex.append('0');
                hex.append(hexStr);
            }
            return hex.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5 签名失败", e);
        }
    }

    // 判断金额是否最多两位小数
    public static boolean isAtMostTwoDecimalPlaces(BigDecimal amount) {
        // 非空判断 + 金额必须大于 0 + 最多两位小数
        return amount != null
                && amount.compareTo(BigDecimal.ZERO) > 0
                && amount.scale() <= 2;
    }

    public static void main(String[] args) throws Exception {


    }


}
