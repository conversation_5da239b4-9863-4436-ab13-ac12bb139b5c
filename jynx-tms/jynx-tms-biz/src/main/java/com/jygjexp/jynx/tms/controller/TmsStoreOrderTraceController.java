package com.jygjexp.jynx.tms.controller;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.service.TmsStoreOrderTraceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门店订单轨迹跟踪
 *
 * <AUTHOR>
 * @date 2025-07-31 15:12:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreOrderTrace" )
@Tag(description = "tmsStoreOrderTrace" , name = "门店订单轨迹跟踪管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreOrderTraceController {

    private final  TmsStoreOrderTraceService service;

    /**
     * 根据主单号查询订单轨迹 - 支持主子单号查询
     * @param entrustedOrderNumber 查询门店订单轨迹
     * @return
     */
    @Operation(summary = "查询订单轨迹" , description = "查询订单轨迹" )
    @GetMapping("/getTrace" )
    // @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderTrace_view')" )
    public R getStoreOrderTraces(@RequestParam("entrustedOrderNumber") String entrustedOrderNumber) {
        return R.ok(service.getStoreOrderTraces(entrustedOrderNumber));
    }

}
