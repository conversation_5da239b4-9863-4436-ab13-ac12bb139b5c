package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsStoreEmployeeDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreEmployeeEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreEntity;
import com.jygjexp.jynx.tms.service.TmsStoreEmployeeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 门店员工表
 *
 * <AUTHOR>
 * @date 2025-07-11 14:00:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreEmployee" )
@Tag(description = "tmsStoreEmployee" , name = "门店员工表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreEmployeeController {

    private final  TmsStoreEmployeeService tmsStoreEmployeeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStoreEmployee 门店员工表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_view')" )
    public R<IPage<TmsStoreEmployeeEntity>> getTmsStoreEmployeePage(@ParameterObject Page page, @ParameterObject TmsStoreEmployeeEntity tmsStoreEmployee) {
        // 获取对应的门店ID
        return R.ok(tmsStoreEmployeeService.getPage(page, tmsStoreEmployee));
    }

    /**
     * 通过id查询门店员工表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStoreEmployeeService.getById(id));
    }

    /**
     * 新增门店员工表
     * @param tmsStoreEmployee 门店员工表
     * @return R
     */
    @Operation(summary = "新增门店员工表" , description = "新增门店员工表" )
    @SysLog("新增门店员工表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_add')" )
    public R save(@Valid @RequestBody TmsStoreEmployeeDTO tmsStoreEmployee) {
        return tmsStoreEmployeeService.saveEmployee(tmsStoreEmployee);
    }

    /**
     * 修改门店员工表
     * @param tmsStoreEmployee 门店员工表
     * @return R
     */
    @Operation(summary = "修改门店员工表" , description = "修改门店员工表" )
    @SysLog("修改门店员工表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_edit')" )
    public R updateById(@Valid@RequestBody TmsStoreEmployeeEntity tmsStoreEmployee) {
        return tmsStoreEmployeeService.updateEmployee(tmsStoreEmployee);
    }

    /**
     * 通过id删除门店员工表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店员工表" , description = "通过id删除门店员工表" )
    @SysLog("通过id删除门店员工表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return tmsStoreEmployeeService.removeBatchEmployee(ids);
    }

    /**
     * 禁用/启用员工
     */
    @Operation(summary = "禁用/启用员工", description = "禁用/启用员工")
    @SysLog("禁用/启用员工")
    @PutMapping("/updateStatus")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_status')")
    public R updateStatus(@RequestBody TmsStoreEmployeeEntity tmsStore) {
        return tmsStoreEmployeeService.updateStatus(tmsStore);
    }

    /**
     * 导出excel 表格
     * @param tmsStoreEmployee 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreEmployee_export')" )
    public List<TmsStoreEmployeeEntity> export(TmsStoreEmployeeEntity tmsStoreEmployee,Long[] ids) {
        return tmsStoreEmployeeService.list(Wrappers.lambdaQuery(tmsStoreEmployee).in(ArrayUtil.isNotEmpty(ids), TmsStoreEmployeeEntity::getId, ids));
    }
}