package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.*;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.app.api.dto.AppUserDTO;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.constants.LineHaulIsPickConstant;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.dto.app.TmsAppDeliveryOrderGroupDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsLmdDriverMapper;
import com.jygjexp.jynx.tms.mapper.TmsTransportTaskOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsVehicleInfoMapper;
import com.jygjexp.jynx.tms.model.bo.SendReviewBo;
import com.jygjexp.jynx.tms.mongo.entity.TmsLargeDriverRealTimeLocation;
import com.jygjexp.jynx.tms.mongo.service.TmsLargeDriverRealTimeLocationService;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.ALiYunSms;
import com.jygjexp.jynx.tms.utils.AliYunOSS;
import com.jygjexp.jynx.tms.utils.OrderTools;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.app.*;
import com.jygjexp.jynx.tms.vo.excel.TmsLmdDriverExcelVo;
import com.mongoplus.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * 中大件派送司机信息
 *
 * <AUTHOR>
 * @date 2025-04-02 20:15:09
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class TmsLmdDriverServiceImpl extends ServiceImpl<TmsLmdDriverMapper, TmsLmdDriverEntity> implements TmsLmdDriverService {

    private final TmsLmdDriverMapper driverMapper;
    private final RemoteTmsAppUserService remoteTmsAppUserService;
    private final TmsVehicleInfoMapper vehicleInfoMapper;
    private final TmsTransportTaskOrderMapper transportTaskOrderMapper;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsOrderTrackService orderTrackService;
    private final TmsInventoryManagementMapper inventoryManagementMapper;
    private final TmsStorageRecordMapper storageRecordMapper;
    private final TmsOutboundRecordMapper outboundRecordMapper;
    private final TmsLineHaulOrderMapper lineHaulOrderMapper;
    private final TmsOrderLineHaulRelationMapper tmsOrderLineHaulRelationMapper;
    private final TmsOverAreaService tmsOverAreaService;
    private final TmsBigAreaService tmsBigAreaService;
    private final TmsExceptionManagementMapper tmsExceptionManagementMapper;
    private final TmsSiteMapper tmsSiteMapper;
    private final TmsExceptionManagementMapper exceptionManagementMapper;
    private final TmsStorageandorderMapper storageandorderMapper;
    private final TmsCageAndOrderMapper cageAndOrderMapper;
    private final TmsLabelMapper labelMapper;
    private final TmsLabelService tmsLabelService;
    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final StringRedisTemplate stringRedisTemplate;
    private final TmsSmsLogMapper tmsSmsLogMapper;
    private final TmsSortingGridMapper tmsSortingGridMapper;
    private final TmsManualSortingRecordMapper tmsManualSortingRecordMapper;
    private final TmsWarehouseEmployeeMapper tmsWarehouseEmployeeMapper;
    private final TmsVehicleDriverRelationMapper vehicleDriverRelationMapper;
    private final TmsOrderBatchMapper tmsOrderBatchMapper;
    private final TmsDriverLocationMapper driverLocationMapper;
    private final TmsLargeDriverRealTimeLocationService tmsLargeDriverRealTimeLocationService;
    private final TmsReportManagementService tmsReportManagementService;
    private final TmsDeliveryFailedProofMapper tmsDeliveryFailedProofMapper;
    private final TmsReportAndOrderService reportAndOrderService;
    private final TmsStorageRecordService tmsStorageRecordService;
    private final TmsLmdSortingService tmsLmdSortingService;
    private final TmsOrderScanRecordService tmsOrderScanRecordService;

    // 自定义雪花 ID 生成器
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private static final String REDIS_REPORT_SEQ_KEY_PREFIX = "report:seq:";

    // 省份映射
    private static final Set<String> EAST_PROVINCES = new HashSet<>(Arrays.asList("ON", "PE", "QC", "NS", "NL", "Ontario", "Prince Edward Island", "Quebec","Nova Scotia", "Newfoundland and Labrador"));
    private static final Set<String> WEST_PROVINCES = new HashSet<>(Arrays.asList("SK", "AB", "BC", "Saskatchewan", "Alberta", "British Columbia"));

    // 分页查询
    @Override
    public Page<TmsLmdDriverPageVo> search(Page page, TmsLmdDriverPageVo vo) {
        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = getWrapper(vo, null);
        return driverMapper.selectJoinPage(page, TmsLmdDriverPageVo.class, wrapper);
    }

    // 新增司机
    @Override
    public R addLmdDriver(TmsLmdDriverEntity tmsLmdDriver) {
        // 新增之前需先判断手机号是否已存在
        if (!checkPhone(tmsLmdDriver.getPhone())) {
            return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsLmdDriver.getPhone());
        }

        // 判断邮箱和驾照号是否已存在
        if (!checkEmailAndIdNumber(tmsLmdDriver.getEmail(), tmsLmdDriver.getIdNumber())) {
            return LocalizedR.failed("tms.app.driver.register.email.and.ID.repetition",tmsLmdDriver.getEmail()+"-"+tmsLmdDriver.getIdNumber());
        }

        // 判断司机号是否重复
        if (!checkDriverNum(tmsLmdDriver.getDriverNum(),null)) {
            return LocalizedR.failed("tms.app.driver.register.driverNum.repetition", tmsLmdDriver.getDriverNum());
        }

        R<AppUserInfo> info = remoteTmsAppUserService.info(tmsLmdDriver.getPhone());
        if (info.getCode() == 0) {
            return LocalizedR.failed("tms.app.driver.add.phone.repetition", tmsLmdDriver.getPhone());
        }

        // 同步创建 APP 账号
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setRole(Collections.singletonList(1L));
        appUserDTO.setPhone(tmsLmdDriver.getPhone());
        appUserDTO.setUsername(tmsLmdDriver.getDriverName());
        appUserDTO.setNickname(tmsLmdDriver.getDriverName());
        appUserDTO.setName(tmsLmdDriver.getDriverName());
        appUserDTO.setPassword("123456");

        R saveAppUser = remoteTmsAppUserService.save(appUserDTO);
        if (saveAppUser.getCode() == 1) {
            return LocalizedR.failed("tms.app.driver.register.error", saveAppUser.getMsg());
        }

        // 生成对应的司机单号
        tmsLmdDriver.setDriverNum(generateDriverNum(tmsLmdDriver.getProvince()));

        // 保存司机信息
        boolean driverSaved = driverMapper.insert(tmsLmdDriver)>0;
        if (!driverSaved) {
            R<AppUserInfo> deleteInfo = remoteTmsAppUserService.info(tmsLmdDriver.getPhone());
            if (deleteInfo.getCode() == 0) {
                remoteTmsAppUserService.removeById(new Long[]{deleteInfo.getData().getAppUser().getUserId()});
            }
            return LocalizedR.failed("tms.app.driver.register.error", tmsLmdDriver.getDriverName());
        }
        return R.ok();
    }

    /**
     * 工号格式
     */
    private String generateDriverNum(String province) {
        int startNo;
        String prefix; // 方便校验
        List<String> provinces;
        if (EAST_PROVINCES.contains(province)) {
            startNo = 10001;
            prefix = "1";
            provinces = new ArrayList<>(EAST_PROVINCES);
        } else if (WEST_PROVINCES.contains(province)) {
            startNo = 20001;
            prefix = "2";
            provinces = new ArrayList<>(WEST_PROVINCES);
        } else {
            // 其他地区，使用随机5位号
            Random random = new Random();
            return String.format("%05d", random.nextInt(100000));
        }
        // 查询最新的合法司机号
        LambdaQueryWrapper<TmsLmdDriverEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TmsLmdDriverEntity::getProvince, provinces)
                .orderByDesc(TmsLmdDriverEntity::getCreateTime);
        // 不加limit，后面用stream找第一个合法的
        List<TmsLmdDriverEntity> drivers = this.list(wrapper);
        Integer nextNo = null;
        if (drivers != null) {
            for (TmsLmdDriverEntity driver : drivers) {
                String driverNo = driver.getDriverNum();
                // 司机号必须是数字且以指定前缀开头，且5位数起步
                if (driverNo != null
                        && driverNo.matches("^\\d+$")
                        && driverNo.startsWith(prefix)
                        && driverNo.length() == 5) {
                    nextNo = Integer.parseInt(driverNo) + 1;
                    break;
                }
            }
        }
        if (nextNo == null) {
            nextNo = startNo;
        }
        return String.valueOf(nextNo);
    }

    // 修改司机
    @Override
    public R updateLmdDriver(TmsLmdDriverEntity tmsLmdDriver) {
        // 修改之前需先判断手机号是否已存在
        if (!checkPhoneUpdate(tmsLmdDriver.getPhone(), tmsLmdDriver.getDriverId())){
            return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsLmdDriver.getPhone());
        }

        // 判断司机号是否重复
        if (!checkDriverNum(tmsLmdDriver.getDriverNum(),tmsLmdDriver.getDriverId())) {
            return LocalizedR.failed("tms.app.driver.register.driverNum.repetition", tmsLmdDriver.getDriverNum());
        }

        // 先查出司机信息
        TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectById(tmsLmdDriver.getDriverId());

        // 同步修改app账号
        R<AppUserInfo> info = remoteTmsAppUserService.info(tmsDriverEntity.getPhone());
        if (info.getCode()==1){
            return R.failed("Account does not exist");
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setPhone(tmsLmdDriver.getPhone());
        appUserDTO.setNewPhone(tmsDriverEntity.getPhone());
        appUserDTO.setUsername(tmsLmdDriver.getDriverName());
        appUserDTO.setNickname(tmsLmdDriver.getDriverName());
        appUserDTO.setName(tmsLmdDriver.getDriverName());
        appUserDTO.setUserId(info.getData().getAppUser().getUserId());
        R r = remoteTmsAppUserService.updateById(appUserDTO);
        if (r.getCode()==1){
            return LocalizedR.failed("tms.app.driver.update.error", "");
        }
        return R.ok(driverMapper.updateById(tmsLmdDriver));
    }

    // 司机启用停用
    @Override
    public R LmdbBusinessSwitch(Long driverId, Integer isValid) {
        if (null != driverId && null != isValid)
        {
            TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectById(driverId);
            if (isValid == 1){
                // 启用之前需先判断手机号是否已存在
                if (!checkPhone(tmsDriverEntity.getPhone())){
                    return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsDriverEntity.getPhone());
                }
            }
            tmsDriverEntity.setIsValid(isValid);
            tmsDriverEntity.setIsOpen(Boolean.FALSE);
            driverMapper.updateById(tmsDriverEntity);
        }
        return R.ok(Boolean.TRUE);
    }

    // 校验司机手机号，邮箱是否存在
    private Boolean checkPhone(String phone) {
        TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getPhone, phone)
                .eq(TmsLmdDriverEntity::getIsValid, true), false);
        if (null != tmsDriverEntity){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    // 校验司机号是否重复
    private Boolean checkDriverNum(String driverNum,Long driverId){
        TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverNum, driverNum)
                .ne(ObjectUtil.isNotNull(driverId),TmsLmdDriverEntity::getDriverId, driverId), false);
        if (null != tmsDriverEntity){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    // 校验司机邮箱，驾照号是否存在
    private Boolean checkEmailAndIdNumber(String email, String idNumber) {
        // 查询是否存在相同的邮箱或驾照号，且 isValid 为 true
        TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .and(wrapper -> wrapper
                        .eq(TmsLmdDriverEntity::getIsValid, true)
                        .and(w -> w.eq(TmsLmdDriverEntity::getIdNumber, idNumber)
                                .or().eq(TmsLmdDriverEntity::getEmail, email))
                ), false);

        return tmsDriverEntity == null;
    }

    // 修改校验司机手机号是否存在
    private Boolean checkPhoneUpdate(String phone, Long driverId) {
        TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getPhone, phone)
                .eq(TmsLmdDriverEntity::getIsValid, true)
                .ne(TmsLmdDriverEntity::getDriverId, driverId), false);
        if (null != tmsDriverEntity){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    // 中大件司机注册
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R register(TmsAppLmdDriverVo tmsAppDriverVo) {
        // 先校验司机手机号是否重复
        Boolean isExist = driverMapper.selectCount(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getPhone, tmsAppDriverVo.getPhone()).eq(TmsLmdDriverEntity::getIsValid, true)) > 0;
        if (isExist) {
            return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsAppDriverVo.getPhone());
        }

        // 判断邮箱和身份证号是否已存在
        if (!checkEmailAndIdNumber(tmsAppDriverVo.getEmail(), tmsAppDriverVo.getIdNumber())) {
            return LocalizedR.failed("tms.app.driver.register.email.and.ID.repetition",tmsAppDriverVo.getEmail()+"-"+tmsAppDriverVo.getIdNumber());
        }

        //生成对应的司机号
        String driverNum = generateDriverNum(tmsAppDriverVo.getProvince());

        // 判断司机号是否重复
//        if (!checkDriverNum(driverNum,null)) {
//            return LocalizedR.failed("tms.app.driver.register.driverNum.repetition", tmsAppDriverVo.getDriverNum());
//        }
        try {
            // 中大件司机信息新增
            TmsLmdDriverEntity driver = new TmsLmdDriverEntity();
            //BeanUtil.copyProperties(tmsAppDriverVo, driver);
            driver.setCarrierId(tmsAppDriverVo.getCarrierId());
            driver.setDriverName(tmsAppDriverVo.getDriverName());
            driver.setPhone(tmsAppDriverVo.getPhone());
            driver.setIdNumber(tmsAppDriverVo.getIdNumber());
            driver.setEmergencyName(tmsAppDriverVo.getEmergencyName());
            driver.setEmergencyPhone(tmsAppDriverVo.getEmergencyPhone());
            driver.setLicenseType(tmsAppDriverVo.getLicenseType());
            driver.setLicenseTimeStart(tmsAppDriverVo.getLicenseTimeStart());
            driver.setLicenseTimeEnd(tmsAppDriverVo.getLicenseTimeEnd());
            driver.setHomeAddress(tmsAppDriverVo.getHomeAddress());
            driver.setIdCardFront(tmsAppDriverVo.getIdCardFront());
            driver.setIdCardBack(tmsAppDriverVo.getIdCardBack());
            driver.setDrivingLicenseFront(tmsAppDriverVo.getDrivingLicenseFront());
            driver.setDrivingLicenseBack(tmsAppDriverVo.getDrivingLicenseBack());
            driver.setOtherQualification(tmsAppDriverVo.getOtherQualification());

            driver.setCountry(tmsAppDriverVo.getCountry());
            driver.setProvince(tmsAppDriverVo.getProvince());
            driver.setRegionId(tmsAppDriverVo.getRegionId());
            driver.setZip(tmsAppDriverVo.getZip());
            driver.setEmail(tmsAppDriverVo.getEmail());
            driver.setSocialSecurityNumber(tmsAppDriverVo.getSocialSecurityNumber());
            driver.setHomeAddress(tmsAppDriverVo.getHomeAddress());
            driver.setWorkType(tmsAppDriverVo.getWorkType());
            driver.setDriverType(tmsAppDriverVo.getDriverType());
            driver.setOrgCode(tmsAppDriverVo.getOrgCode());
            driver.setTransportNumber(tmsAppDriverVo.getTransportNumber());
            driver.setBankAccount(tmsAppDriverVo.getBankAccount());
            driver.setDriverNum(driverNum);
            int driverResult = driverMapper.insert(driver);
            if (driverResult <= 0) {
                throw new RuntimeException("Failed to add driver information.");
            }

            // 车辆信息新增
            TmsVehicleInfoEntity vehicleInfo = new TmsVehicleInfoEntity();
            vehicleInfo.setCarrierId(tmsAppDriverVo.getCarrierId());
            vehicleInfo.setDriverId(driver.getDriverId());
            vehicleInfo.setContactPhone(tmsAppDriverVo.getPhone());
            vehicleInfo.setLicensePlate(tmsAppDriverVo.getLicensePlate());
            vehicleInfo.setVehicleType(tmsAppDriverVo.getVehicleType());
            vehicleInfo.setLength(tmsAppDriverVo.getLength());
            vehicleInfo.setWidth(tmsAppDriverVo.getWidth());
            vehicleInfo.setHeight(tmsAppDriverVo.getHeight());
            vehicleInfo.setVehicleImageUrl(tmsAppDriverVo.getVehicleImageUrl());
            vehicleInfo.setInsuranceStartDate(tmsAppDriverVo.getInsuranceStartDate());
            vehicleInfo.setInsuranceEndDate(tmsAppDriverVo.getInsuranceEndDate());
            vehicleInfo.setCargoType(tmsAppDriverVo.getCargoType());
            vehicleInfo.setWarehouseHeight(tmsAppDriverVo.getWarehouseHeight());
            vehicleInfo.setWarehouseLength(tmsAppDriverVo.getWarehouseLength());
            vehicleInfo.setWarehouseWidth(tmsAppDriverVo.getWarehouseWidth());
            vehicleInfo.setOwnershipType(tmsAppDriverVo.getOwnershipType());
            int vehicleResult = vehicleInfoMapper.insert(vehicleInfo);
            if (vehicleResult <= 0) {
                throw new RuntimeException("Failed to add vehicle information.");
            }

            //将司机与车辆绑定中间表
            TmsVehicleDriverRelationEntity relation = new TmsVehicleDriverRelationEntity();
            relation.setDriverId(driver.getDriverId());
            relation.setVehicleId(vehicleInfo.getId());
            int relationResult = vehicleDriverRelationMapper.insert(relation);
            return R.ok(Boolean.TRUE);
        } catch (Exception e) {
            throw new RuntimeException("Driver registration failure: " + e.getMessage(), e); // 确保事务回滚
        }
    }

    // 根据手机号获取信息
    @Override
    public R getPhone(String phone) {
        TmsMiniAppVo miniAppVo = new TmsMiniAppVo();

        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<TmsLmdDriverEntity>()
                .selectAll(TmsLmdDriverEntity.class)
                .selectAll(TmsVehicleInfoEntity.class)
                .leftJoin(TmsVehicleDriverRelationEntity.class,TmsVehicleDriverRelationEntity::getDriverId, TmsLmdDriverEntity::getDriverId)
                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId)
                .eq(TmsLmdDriverEntity::getPhone, phone);
        //.eq(TmsLmdDriverEntity::getIsValid, true);

        TmsAppLmdDriverVo tmsLmdDriverEntity = driverMapper.selectJoinOne(TmsAppLmdDriverVo.class, wrapper);
        miniAppVo.setDriver(tmsLmdDriverEntity);

        // 根据手机号查询仓库人员信息
        TmsWarehouseEmployeeEntity tmsWarehouseEmployeeEntity = tmsWarehouseEmployeeMapper.selectOne(new LambdaQueryWrapper<TmsWarehouseEmployeeEntity>()
                .eq(TmsWarehouseEmployeeEntity::getMobile, phone),false);
        //.eq(TmsWarehouseEmployeeEntity::getIsValid, true), false);

        miniAppVo.setWarehouseEmployee(tmsWarehouseEmployeeEntity);

        Long userId = SecurityUtils.getUser().getId();    // 获取用户ID
        R user = remoteTmsAppUserService.infoById(userId);
        // 根据手机号查询账号是否有仓储权限
        if (user.getCode()!=1){
            miniAppVo.setUserName(user.getData().toString());

            // 获取角色信息
            AppUserInfo appUserInfo = (AppUserInfo) user.getData();
            List<Long> rolesList = Arrays.asList(appUserInfo.getRoles());
            List<String> roleCodeList = Arrays.asList(appUserInfo.getRoleCode());
            miniAppVo.setRolesList(rolesList);
            miniAppVo.setRoleCodesList(roleCodeList);
        }
        return R.ok(miniAppVo);
    }

    // 中大件司机修改个人信息
    @Override
    public R driverInfoUpdate(TmsAppDriverPersonVo driverInfo) {

        // 校验车辆信息字段是否为空
        if (ObjectUtil.isNotNull(driverInfo.getVehicleType()) || StrUtil.isNotBlank(driverInfo.getLicensePlate())
                || StrUtil.isNotBlank(driverInfo.getCargoType()) || ObjectUtil.isNotNull(driverInfo.getInsuranceStartDate())
                || ObjectUtil.isNotNull(driverInfo.getInsuranceEndDate())) {
            // 修改保存车辆信息
            TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, driverInfo.getDriverId()));

            // 校验是否存在车辆信息
            if (ObjectUtil.isNull(tmsVehicleDriverRelation)) {
                return LocalizedR.failed("tms.app.driver.update.not.vehicle.info", "");
            }

            TmsVehicleInfoEntity vehicleInfo = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                    .eq(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()), false);
            vehicleInfo.setVehicleType(driverInfo.getVehicleType());
            vehicleInfo.setLicensePlate(driverInfo.getLicensePlate());
            vehicleInfo.setCargoType(driverInfo.getCargoType());
            vehicleInfo.setInsuranceStartDate(driverInfo.getInsuranceStartDate());
            vehicleInfo.setInsuranceEndDate(driverInfo.getInsuranceStartDate());
            vehicleInfoMapper.updateById(vehicleInfo);
        }


        if (driverInfo != null) {
            // 判断手机号是否重复
            TmsLmdDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                    .eq(TmsLmdDriverEntity::getPhone, driverInfo.getPhone())
                    .eq(TmsLmdDriverEntity::getIsValid, true)
                    .ne(TmsLmdDriverEntity::getDriverId, driverInfo.getDriverId()), false);
            if (ObjectUtil.isNotNull(tmsDriverEntity)) {
                return LocalizedR.failed("tms.app.driver.register.phone.repetition", driverInfo.getPhone());
            }
            //修改app账号信息
            R<AppUserInfo> info = remoteTmsAppUserService.info(driverInfo.getPhone());
            if (info.getCode() == 1) {
                return R.failed("Account does not exist");
            }
            AppUserDTO appUserDTO = new AppUserDTO();
            appUserDTO.setPhone(driverInfo.getPhone());
            appUserDTO.setNewPhone(driverInfo.getPhone());
            appUserDTO.setUsername(driverInfo.getDriverName());
            //appUserDTO.setRole(Collections.singletonList(1l));
            appUserDTO.setUserId(info.getData().getAppUser().getUserId());
            appUserDTO.setAvatar(driverInfo.getDriverAvatar());
            R r = remoteTmsAppUserService.updateDriverInfo(appUserDTO);
            if (r.getCode() == 1) {
                return LocalizedR.failed("tms.app.driver.update.error", "");
            }
            TmsLmdDriverEntity driverInfoEntity = new TmsLmdDriverEntity();
            BeanUtil.copyProperties(driverInfo, driverInfoEntity);
            driverMapper.updateById(driverInfoEntity);
            return R.ok(Boolean.TRUE);
        }
        return R.failed(Boolean.FALSE);
    }

    // app仓库个人信息修改
    @Override
    public R warehouseInfoUpdate(TmsAppWarehousePersonVo warehouseInfo) {
        if (warehouseInfo != null) {
            // 判断手机号是否重复
            TmsWarehouseEmployeeEntity tmsWarehouseEntity = tmsWarehouseEmployeeMapper.selectOne(new LambdaQueryWrapper<TmsWarehouseEmployeeEntity>()
                    .eq(TmsWarehouseEmployeeEntity::getMobile, warehouseInfo.getMobile())
                    .eq(TmsWarehouseEmployeeEntity::getIsValid, true)
                    .ne(TmsWarehouseEmployeeEntity::getEmployeeId, warehouseInfo.getEmployeeId()), false);
            if (ObjectUtil.isNotNull(tmsWarehouseEntity)) {
                return LocalizedR.failed("tms.app.warehouse.employee.exist", warehouseInfo.getRealname()+warehouseInfo.getMobile());
            }
            //修改app账号信息
            R<AppUserInfo> info = remoteTmsAppUserService.info(warehouseInfo.getMobile());
            if (info.getCode() == 1) {
                return R.failed("Account does not exist");
            }
            AppUserDTO appUserDTO = new AppUserDTO();
            appUserDTO.setPhone(warehouseInfo.getMobile());
            appUserDTO.setNewPhone(warehouseInfo.getMobile());
            appUserDTO.setUsername(warehouseInfo.getRealname());
            //appUserDTO.setRole(Collections.singletonList(4l));
            appUserDTO.setUserId(info.getData().getAppUser().getUserId());
            appUserDTO.setAvatar(warehouseInfo.getWarehouseAvatar());
            R r = remoteTmsAppUserService.updateDriverInfo(appUserDTO);
            if (r.getCode() == 1) {
                return LocalizedR.failed("tms.app.warehouse.employee.update.error", "");
            }
            TmsWarehouseEmployeeEntity warehouseInfoEntity = new TmsWarehouseEmployeeEntity();
            BeanUtil.copyProperties(warehouseInfo, warehouseInfoEntity);
            tmsWarehouseEmployeeMapper.updateById(warehouseInfoEntity);
            return R.ok(Boolean.TRUE);
        }
        return R.failed(Boolean.FALSE);
    }

    // 司机审核
    @Override
    public R audit(TmsLmdDriverEntity tmsLmdDriverEntity) {
        if (null == tmsLmdDriverEntity) {
            return R.failed("Please check whether the information is complete.");
        }
        tmsLmdDriverEntity.setAuditStatus(tmsLmdDriverEntity.getAuditStatus());
        tmsLmdDriverEntity.setAuditTime(LocalDateTime.now());
        if (tmsLmdDriverEntity.getAuditStatus() == 1){
            // 审核驳回将账号禁用
            tmsLmdDriverEntity.setIsValid(0);
        }
        return R.ok(driverMapper.updateById(tmsLmdDriverEntity) > 0);
    }

    // 中大件司机列表导出
    @Override
    public List<TmsLmdDriverExcelVo> getDriverExcel(TmsLmdDriverPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = getWrapper(vo, ids);
        List<TmsLmdDriverExcelVo> list = driverMapper.selectJoinList(TmsLmdDriverExcelVo.class, wrapper);

        // 司机类型字典
        Map<String, String> driverTypeMap = new HashMap<>();
        driverTypeMap.put("0", "干线");
        driverTypeMap.put("1", "卡派");
        driverTypeMap.put("2", "中大件");

        // 遍历转换
        for (TmsLmdDriverExcelVo voItem : list) {
            String driverTypeStr = voItem.getDriverType();
            if (StrUtil.isNotBlank(driverTypeStr)) {
                String converted = Arrays.stream(driverTypeStr.split(","))
                        .map(String::trim)
                        .map(code -> driverTypeMap.getOrDefault(code, code))  // 找不到默认原值
                        .collect(Collectors.joining(","));
                voItem.setDriverType(converted);
            }
        }
        return list;
    }

    // 图片上传工具接口
    @Override
    public R uploadFile(MultipartFile file, String dir, Long groupId, String type) {
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("bucketName", "nbexpress");
        resultMap.put("fileName", fileName);

        String fileUrl = null;
        try {
            // 调用 sendToOss 方法上传文件到 OSS 并获取文件 URL
            fileUrl = AliYunOSS.sendToOssTwo(file, dir, fileName);
            // 设置返回结果的 URL
            resultMap.put("url", fileUrl);
        } catch (Exception e) {
            log.error("上传失败", e);
            return R.failed(e.getLocalizedMessage());
        }
        return R.ok(resultMap);
    }

    // web端司机揽收上传取货证明
    @Override
    public R webUploadPickupProof(TmsAppDriverDeliveryVo vo) {
        // 根据跟踪单号查询跟踪单信息
        TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber()), false);

        if (null == tmsCustomerOrder) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", vo.getEntrustedOrderNumber());
        }

        // 如果没有上传提货操作，只上传送货证明操作则需要拦截
        if (StrUtil.isBlank(tmsCustomerOrder.getPickupProof()) && StrUtil.isNotBlank(vo.getDeliveryProof())
                && StrUtil.isBlank(tmsCustomerOrder.getCollectionPickupProof())) {
            return LocalizedR.failed("tms.web.collent.upload.error", tmsCustomerOrder.getEntrustedOrderNumber());
        }

        // 获取揽收任务信息   -- 用来判断该次上传的任务是否需要修改状态，还是只补充图片
        TmsTransportTaskOrderEntity tmsTransportTaskOrder = transportTaskOrderMapper.selectOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getPTaskOrder()), false);


        // 如果上传取货证明的情况
        if (StrUtil.isNotBlank(vo.getPickupProof())) {

            // 如果任务状态为运输中 | 已完成  则只需要补充图片不需要执行其他逻辑
            if (tmsTransportTaskOrder.getTaskStatus() == TransportTaskStatus.IN_TRANSIT.getCode() || tmsTransportTaskOrder.getTaskStatus() == TransportTaskStatus.DELIVERED.getCode()){
                tmsCustomerOrder.setCollectionPickupProof(vo.getPickupProof());
                customerOrderMapper.updateById(tmsCustomerOrder);
                return R.ok(Boolean.TRUE);
            }

            tmsCustomerOrder.setCollectionPickupProof(vo.getPickupProof());
            // 取货成功修改订单状态   修改跟踪单为待运输
            tmsCustomerOrder.setOrderStatus(NewOrderStatus.AWAITING_TRANSPORTATION.getCode());
            int b = customerOrderMapper.updateById(tmsCustomerOrder);
            // 更新子单状态为待运输
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                    .eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode()));

            // 将任务单状态改为配送中
            transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                    .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getPTaskOrder())
                    .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()));

            // 记录揽收提货轨迹
            orderTrackService.saveTrack(vo.getEntrustedOrderNumber(), tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.AWAITING_TRANSPORTATION.getValue(),OrderTrackLink.AWAITING_TRANSPORTATION.getCode());
        }

        // 如果上传送货证明的情况
        if (StrUtil.isNotBlank(vo.getDeliveryProof())) {
            // 如果任务状态为已完成  则只需要补充图片不需要执行其他逻辑
            if (tmsTransportTaskOrder.getTaskStatus() == TransportTaskStatus.DELIVERED.getCode()){
                tmsCustomerOrder.setCollectionDeliveryProof(vo.getDeliveryProof());
                customerOrderMapper.updateById(tmsCustomerOrder);
                return R.ok(Boolean.TRUE);
            }

            tmsCustomerOrder.setCollectionDeliveryProof(vo.getDeliveryProof());
            boolean b = customerOrderMapper.updateById(tmsCustomerOrder) > 0;
            // 记录揽收送货完成轨迹
            orderTrackService.saveTrack(vo.getEntrustedOrderNumber(), tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.AWAITING_TRANSPORTATION.getValue(),
                    OrderTrackLink.TWO_COLLECTION_WAREHOUSE_RECEIVED.getCode());

            // 查询该揽收任务下是否所有订单都已经上传揽收送货凭证
            boolean allProofUploaded = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getPTaskOrder, tmsCustomerOrder.getPTaskOrder())
                    .isNull(TmsCustomerOrderEntity::getCollectionDeliveryProof)) == 0;

            //todo 订单揽收完成将订单扫描标识改为未扫描，给后面派送扫描做准备
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                    .set(TmsCustomerOrderEntity::getIsScan, Boolean.FALSE));

            // 如果没有未变成未上传送货证明的订单，则将揽收任务单状态改为已完成
            if (allProofUploaded) {
                transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                        .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getPTaskOrder())
                        .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
            }
        }
        return R.ok(Boolean.TRUE);
    }

    // web端司机派送上传取货证明
    @Override
    public R webDeliveryUploadPickupProof(TmsAppDriverDeliveryVo vo) {
        // 根据跟踪单号查询跟踪单信息
        TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber()), false);

        if (null == tmsCustomerOrder) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", vo.getEntrustedOrderNumber());
        }

        // 获取派送任务信息   -- 用来判断该次上传的任务是否需要修改状态，还是只补充图片
        TmsTransportTaskOrderEntity tmsTransportTaskOrder = transportTaskOrderMapper.selectOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getDTaskOrder()), false);

        // 如果上传取货证明的情况
        if (StrUtil.isNotBlank(vo.getPickupProof())) {
            tmsCustomerOrder.setPickupProof(vo.getPickupProof());
            customerOrderMapper.updateById(tmsCustomerOrder);
        }

        // 如果上传送货证明的情况
        if (StrUtil.isNotBlank(vo.getDeliveryProof())) {
            // 如果任务状态为已完成  则只需要补充图片不需要执行其他逻辑
            if (tmsTransportTaskOrder.getTaskStatus() == TransportTaskStatus.DELIVERED.getCode()){
                tmsCustomerOrder.setDeliveryProof(vo.getDeliveryProof());
                customerOrderMapper.updateById(tmsCustomerOrder);
                return R.ok(Boolean.TRUE);
            }


            tmsCustomerOrder.setDeliveryProof(vo.getDeliveryProof());
            tmsCustomerOrder.setOrderStatus(NewOrderStatus.COMPLETED.getCode());
            // 记录订单完成时间
            tmsCustomerOrder.setFinishTime(LocalDateTime.now());
            customerOrderMapper.updateById(tmsCustomerOrder);

            // 更新子单状态为已完成
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                    .eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode()));

            // 记录派送送货完成轨迹
            orderTrackService.saveTrack(vo.getEntrustedOrderNumber(), tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.COMPLETED.getValue(),OrderTrackLink.DELIVERED.getCode());

            if (StrUtil.isNotBlank(tmsCustomerOrder.getReceiverPhone())) {
                // 暂存短信模版
                String template = "Your parcel ({tracking number}) has been delivered by your trusted delivery partner, " +
                        "Neighbour Express. Please see details at {tracking url}. " +
                        "If you have NOT received your package, please contact us at {support url}.";

                template = template.replace("{tracking number}", tmsCustomerOrder.getEntrustedOrderNumber())
                        .replace("{tracking url}", "https://www.neighbourexpress.com/")
                        .replace("{support url}", "https://www.neighbourexpress.com/");
                // 派送完成给客户发送短信
                R r = ALiYunSms.sentMes(tmsCustomerOrder.getReceiverPhone(), template);
                // 记录短信日志
                TmsSmsLogEntity smsLogEntity = new TmsSmsLogEntity();
                smsLogEntity.setMobile(tmsCustomerOrder.getReceiverPhone());
                smsLogEntity.setTemplateParam(template);
                smsLogEntity.setSendStatus(r.isOk() ? 1 : 0);
                smsLogEntity.setCreatedTime(LocalDateTime.now());
                smsLogEntity.setUpdatedTime(LocalDateTime.now());
                smsLogEntity.setResponseMsg(r.getMsg());
                tmsSmsLogMapper.insert(smsLogEntity);
                //smsLogEntity.setIsDomestic(1);
                //smsLogEntity.setResponseCode("200");
            }

            // 判断派送任务单是否还有未变成已完成的订单
            boolean hasUnfinishedOrders = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getDTaskOrder, tmsCustomerOrder.getDTaskOrder())
                    .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())) > 0;

            // 如果没有未变成已完成的订单，则将派送任务单状态改为已完成
            if (!hasUnfinishedOrders) {
                transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                        .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getDTaskOrder())
                        .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
            }

            // 完成派送，更新批次管理的已送达数
            TmsOrderBatchEntity orderBatch = tmsOrderBatchMapper.selectOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                    .eq(TmsOrderBatchEntity::getBatchNo, tmsCustomerOrder.getBatchNo()), false);
            if (orderBatch != null) {
                // 实时统计已完成订单数量
                int newDeliveredCount = orderBatch.getDeliveredCount() + 1;
                int newNonDeliveredCount = Math.max(orderBatch.getOrderCount() - newDeliveredCount, 0);

                // 更新字段
                orderBatch.setDeliveredCount(newDeliveredCount);
                orderBatch.setNonDeliveryCount(newNonDeliveredCount);
                tmsOrderBatchMapper.updateById(orderBatch);
            }
        }
        return R.ok(Boolean.TRUE);
    }

    // 根据单号获取揽收/派送订单POD
    @Override
    public R getOrderPOD(String orderNo) {
        TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo), false);
        return R.ok(tmsCustomerOrder);
    }

    // 根据状态查询揽收/派送订单
    @Override
    public R getOrderListByStatus(Integer isTask, Integer status, Long driverId) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<>();
        if (isTask == 1) {
            wrapper.selectAll(TmsTransportTaskOrderEntity.class)
                    //.selectAll(TmsCustomerOrderEntity.class)
                    .selectAs(TmsCustomerOrderEntity::getEntrustedOrderNumber,TmsTransportTaskOrderDto.Fields.entrustedOrderNumber)
                    .selectAs(TmsCustomerOrderEntity::getOrderStatus,TmsTransportTaskOrderDto.Fields.orderStatus)
                    .selectAs(TmsCustomerOrderEntity::getShipperName,TmsTransportTaskOrderDto.Fields.shipperName)
                    .selectAs(TmsCustomerOrderEntity::getShipperAddress,TmsTransportTaskOrderDto.Fields.shipperAddress)
                    .selectAs(TmsCustomerOrderEntity::getCargoQuantity,TmsTransportTaskOrderDto.Fields.cargoQuantity)
                    .selectAs(TmsCustomerOrderEntity::getTotalWeight,TmsTransportTaskOrderDto.Fields.totalWeight)
                    .selectAs(TmsCustomerOrderEntity::getTotalVolume,TmsTransportTaskOrderDto.Fields.totalVolume)
                    .selectAs(TmsCustomerOrderEntity::getShipperLatLng,TmsTransportTaskOrderDto.Fields.shipperLatLng)
                    .selectAs(TmsCustomerOrderEntity::getReceiverLatLng,TmsTransportTaskOrderDto.Fields.receiverLatLng)
                    .selectAs(TmsCustomerOrderEntity::getCargoType, TmsTransportTaskOrderDto.Fields.cargoType)
                    .selectAs(TmsCustomerOrderEntity::getShipperPhone, TmsTransportTaskOrderDto.Fields.shipperPhone)
                    .selectAs(TmsCustomerOrderEntity::getReceiverPhone, TmsTransportTaskOrderDto.Fields.receiverPhone)
                    .selectAs(TmsCustomerOrderEntity::getIsOneTicketMany, TmsTransportTaskOrderDto.Fields.isOneTicketMany);
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getPTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo);
            wrapper.eq(TmsTransportTaskOrderEntity::getTaskType, 1);  // 表示揽收订单
        } else {
            wrapper.selectAll(TmsTransportTaskOrderEntity.class)
                    .selectCount(TmsTransportTaskOrderEntity::getId, TmsTransportTaskOrderDto.Fields.sumVote)
                    .selectCount(TmsCustomerOrderEntity::getId, TmsTransportTaskOrderDto.Fields.sumNumber)
                    .selectSum(TmsTransportTaskOrderEntity::getTotalWeight, TmsTransportTaskOrderDto.Fields.sumWeight)
                    .selectSum(TmsTransportTaskOrderEntity::getTotalVolume, TmsTransportTaskOrderDto.Fields.sumVolume)
                    .groupBy(TmsTransportTaskOrderEntity::getTaskOrderNo);
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getDTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo);
            wrapper.eq(TmsTransportTaskOrderEntity::getTaskType, 2);  // 表示派送订单
        }

        wrapper.eq(TmsTransportTaskOrderEntity::getTaskStatus, status)
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime);

        List<TmsTransportTaskOrderDto> tmsTransportTaskOrderDtos = transportTaskOrderMapper.selectJoinList(TmsTransportTaskOrderDto.class, wrapper);
        return R.ok(tmsTransportTaskOrderDtos);
    }

    // 查询派送配送失败列表
    @Override
    public R getDeliveryFailedList(TmsAppDeliveryFailedListVo vo) {
        //查出司机对应的任务单号列表
        List<String> taskOrderNoList = transportTaskOrderMapper.selectList(
                new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                        .eq(TmsTransportTaskOrderEntity::getDriverId, vo.getDriverId())
                        .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, vo.getBatchNo())
        ).stream().map(TmsTransportTaskOrderEntity::getTaskOrderNo).collect(Collectors.toList());
        if (CollUtil.isEmpty(taskOrderNoList)) {
            return R.ok(Collections.emptyList());
        }
        // 查出这些任务单号下的主单
        List<String> mainOrderNos = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getDTaskOrder, taskOrderNoList)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false) // 主单
        ).stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());

        if (CollUtil.isEmpty(mainOrderNos)) {
            return R.ok(Collections.emptyList());
        }

        // 查出这些主单下的子单，且状态为失败
        List<TmsCustomerOrderEntity> failedSubOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getSubFlag, true) // 子单
                        .eq(TmsCustomerOrderEntity::getOrderStatus, vo.getStatus())
                .and(w -> {
                    for (String mainNo : mainOrderNos) {
                        w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                    }
                })
        );

        return R.ok(failedSubOrders);
    }

    /**
     * 地图派送运输中和待提货列表
     * @param driverId
     * @return
     */
    @Override
    public R getDeliveryOrderList(Long driverId) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsTransportTaskOrderEntity.class)
                .selectCount(TmsTransportTaskOrderEntity::getId, TmsTransportTaskOrderDto.Fields.sumVote)
                .selectCount(TmsCustomerOrderEntity::getId, TmsTransportTaskOrderDto.Fields.sumNumber)
                .selectSum(TmsTransportTaskOrderEntity::getTotalWeight, TmsTransportTaskOrderDto.Fields.sumWeight)
                .selectSum(TmsTransportTaskOrderEntity::getTotalVolume, TmsTransportTaskOrderDto.Fields.sumVolume)
                .groupBy(TmsTransportTaskOrderEntity::getTaskOrderNo)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getDTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo)
                .eq(TmsTransportTaskOrderEntity::getTaskType, 2)  // 表示派送订单
                .in(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode(), TransportTaskStatus.IN_TRANSIT.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime);

        List<TmsTransportTaskOrderDto> tmsTransportTaskOrderDtos = transportTaskOrderMapper.selectJoinList(TmsTransportTaskOrderDto.class, wrapper);
        return R.ok(tmsTransportTaskOrderDtos);
    }

    // 派送、揽收异常上报
    @Override
    public R reporting(TmsAppExceptionManagementVo tmsExceptionManagement) {
        //判空
        if (ObjectUtil.isNull(tmsExceptionManagement)) {
            return LocalizedR.failed("tms.exception.reporting.content.is.empty", Optional.ofNullable(null));
        }

        // 查询司机信息
        TmsLmdDriverEntity tmsLmdDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverId, tmsExceptionManagement.getDriverId())
                .or()
                .eq(TmsLmdDriverEntity::getDriverName, tmsExceptionManagement.getDriverName()), false);

        if (ObjectUtil.isNull(tmsLmdDriverEntity)) {
            return LocalizedR.failed("上报司机不能为空");
        }
        // 查询车牌信息
        TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()), false);
        TmsVehicleInfoEntity vehicleInfo = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                .eq(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()), false);

        tmsExceptionManagement.setDriverName(tmsLmdDriverEntity.getDriverName());
        tmsExceptionManagement.setLicensePlate(vehicleInfo.getLicensePlate());
        // 根据规则生成异常单号
        String exceptionOrderNo = generateExceptionId();
        tmsExceptionManagement.setExceptionOrderNo(exceptionOrderNo);
        tmsExceptionManagement.setExceptionTime(LocalDateTime.now());
        if (exceptionManagementMapper.insert(tmsExceptionManagement) <= 0) {
            return LocalizedR.failed("tms.exception.reporting.failed", Optional.ofNullable(null));
        }
        return R.ok(Boolean.TRUE);
    }

    // 生成单号(规则：EX前缀+日期+随机四位数+001递增序号)
    private String generateExceptionId() {
        // 前缀
        String prefix = "EX";

        // 日期部分
        String datePart = new SimpleDateFormat("yyyyMMdd").format(new Date());

        // 随机 4 位数字
        String randomPart = RandomUtil.randomNumbers(4);

        // 从 Redis 获取当天的序号，并递增
        String key = "exception_seq:" + datePart;
        Long seq = stringRedisTemplate.opsForValue().increment(key, 1);

        // 格式化序号为三位数（001, 002...）
        String seqPart = String.format("%03d", seq);

        // 组合异常单号
        return prefix + datePart + randomPart + seqPart;
    }

    // 根据任务单号查询详情
    @Override
    public R getOrderDetailByTaskNo(String taskNo) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<TmsTransportTaskOrderEntity>()
                .selectAll(TmsTransportTaskOrderEntity.class)
                .select(TmsTransportTaskOrderEntity::getTaskOrderNo)
                .select(TmsTransportTaskOrderEntity::getTaskStatus)
                .select(TmsTransportTaskOrderEntity::getDestination)
                .select(TmsTransportTaskOrderEntity::getReceiverName)
                .select(TmsTransportTaskOrderEntity::getReceiverPhone)
                .select(TmsTransportTaskOrderEntity::getEstimatedArrivalTimeStart)
                .select(TmsTransportTaskOrderEntity::getEstimatedArrivalTimeEnd)
                .select(TmsTransportTaskOrderEntity::getRemark)
                .select(TmsTransportTaskOrderEntity::getIsTailgatePickup)
                .select(TmsTransportTaskOrderEntity::getOrderType)
                .select(TmsTransportTaskOrderEntity::getTotalWeight)
                .select(TmsTransportTaskOrderEntity::getTotalVolume)
                .select(TmsTransportTaskOrderEntity::getCargoQuantity)
                .select(TmsCustomerOrderEntity::getPickupProof)   // 上传提货证明
                .select(TmsCustomerOrderEntity::getDeliveryProof)  // 上传送货证明
                .select(TmsCustomerOrderEntity::getIsScan)  // 扫描标识
                .select(TmsCargoInfoEntity::getCargoDescription)    // 货物描述
                .select(TmsExceptionManagementEntity::getExceptionTime)       // 异常上报时间
                .select(TmsExceptionManagementEntity::getExceptionLocation)   // 异常上报地点
                .select(TmsExceptionManagementEntity::getExceptionType)     // 异常类型
                .select(TmsExceptionManagementEntity::getExceptionDescription)  // 异常描述
                .select(TmsExceptionManagementEntity::getHandlingPlan) // 处理方案
                .select(TmsExceptionManagementEntity::getImageUrls) // 异常上报图片
                //.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsTransportTaskOrderEntity::getEntrustedOrderNumber)
                .leftJoin(TmsCargoInfoEntity.class, TmsCargoInfoEntity::getCustomerOrderNumber, TmsTransportTaskOrderEntity::getCustomerOrderNumber)
                .leftJoin(TmsExceptionManagementEntity.class, TmsExceptionManagementEntity::getDispatchOrderNo, TmsTransportTaskOrderEntity::getTaskOrderNo)
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, taskNo);
        List<TmsTransportTaskOrderDetailDto> tmsTransportTaskOrderDtos = transportTaskOrderMapper.selectJoinList(TmsTransportTaskOrderDetailDto.class, wrapper);
        return R.ok(tmsTransportTaskOrderDtos);
    }


    // 根据跟踪单号查询订单详情
    @Override
    public R getOrderNoDetai(String entrustedOrder, Integer isTask) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .selectAll(TmsCustomerOrderEntity.class)
                .select(TmsExceptionManagementEntity::getExceptionTime)       // 异常上报时间
                .select(TmsExceptionManagementEntity::getExceptionLocation)   // 异常上报地点
                .select(TmsExceptionManagementEntity::getExceptionType)     // 异常类型
                .select(TmsExceptionManagementEntity::getExceptionDescription)  // 异常描述
                .select(TmsExceptionManagementEntity::getHandlingPlan) // 处理方案
                .select(TmsExceptionManagementEntity::getImageUrls) // 异常上报图片
                .leftJoin(TmsExceptionManagementEntity.class, TmsExceptionManagementEntity::getDispatchOrderNo, TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrder);
        List<TmsCustomerOrderEntity> customerOrderDtos = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper);
        return R.ok(customerOrderDtos);
    }

    // 派送根据跟踪单号查询主子单详情
    @Override
    public R getDeliveryOrderNoDetail(String entrustedOrder) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .selectAll(TmsCustomerOrderEntity.class)
                .select(TmsExceptionManagementEntity::getExceptionTime)       // 异常上报时间
                .select(TmsExceptionManagementEntity::getExceptionLocation)   // 异常上报地点
                .select(TmsExceptionManagementEntity::getExceptionType)     // 异常类型
                .select(TmsExceptionManagementEntity::getExceptionDescription)  // 异常描述
                .select(TmsExceptionManagementEntity::getHandlingPlan) // 处理方案
                .select(TmsExceptionManagementEntity::getImageUrls) // 异常上报图片
                .leftJoin(TmsExceptionManagementEntity.class, TmsExceptionManagementEntity::getDispatchOrderNo, TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrder);
        List<TmsCustomerOrderEntity> customerOrderDtos = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper);
        return R.ok(customerOrderDtos);
    }

    // 派送地图根据批次号查询全部主子单详情
    @Override
    public R getDeliveryOrderNoAll(String taskNo) {
        // 查询该批次下的所有主单
        List<TmsCustomerOrderEntity> mainOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDTaskOrder, taskNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false)
        );

        if (mainOrders.isEmpty()) {
            return R.ok(Collections.emptyList());
        }

        // 提取主单号集合
        Set<String> mainOrderNos = mainOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toSet());

        // 查询所有子单（模糊匹配：likeRight 主单号）
        MPJLambdaWrapper<TmsCustomerOrderEntity> subWrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .selectAll(TmsCustomerOrderEntity.class)
                .select(TmsExceptionManagementEntity::getExceptionTime)
                .select(TmsExceptionManagementEntity::getExceptionLocation)
                .select(TmsExceptionManagementEntity::getExceptionType)
                .select(TmsExceptionManagementEntity::getExceptionDescription)
                .select(TmsExceptionManagementEntity::getHandlingPlan)
                .select(TmsExceptionManagementEntity::getImageUrls)
                .leftJoin(TmsExceptionManagementEntity.class, TmsExceptionManagementEntity::getDispatchOrderNo, TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .eq(TmsCustomerOrderEntity::getSubFlag, true);

        subWrapper.and(w -> {
            for (String mainNo : mainOrderNos) {
                w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
            }
        });

        List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, subWrapper);

        // 子单按主单号前缀分组
        Map<String, List<TmsCustomerOrderEntity>> subOrderMap = new HashMap<>();
        for (TmsCustomerOrderEntity sub : subOrders) {
            String subNo = sub.getEntrustedOrderNumber();
            for (String mainNo : mainOrderNos) {
                if (subNo.startsWith(mainNo)) {
                    subOrderMap.computeIfAbsent(mainNo, k -> new ArrayList<>()).add(sub);
                    break;
                }
            }
        }

        // 主单组装返回结构
        List<TmsAppDeliveryOrderGroupDto> resultList = new ArrayList<>();
        for (TmsCustomerOrderEntity main : mainOrders) {
            TmsAppDeliveryOrderGroupDto dto = new TmsAppDeliveryOrderGroupDto();
            BeanUtils.copyProperties(main, dto);
            dto.setSubOrders(subOrderMap.getOrDefault(main.getEntrustedOrderNumber(), new ArrayList<>()));
            resultList.add(dto);
        }

        return R.ok(resultList);
    }


    // app-揽收-消息通知获取详情
    @Override
    public R getMessageOrderNoDetai(String orderNo) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .selectAll(TmsCustomerOrderEntity.class)
                .select(TmsExceptionManagementEntity::getExceptionTime)       // 异常上报时间
                .select(TmsExceptionManagementEntity::getExceptionLocation)   // 异常上报地点
                .select(TmsExceptionManagementEntity::getExceptionType)     // 异常类型
                .select(TmsExceptionManagementEntity::getExceptionDescription)  // 异常描述
                .select(TmsExceptionManagementEntity::getHandlingPlan) // 处理方案
                .select(TmsExceptionManagementEntity::getImageUrls) // 异常上报图片
                .leftJoin(TmsExceptionManagementEntity.class, TmsExceptionManagementEntity::getDispatchOrderNo, TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .eq(TmsCustomerOrderEntity::getPTaskOrder, orderNo);
        List<TmsCustomerOrderEntity> customerOrderDtos = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper);
        if (CollUtil.isNotEmpty(customerOrderDtos)){
            return R.ok(customerOrderDtos);
        }
        return R.failed(50006,"未找到对应揽收任务单");
    }

    // 根据任务单号查询跟踪单详情列表
    @Override
    public R getDetailByTaskNo(String taskNo, Integer isTask) {
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime)
        ;
        if (isTask == 1) {
            wrapper.leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getPTaskOrder);
            wrapper.eq(TmsTransportTaskOrderEntity::getTaskType, 1);  // 表示揽收订单
            wrapper.eq(TmsTransportTaskOrderEntity::getTaskOrderNo, taskNo);
        } else {
            wrapper.leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder);
            wrapper.eq(TmsTransportTaskOrderEntity::getTaskType, 2);  // 表示派送订单
            wrapper.eq(TmsTransportTaskOrderEntity::getTaskOrderNo, taskNo);
        }
        List<TmsCustomerOrderEntity> customerOrderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper);
        return R.ok(customerOrderList);
    }

    /**
     * 查询所有司机信息
     */
    @Override
    public R getDriverinfo(Page page, TmsScheduleDriverDto tmsScheduleDriverDto) {
        LambdaQueryWrapper<TmsLmdDriverEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(tmsScheduleDriverDto.getDriverName()), TmsLmdDriverEntity::getDriverName, tmsScheduleDriverDto.getDriverName())
                .like(ObjectUtil.isNotNull(tmsScheduleDriverDto.getDriverType()), TmsLmdDriverEntity::getDriverType, tmsScheduleDriverDto.getDriverType())
                .eq(ObjectUtil.isNotNull(tmsScheduleDriverDto.getDriverStatus()), TmsLmdDriverEntity::getIsOpen, tmsScheduleDriverDto.getDriverStatus())
                .like(StrUtil.isNotBlank(tmsScheduleDriverDto.getPhone()), TmsLmdDriverEntity::getPhone, tmsScheduleDriverDto.getPhone())
                .eq(ObjectUtil.isNotNull(tmsScheduleDriverDto.getRegionId()), TmsLmdDriverEntity::getRegionId, tmsScheduleDriverDto.getRegionId())
                //不要纯卡派类型的司机
                .ne(TmsLmdDriverEntity::getDriverType, 1)
                .orderByDesc(TmsLmdDriverEntity::getCreateTime);
        List<TmsScheduleDriverVo> tmsScheduleDriverVos = new ArrayList<>();
        Page newPage = page(page, wrapper);
        List<TmsLmdDriverEntity> records = newPage.getRecords();
        if (ObjectUtil.isNotNull(records) && !records.isEmpty()) {
            // List<Long> regionIds = records.stream().map(TmsLmdDriverEntity::getRegionId).collect(Collectors.toList());

            // todo: 因为现在司机区域变成可多选，变成了String类型存储，通过逗号分割
            List<Long> regionIds = records.stream()
                    .flatMap(record -> Arrays.stream(record.getRegionId().split(",")))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            //统计司机的订单数
            //查询所有待提货、运输中状态的揽收任务
            Map<Long, List<TmsTransportTaskOrderEntity>> collectTaskMap = transportTaskOrderMapper.selectList(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskType, 1)
                            .isNotNull(TmsTransportTaskOrderEntity::getDriverId)
                            .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode())
                            .or()
                            .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()))
                    .stream()
                    .collect(Collectors.groupingBy(TmsTransportTaskOrderEntity::getDriverId));
            //查询所有待提货、运输中状态的派送任务
            Map<Long, List<TmsTransportTaskOrderEntity>> deliveryTaskMap = transportTaskOrderMapper.selectList(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskType, 2)
                            .isNotNull(TmsTransportTaskOrderEntity::getDriverId)
                            .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode())
                            .or()
                            .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()))
                    .stream()
                    .collect(Collectors.groupingBy(TmsTransportTaskOrderEntity::getDriverId));

            //查询所有待提货、运输中状态的派送任务
            Map<Long, List<TmsLineHaulOrderEntity>> lineHaulOrderMap = lineHaulOrderMapper.selectList(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                            .isNotNull(TmsLineHaulOrderEntity::getDriverId)
                            .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode())
                            .or()
                            .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()))
                    .stream()
                    .collect(Collectors.groupingBy(TmsLineHaulOrderEntity::getDriverId));

            //揽收任务订单数
            //查询所有待揽收、运输中状态的揽收任务
            if (ObjectUtil.isNotNull(regionIds) && !regionIds.isEmpty()) {
                Map<Long, TmsBigAreaEntity> overAreaMap = tmsBigAreaService.list(new LambdaQueryWrapper<TmsBigAreaEntity>()
                        .in(TmsBigAreaEntity::getId, regionIds)).stream().collect(Collectors.toMap(TmsBigAreaEntity::getId, Function.identity()));
                records.forEach(item -> {
                    Set<TmsCustomerOrderEntity> customerOrders = new HashSet<>();
                    //该司机的揽收任务数
                    if(ObjectUtil.isNotNull(collectTaskMap.get(item.getDriverId()))){
                        List<String> collectTaskNos = collectTaskMap.get(item.getDriverId()).stream()
                                .map(TmsTransportTaskOrderEntity::getTaskOrderNo).collect(Collectors.toList());
                        if(ObjectUtil.isNotNull(collectTaskNos) && !collectTaskNos.isEmpty()){
                            List<TmsCustomerOrderEntity> customerOrderEntities = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .in(TmsCustomerOrderEntity::getPTaskOrder, collectTaskNos));
                            if(ObjectUtil.isNotNull(customerOrderEntities) && !customerOrderEntities.isEmpty()){
                                customerOrders.addAll(customerOrderEntities);
                            }
                        }
                    }
                    //该司机的派送任务数
                    if(ObjectUtil.isNotNull(deliveryTaskMap.get(item.getDriverId()))){
                        List<String> deliveryTaskNos = deliveryTaskMap.get(item.getDriverId()).stream()
                                .map(TmsTransportTaskOrderEntity::getTaskOrderNo).collect(Collectors.toList());
                        if(ObjectUtil.isNotNull(deliveryTaskNos) && !deliveryTaskNos.isEmpty()){
                            List<TmsCustomerOrderEntity> customerOrderEntities = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .in(TmsCustomerOrderEntity::getDTaskOrder, deliveryTaskNos));
                            if(ObjectUtil.isNotNull(customerOrderEntities) && !customerOrderEntities.isEmpty()){
                                customerOrders.addAll(customerOrderEntities);
                            }
                        }
                    }
                    //干线任务订单数
                    if(ObjectUtil.isNotNull(lineHaulOrderMap.get(item.getDriverId()))){
                        List<String> lineHaulTaskNos = lineHaulOrderMap.get(item.getDriverId()).stream()
                                .map(TmsLineHaulOrderEntity::getLineHaulNo).collect(Collectors.toList());
                        if(ObjectUtil.isNotNull(lineHaulTaskNos) && !lineHaulTaskNos.isEmpty()){
                            List<String> entrustedOrderNumbers = tmsOrderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
                                            .in(TmsOrderLineHaulRelationEntity::getLineHaulNo, lineHaulTaskNos)).stream()
                                    .map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toList());
                            if(ObjectUtil.isNotNull(entrustedOrderNumbers) && !entrustedOrderNumbers.isEmpty()){
                                List<TmsCustomerOrderEntity> customerOrderEntities =customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
                                customerOrders.addAll(customerOrderEntities);
                            }

                        }
                    }
                    TmsScheduleDriverVo tmsScheduleDriverVo = new TmsScheduleDriverVo();
                    tmsScheduleDriverVo.setDriverId(item.getDriverId());
                    tmsScheduleDriverVo.setDriverType(item.getDriverType());
                    tmsScheduleDriverVo.setDriverName(item.getDriverName());
                    tmsScheduleDriverVo.setDriverNum(item.getDriverNum());
                    tmsScheduleDriverVo.setDriverStatus(item.getIsOpen());
                    // TODO 暂时适配区域多选数据
                    String[] split = item.getRegionId().split(",");
                    if (split.length > 0) {
                        if (ObjectUtil.isNotNull(overAreaMap.get(Long.valueOf(split[0])))) {
                            tmsScheduleDriverVo.setRegionName(overAreaMap.get(Long.valueOf(split[0])).getAreaName());
                        }
                    }
                    tmsScheduleDriverVo.setPhone(item.getPhone());
                    tmsScheduleDriverVo.setOrderCount(customerOrders.size());
                    tmsScheduleDriverVos.add(tmsScheduleDriverVo);
                });
            }
            return R.ok(newPage.setRecords(tmsScheduleDriverVos));
        } else {
            return R.ok(newPage.setRecords(tmsScheduleDriverVos));
        }
    }

    private MPJLambdaWrapper getWrapper(TmsLmdDriverPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.like(StrUtil.isNotBlank(vo.getCarrierCode()), TmsCarrierEntity::getCarrierCode, vo.getCarrierCode())
                .like(StrUtil.isNotBlank(vo.getCarrierName()), TmsCarrierEntity::getCarrierName, vo.getCarrierName())
                .like(StrUtil.isNotBlank(vo.getDriverName()), TmsLmdDriverPageVo::getDriverName, vo.getDriverName())
                .like(StrUtil.isNotBlank(vo.getDriverNum()), TmsLmdDriverPageVo::getDriverNum, vo.getDriverNum())
                .eq(ObjUtil.isNotNull(vo.getIsValid()), TmsLmdDriverPageVo::getIsValid, vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getPhone()), TmsLmdDriverPageVo::getPhone, vo.getPhone())
                .eq(ObjUtil.isNotNull(vo.getWorkType()), TmsLmdDriverPageVo::getWorkType, vo.getWorkType())
                .like(ObjUtil.isNotNull(vo.getDriverType()), TmsLmdDriverPageVo::getDriverType, vo.getDriverType())
                .eq(ObjUtil.isNotNull(vo.getAuditStatus()), TmsLmdDriverPageVo::getAuditStatus, vo.getAuditStatus())
                .selectAll(TmsLmdDriverPageVo.class)
                .select(TmsCarrierEntity::getCarrierName)
                .leftJoin(TmsCarrierEntity.class, TmsCarrierEntity::getCarrierId, TmsLmdDriverPageVo::getCarrierId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsLmdDriverPageVo::getDriverId, ids)
                .orderByDesc(TmsLmdDriverPageVo::getDriverId);
        return wrapper;
    }


    // app司机扫描取货
    @Override
    public R scanPick(String entrustedOrderNo, Long driverId, Integer isTask) {
        entrustedOrderNo=  isCustomerOrder(entrustedOrderNo,false);
        // 判断entrustedOrderNo输入是否小于15位，如果小于则报错
        if (entrustedOrderNo.length() <= 15) {
            return R.failed("系统不存在此面单/The system does not have this waybill");
        }

        // 截取掉单号后三位，提取出主单号
        String mainNo = entrustedOrderNo.substring(0, entrustedOrderNo.length() - 3);
        // 根据任务单号查询司机信息，检查该订单司机是否与当前司机有关联
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .select(TmsCustomerOrderEntity::getDeliveryOrderSortNo)
                .select(TmsCustomerOrderEntity::getDTaskOrder)
                .select(TmsCustomerOrderEntity::getOrderStatus)
                .select(TmsCustomerOrderEntity::getCollectionPickupProof)
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo)
                .eq(TmsTransportTaskOrderEntity::getTaskType, isTask)
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId);

        // 排除用户输入不存在的子单
        Long subCount = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>().eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNo));
        if (subCount == 0) {
            return LocalizedR.failed("tms.app.sub.order.not.exist", "");
        }

        if (isTask.equals(TaskType.COLLECTION.getCode())) {
            wrapper.leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getPTaskOrder);
        } else {
            wrapper.leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder);
        }
        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectJoinOne(TmsCustomerOrderEntity.class, wrapper);

        if (ObjectUtil.isNull(customerOrder)) {
            // 不属于本次取货范围
            return R.failed(25111,LocalizedR.getMessage("tms.app.driver.scan.pick.error", new Object[]{entrustedOrderNo}));
        }

        if (isTask.equals(TaskType.COLLECTION.getCode())){
            // 判断揽收订单如果已经提货则不允许扫描
            if (StrUtil.isNotBlank(customerOrder.getCollectionPickupProof())) {
                return LocalizedR.failed("tms.app.canceled.order.not.scan", customerOrder.getEntrustedOrderNumber());
            }
        }else{
            // 判断派送订单已完成则不可扫描
            if (customerOrder.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())) {
                return LocalizedR.failed("tms.app.completed.order.not.scan", customerOrder.getEntrustedOrderNumber());
            }
        }

        // 获取对应批次任务
        TmsTransportTaskOrderEntity taskOrder = transportTaskOrderMapper.selectOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, customerOrder.getDTaskOrder()));


        TmsCustomerOrderEntity isScan = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNo), false);
        // 判断 扫描状态是否为已扫描
        if (isScan.getIsScan()) {
            if(isTask.equals(TaskType.DELIVERY.getCode()) && isScan.getOrderStatus().equals(NewOrderStatus.AWAITING_DELIVERY.getCode())){
                // 说明此时该面单已经提货，不允许再扫描提货
                return LocalizedR.failed("tms.app.driver.waybill.scan.error", isScan.getEntrustedOrderNumber());
            }
            String deliveryInfo = customerOrder.getDeliveryOrderSortNo() + "-" + (taskOrder != null ? taskOrder.getId() : "other");
            return R.ok(deliveryInfo, "Already picked up");
        }

        // 扫描成功后修改跟踪单扫描状态为已扫描
        int update = customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNo)
                .set(TmsCustomerOrderEntity::getIsScan, true));

        if (update > 0) {
            // **根据子单号提取主单号**
            String mainOrderNo = entrustedOrderNo.substring(0, 13);  // 提取前 13 位作为主单号
            // 判断主单是否还有无未扫描的子单号
            long count = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo) // 匹配所有以 mainOrderNo 开头的子单
                    .eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .ne(TmsCustomerOrderEntity::getIsScan, true));
            if (count == 0) {
                // 如果没有未扫描的子单，则修改主单状态为已扫描
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false)
                        .set(TmsCustomerOrderEntity::getIsScan, true));
            }
        }
        // 拼接返回顺序号
        String returnInfo = customerOrder.getDeliveryOrderSortNo() + "-" + (taskOrder != null ? taskOrder.getId() : "other");
        return R.ok(returnInfo);
    }



    // app 司机上传取货证明
    @Override
    public R uploadPickupProof(String entrustedOrderNumber, String pickupProof, Integer isTask) {
        entrustedOrderNumber= isCustomerOrder(entrustedOrderNumber,true);
        // 根据跟踪单号查询跟踪单信息
        TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber), false);

        if (null == tmsCustomerOrder) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", entrustedOrderNumber);
        }

        if (isTask == 1) {
            // 判断跟踪单的扫描状态是否为已扫描
            if (!tmsCustomerOrder.getIsScan()) {
                return LocalizedR.failed("tms.app.driver.order.scan", entrustedOrderNumber);
            }

            if (StrUtil.isNotBlank(pickupProof)) {
                tmsCustomerOrder.setCollectionPickupProof(pickupProof);
            }
            // 取货成功修改订单状态   修改跟踪单为待运输
            tmsCustomerOrder.setOrderStatus(NewOrderStatus.AWAITING_TRANSPORTATION.getCode());
            int b = customerOrderMapper.updateById(tmsCustomerOrder);
            if (b > 0) {
                // 更新子单状态为待运输
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
                        .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode()));

                // 将任务单状态改为配送中
                transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                        .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getPTaskOrder())
                        .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()));

                // 记录揽收提货轨迹
                orderTrackService.saveTrack(entrustedOrderNumber, tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.AWAITING_TRANSPORTATION.getValue(),OrderTrackLink.AWAITING_TRANSPORTATION.getCode());
            } else {
                return R.failed(Boolean.FALSE);
            }
        } else if (isTask == 2) {
            if (StrUtil.isNotBlank(pickupProof)) {
                tmsCustomerOrder.setPickupProof(pickupProof);
                int b = customerOrderMapper.updateById(tmsCustomerOrder);
            }

        }

        return R.ok(Boolean.TRUE);
    }


    // 司机送货成功
    @Override
    public R deliverySuccess(String entrustedOrderNumber, String deliveryProof, Integer isTask) {
        entrustedOrderNumber= isCustomerOrder(entrustedOrderNumber,true);
        // 根据跟踪单号查询跟踪单信息
        TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber), false);

        if (null == tmsCustomerOrder) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", entrustedOrderNumber);
        }

        if (isTask.equals(TaskType.COLLECTION.getCode())) {
            // 判断订单是否存在揽收提货证明，校验一下该订单是否已经提货
            if (StrUtil.isBlank(tmsCustomerOrder.getCollectionPickupProof())) {
                return LocalizedR.failed("tms.app.driver.order.pickup.proof.not.exist", entrustedOrderNumber);
            }

            if (StrUtil.isNotBlank(deliveryProof)) {
                tmsCustomerOrder.setCollectionDeliveryProof(deliveryProof);
            }
            boolean b = customerOrderMapper.updateById(tmsCustomerOrder) > 0;
            if (b) {
                // 记录揽收送货完成轨迹
                orderTrackService.saveTrack(entrustedOrderNumber, tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.AWAITING_TRANSPORTATION.getValue(), OrderTrackLink.TWO_COLLECTION_WAREHOUSE_RECEIVED.getCode());

                // 查询该揽收任务下是否所有订单都已经上传揽收送货凭证
/*                boolean allProofUploaded = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getPTaskOrder, tmsCustomerOrder.getPTaskOrder())
                        .isNull(TmsCustomerOrderEntity::getCollectionDeliveryProof)) == 0;*/

                //todo 订单揽收完成将订单扫描标识改为未扫描，给后面派送扫描做准备
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                        .set(TmsCustomerOrderEntity::getIsScan, Boolean.FALSE));

                // 如果没有未变成未上传送货证明的订单，则将揽收任务单状态改为已完成
/*                if (allProofUploaded) {*/
                    transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getPTaskOrder())
                            .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));

                    // 揽收任务完成执行自动入库操作
                    // Boolean pIsTrue = automaticWarehousing(tmsCustomerOrder);
                //}
            }
        } else if (isTask.equals(TaskType.DELIVERY.getCode())) {
            // 判断订单状态是否大于待收货，校验一下该订单是否已经提货
            if (tmsCustomerOrder.getOrderStatus() < NewOrderStatus.AWAITING_DELIVERY.getCode()) {
                return LocalizedR.failed("tms.app.driver.order.pickup.proof.not.exist", entrustedOrderNumber);
            }

            if (StrUtil.isNotBlank(deliveryProof)) {
                tmsCustomerOrder.setDeliveryProof(deliveryProof);
            }
            tmsCustomerOrder.setOrderStatus(NewOrderStatus.COMPLETED.getCode());
            // 记录订单完成时间
            tmsCustomerOrder.setFinishTime(LocalDateTime.now());
            boolean b = customerOrderMapper.updateById(tmsCustomerOrder) > 0;
            if (b) {
                // 更新子单状态为已完成
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
                        .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode()));

                // 记录派送送货完成轨迹
                orderTrackService.saveTrack(entrustedOrderNumber, tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.COMPLETED.getValue(),OrderTrackLink.DELIVERED.getCode());

                if (StrUtil.isNotBlank(tmsCustomerOrder.getReceiverPhone())) {
                    // 暂存短信模版
                    String template="Your parcel ({tracking number}) has been delivered by your trusted delivery partner, " +
                            "Neighbour Express. Please see details at {tracking url}. " +
                            "If you have NOT received your package, please contact us at {support url}.";

                    template=template.replace("{tracking number}", tmsCustomerOrder.getEntrustedOrderNumber())
                            .replace("{tracking url}", "https://www.neighbourexpress.com/")
                            .replace("{support url}", "https://www.neighbourexpress.com/");
                    // 派送完成给客户发送短信
                    R r = ALiYunSms.sentMes(tmsCustomerOrder.getReceiverPhone(), template);
                    // 记录短信日志
                    TmsSmsLogEntity smsLogEntity = new TmsSmsLogEntity();
                    smsLogEntity.setMobile(tmsCustomerOrder.getReceiverPhone());
                    smsLogEntity.setTemplateParam(template);
                    smsLogEntity.setSendStatus(r.isOk()?1:0);
                    smsLogEntity.setCreatedTime(LocalDateTime.now());
                    smsLogEntity.setUpdatedTime(LocalDateTime.now());
                    smsLogEntity.setResponseMsg(r.getMsg());
                    tmsSmsLogMapper.insert(smsLogEntity);
                    //smsLogEntity.setIsDomestic(1);
                    //smsLogEntity.setResponseCode("200");

                }

                // 判断派送任务单是否还有未变成已完成的订单
                boolean hasUnfinishedOrders = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDTaskOrder, tmsCustomerOrder.getDTaskOrder())
                        .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())) > 0;

                // 如果没有未变成已完成的订单，则将派送任务单状态改为已完成
                if (!hasUnfinishedOrders) {
                    transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getDTaskOrder())
                            .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
                }

                // 完成派送，更新批次管理的已送达数
                TmsOrderBatchEntity orderBatch = tmsOrderBatchMapper.selectOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                        .eq(TmsOrderBatchEntity::getBatchNo, tmsCustomerOrder.getBatchNo()), false);
                if (orderBatch != null) {
                    // 实时统计已完成订单数量
                    int newDeliveredCount = orderBatch.getDeliveredCount() + 1;
                    int newNonDeliveredCount = Math.max(orderBatch.getOrderCount() - newDeliveredCount, 0);

                    // 更新字段
                    orderBatch.setDeliveredCount(newDeliveredCount);
                    orderBatch.setNonDeliveryCount(newNonDeliveredCount);
                    tmsOrderBatchMapper.updateById(orderBatch);
                }

            }
        }
        return R.ok(Boolean.TRUE);
    }

    //判断当前单号是否是客户单号，如果是客户单号，根据需要返回跟踪单号或者面单号
    @Override
    public String isCustomerOrder(String entrustedOrderNumber,Boolean needEntrustedOrderNumberFlag) {
        if (StrUtil.isBlank(entrustedOrderNumber)){
            return entrustedOrderNumber;
        }
        if (entrustedOrderNumber.toUpperCase().startsWith("N")) {
            return entrustedOrderNumber;
        }
        TmsCustomerOrderEntity order = tmsCustomerOrderService.getByOrderNumber(entrustedOrderNumber);
        if (order==null || order.getEntrustedOrderNumber()!=null && order.getIsCustomerLabel()){
            assert order != null;
            return needEntrustedOrderNumberFlag? OrderTools.safeSubstringOrder(order.getEntrustedOrderNumber()):order.getEntrustedOrderNumber() ;
        }
        return entrustedOrderNumber;
    }

    //app-派送司机（子单纬度）配送成功
    @Override
    public R subOrderListSuccess(TmsAppDriverSubOrderDeliveryVo vo) {
        processOrderNumbersInPlace(vo.getSubOrderList(), false);
        List<String> subOrders = vo.getSubOrderList();
        String deliveryProof = vo.getDeliveryProof();
        // 根据子单号查询订单信息
        List<TmsCustomerOrderEntity> subOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrders)
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0"));

        if (CollUtil.isEmpty(subOrderList)) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", subOrderList);
        }


        // ========== 提前校验订单状态 ==========
        List<String> notPickedOrders = new ArrayList<>();
        List<String> alreadyCompletedOrders = new ArrayList<>();
        List<String> undeliveredOrders = new ArrayList<>();

        for (TmsCustomerOrderEntity subOrder : subOrderList) {
            int status = subOrder.getOrderStatus();
            if (status < NewOrderStatus.AWAITING_DELIVERY.getCode()) {
                notPickedOrders.add(subOrder.getEntrustedOrderNumber());
            } else if (status == NewOrderStatus.COMPLETED.getCode()) {
                alreadyCompletedOrders.add(subOrder.getEntrustedOrderNumber());
            } else if (status == NewOrderStatus.WAREHOUSE_RECEIVE.getCode()) {
                undeliveredOrders.add(subOrder.getEntrustedOrderNumber());
            }
        }

        if (!notPickedOrders.isEmpty() || !alreadyCompletedOrders.isEmpty()) {
            StringBuilder message = new StringBuilder();

            if (!notPickedOrders.isEmpty()) {
                message.append("以下订单未提货，不能签收：")
                        .append(String.join(", ", notPickedOrders))
                        .append("。");
            }

            if (!alreadyCompletedOrders.isEmpty()) {
                message.append("以下订单状态，不能重复操作：")
                        .append(String.join(", ", alreadyCompletedOrders))
                        .append("。");
            }

            if (!undeliveredOrders.isEmpty()) {
                message.append("以下订单还未提货,不能进行操作：")
                        .append(String.join(", ", undeliveredOrders))
                        .append("。");
            }

            return R.failed(message.toString());
        }

        // 遍历每一个子单，逐个更新状态
        Set<String> mainOrderSet = new HashSet<>();  // 用于记录关联主单号
        for (TmsCustomerOrderEntity subOrder : subOrderList) {

            // 判断订单状态是否等于仓库收件，校验一下该订单是否已经提货
            if (subOrder.getOrderStatus().equals(NewOrderStatus.WAREHOUSE_RECEIVE.getCode())) {
                return LocalizedR.failed("tms.app.driver.order.pickup.proof.not.exist", subOrder.getEntrustedOrderNumber());
            }

            if (StrUtil.isNotBlank(deliveryProof)) {
                subOrder.setDeliveryProof(deliveryProof);
            }

            subOrder.setOrderStatus(NewOrderStatus.COMPLETED.getCode());
            subOrder.setFinishTime(LocalDateTime.now());
            customerOrderMapper.updateById(subOrder);

            // 保存轨迹
            orderTrackService.saveTrack(subOrder.getEntrustedOrderNumber(), subOrder.getCustomerOrderNumber(), NewOrderStatus.COMPLETED.getValue(), OrderTrackLink.DELIVERED.getCode()
            );

            // 记录主单
            mainOrderSet.add(subOrder.getEntrustedOrderNumber().substring(0, subOrder.getEntrustedOrderNumber().length()-3));
        }


        // 遍历主单，检查是否所有子单都完成，若是则更新主单状态
        for (String mainOrderNo : mainOrderSet) {
            // 查询该主单下是否还有未完成的子单
            Long unfinishedCount = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())
            );

            if (unfinishedCount == 0) {
                // 所有子单完成，更新主单状态
                TmsCustomerOrderEntity mainOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false)
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                );

                if (mainOrder != null) {
                    mainOrder.setOrderStatus(NewOrderStatus.COMPLETED.getCode());
                    mainOrder.setFinishTime(LocalDateTime.now());
                    customerOrderMapper.updateById(mainOrder);

                    // 判断派送任务单下面的订单是否已全部 完成
                    boolean hasUnfinishedOrders = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .eq(TmsCustomerOrderEntity::getDTaskOrder, mainOrder.getDTaskOrder())
                            .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())) > 0;

                    if (!hasUnfinishedOrders) {
                        transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, mainOrder.getDTaskOrder())
                                .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
                    }

                    // 发送短信通知
                    if (StrUtil.isNotBlank(mainOrder.getReceiverPhone())) {
                        String template = "Your parcel ({tracking number}) has been delivered by your trusted delivery partner, " +
                                "Neighbour Express. Please see details at {tracking url}. If you have NOT received your package, please contact us at {support url}.";

                        template = template.replace("{tracking number}", mainOrder.getEntrustedOrderNumber())
                                .replace("{tracking url}", "https://www.neighbourexpress.com/")
                                .replace("{support url}", "https://www.neighbourexpress.com/");

                        R smsResult = ALiYunSms.sentMes(mainOrder.getReceiverPhone(), template);

                        TmsSmsLogEntity smsLog = new TmsSmsLogEntity();
                        smsLog.setMobile(mainOrder.getReceiverPhone());
                        smsLog.setTemplateParam(template);
                        smsLog.setSendStatus(smsResult.isOk() ? 1 : 0);
                        smsLog.setCreatedTime(LocalDateTime.now());
                        smsLog.setUpdatedTime(LocalDateTime.now());
                        smsLog.setResponseMsg(smsResult.getMsg());
                        tmsSmsLogMapper.insert(smsLog);
                    }

                    // 完成派送，更新批次管理的已送达数
                    TmsOrderBatchEntity orderBatch = tmsOrderBatchMapper.selectOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .eq(TmsOrderBatchEntity::getBatchNo, mainOrder.getBatchNo()), false);

                    if (orderBatch != null) {
                        int deliveredCount = orderBatch.getDeliveredCount() + 1;
                        int remainingCount = Math.max(orderBatch.getOrderCount() - deliveredCount, 0);

                        orderBatch.setDeliveredCount(deliveredCount);
                        orderBatch.setNonDeliveryCount(remainingCount);
                        tmsOrderBatchMapper.updateById(orderBatch);
                    }

                }
            }
        }

        return R.ok(Boolean.TRUE);
    }

    /**
     * 揽收成功-批量上传送货证明
     * @param vo
     * @return
     */
    @Override
    public R batchUploadCollectionProof(TmsAppCollectionDeliveryVo vo) {
        processOrderNumbersInPlace(vo.getEntrustedOrderNumbers(),true);
        List<String> entrustedOrderNumbers = vo.getEntrustedOrderNumbers();
        String collectionDeliveryProof = vo.getCollectionDeliveryProof();
        Integer isTask = vo.getIsTask();
        if (isTask == 1) {
            for (String entrustedOrderNumber : entrustedOrderNumbers) {
                // 查询订单
                TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                        .isNotNull(TmsCustomerOrderEntity::getPTaskOrder));

                if (tmsCustomerOrder == null) {
                    return LocalizedR.failed("tms.app.driver.cage.order.not.exist", entrustedOrderNumber);
                }

                // todo: 帮陈昌提交
                if (null != tmsCustomerOrder.getCollectionDeliveryProof()) {
                    return LocalizedR.failed("tms.app.driver.order.delivery.proof.exist", entrustedOrderNumber);
                }

                // 送货凭证不为空时记录
                if(StrUtil.isNotBlank(collectionDeliveryProof)){
                    // 设置送货凭证
                    tmsCustomerOrder.setCollectionDeliveryProof(collectionDeliveryProof);
                    customerOrderMapper.updateById(tmsCustomerOrder) ;
                }

                // 添加轨迹
                orderTrackService.saveTrack(entrustedOrderNumber, tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.AWAITING_TRANSPORTATION.getValue(),OrderTrackLink.TWO_COLLECTION_WAREHOUSE_RECEIVED.getCode());

                // 修改为“未扫描”
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                        .set(TmsCustomerOrderEntity::getIsScan, Boolean.FALSE)
                );
            }

            // 获取所有订单的任务单号
            List<TmsCustomerOrderEntity> customerOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
            List<String> ptaskOrderNos = customerOrderList.stream().map(order -> order.getPTaskOrder()).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(ptaskOrderNos)) {
                // 去重任务单号
                Set<String> distinctPTaskOrders = new HashSet<>(ptaskOrderNos);

                for (String pTaskOrder : distinctPTaskOrders) {
                    // 更新任务单状态为“已完成”
                    transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, pTaskOrder)
                            .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
                }
            }
        }

        return R.ok(Boolean.TRUE);
    }

    /**
     * 派送提交审核(取货完成)   派送扫描成功直接算作提货成功，无需上传提货证明
     * @param reviewBo
     * @return
     */
    @Override
    public R sendReview(SendReviewBo reviewBo) {
        processOrderNumbersInPlace(reviewBo.getScanOrderNos(),false);
        // 去掉已扫描单号子单后三位获取主单
        List<String> mainOrderNos = reviewBo.getScanOrderNos().stream().map(orderNo -> orderNo.length() > 15 ? orderNo.substring(0, orderNo.length() - 3) : orderNo)
                .distinct().collect(Collectors.toList());

        // 存储派送报告单号
        String deliveryOrderNo = null;

        // 存储报告数据
        TmsReportManagementEntity reportEntity = reviewBo.getReport();

        // 获取已扫描订单的派送任务批次号
        TmsCustomerOrderEntity scanOrderBatch = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNos.get(0)),false);
        String batchNo = scanOrderBatch.getDTaskOrder();

        // 判断批次号是否存在报告-订单中间表中，存在即删除覆盖，否则新增
        TmsReportAndOrderEntity reportOrderBatch = reportAndOrderService.getOne(new LambdaQueryWrapper<TmsReportAndOrderEntity>()
                .eq(TmsReportAndOrderEntity::getBatchNo, batchNo),false);
        if (null != reportOrderBatch) {
            // 先记录派送单号报告
            deliveryOrderNo = reportOrderBatch.getReportOrderNo();
            // 先删除中间表记录,但不能删除已扫描完的订单，扫描完的订单需要累加，目前只删除未扫描的订单
            reportAndOrderService.deleteReport(reportOrderBatch.getReportOrderNo());
            // 覆盖中间表记录
            // --已扫描
            reportAndOrderService.saveReport(deliveryOrderNo, reviewBo.getScanOrderNos(), batchNo, 1);
            // --未扫描
            reportAndOrderService.saveReport(deliveryOrderNo, reviewBo.getUnScanOrderNos(), batchNo, 2);
            // --待退还
            reportAndOrderService.saveReport(deliveryOrderNo, reviewBo.getReturnOrderNos(), batchNo, 3);

            // 更新报告记录数据
            tmsReportManagementService.update(new LambdaUpdateWrapper<TmsReportManagementEntity>()
                    .eq(TmsReportManagementEntity::getReportOrderNo, deliveryOrderNo)
                    .eq(TmsReportManagementEntity::getDispatchNum, reportEntity.getDispatchNum())
                    .set(TmsReportManagementEntity::getScanedNum, reportEntity.getScanedNum())
                    .set(TmsReportManagementEntity::getUnscanNum, reportEntity.getUnscanNum())
                    .setSql("return_num = return_num + " + reportEntity.getReturnNum())
            );

            // 子单批量绑定报告编号
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, reviewBo.getScanOrderNos())
                    .set(TmsCustomerOrderEntity::getReportOrderNo, deliveryOrderNo)
            );
        }else{
            // ----------------------新增派送单号报告
            // 记录报告生成单号
            deliveryOrderNo = generateReportOrderNo("D");
            reportEntity.setReportOrderNo(deliveryOrderNo);
            //生成报告
            Boolean report =tmsReportManagementService.saveReportManagement(reportEntity);
            if (!report) {
                return R.failed("请重试！报告提交失败/The report submission failed.");
            }

            // 子单批量绑定报告编号
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, reviewBo.getScanOrderNos())
                    .set(TmsCustomerOrderEntity::getReportOrderNo, deliveryOrderNo)
            );

            // 将绑定数据记录中间表
            // --已扫描
            reportAndOrderService.saveReport(deliveryOrderNo, reviewBo.getScanOrderNos(), batchNo, 1);
            // --未扫描
            reportAndOrderService.saveReport(deliveryOrderNo, reviewBo.getUnScanOrderNos(), batchNo, 2);
            // --待退还
            reportAndOrderService.saveReport(deliveryOrderNo, reviewBo.getReturnOrderNos(), batchNo, 3);
        }

        // 处理子单号
        // 批量修改子单状态
        for (String item : reviewBo.getScanOrderNos()) {
            customerOrderMapper.update(
                    new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                            .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, item)
                            .eq(TmsCustomerOrderEntity::getSubFlag, true)
                            .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_DELIVERY.getCode())
            );
        }

        // 检查每个主单下子单是否都已“待收货”
        for (String mainOrderNo : mainOrderNos) {
            // 查询所有子单
            List<TmsCustomerOrderEntity> tmsCustomerOrderEntities = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                            .eq(TmsCustomerOrderEntity::getSubFlag, true)
            );
            // 检查是否全是已"待收货"
            boolean allAwaitingDelivery = tmsCustomerOrderEntities.stream()
                    .allMatch(item -> item.getOrderStatus() == NewOrderStatus.AWAITING_DELIVERY.getCode());
            if (allAwaitingDelivery) {
                // 主单相关逻辑
                TmsCustomerOrderEntity tmsCustomerOrder = customerOrderMapper.selectOne(
                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                );
                tmsCustomerOrder.setOrderStatus(NewOrderStatus.AWAITING_DELIVERY.getCode());
                int b = customerOrderMapper.updateById(tmsCustomerOrder);
                if (b > 0) {
                    // 将任务单状态改为配送中
                    transportTaskOrderMapper.update(new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, tmsCustomerOrder.getDTaskOrder())
                            .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()));

                    // 记录派送提货轨迹
                    orderTrackService.saveTrack(mainOrderNo, tmsCustomerOrder.getCustomerOrderNumber(), NewOrderStatus.AWAITING_DELIVERY.getValue(),OrderTrackLink.IN_TRANSIT.getCode());

                    if (StrUtil.isNotBlank(tmsCustomerOrder.getReceiverPhone())) {
                        String template = "Your parcel ({tracking number}) has been out for delivery by your trusted delivery partner, Neighbour Express.";
                        template = template.replace("{tracking number}", tmsCustomerOrder.getEntrustedOrderNumber());
                        R r = ALiYunSms.sentMes(tmsCustomerOrder.getReceiverPhone(), template);
                        // 短信日志
                        TmsSmsLogEntity smsLogEntity = new TmsSmsLogEntity();
                        smsLogEntity.setMobile(tmsCustomerOrder.getReceiverPhone());
                        smsLogEntity.setTemplateParam(template);
                        smsLogEntity.setSendStatus(r.isOk() ? 1 : 0);
                        smsLogEntity.setCreatedTime(LocalDateTime.now());
                        smsLogEntity.setUpdatedTime(LocalDateTime.now());
                        smsLogEntity.setResponseMsg(r.getMsg());
                        tmsSmsLogMapper.insert(smsLogEntity);
                    }
                }
            }
        }
        tmsOrderScanRecordService.saveScanRecord(reviewBo.getScanOrderNos(), reviewBo.getDriverId());

        return LocalizedR.ok("tms.app.driver.order.submit.succeed", deliveryOrderNo);
    }

    /**
     * 派送生成报告(完成取货)
     * @param driverId
     * @param entrustedOrderNos
     * @return
     */
    @Override
    @Transactional
    public R generateDeliveryReport(Long driverId, List<String> entrustedOrderNos, List<String> returnOrderNos) {
        if (CollUtil.isEmpty(entrustedOrderNos)) {
            return R.failed("扫描订单不能为空");
        }
        processOrderNumbersInPlace(entrustedOrderNos,false);
        // 获取主单号（去除后三位）
        List<String> mainOrderNos = entrustedOrderNos.stream()
                .map(orderNo -> orderNo.length() > 15 ? orderNo.substring(0, orderNo.length() - 3) : orderNo)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(mainOrderNos)) {
            return R.failed("无法识别主单号");
        }
        String firstMainOrderNo = mainOrderNos.get(0);

        // 查询主单获取任务批次号
        TmsCustomerOrderEntity firstOrder = customerOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, firstMainOrderNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false)
                        .last("LIMIT 1")
        );

        if (firstOrder == null || StrUtil.isBlank(firstOrder.getDTaskOrder())) {
            return R.failed("未找到该订单对应的任务批次");
        }
        String taskOrderNo = firstOrder.getDTaskOrder();

        // 查询该任务批次下所有主单
        List<TmsCustomerOrderEntity> mainOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDTaskOrder, taskOrderNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false)
        );

        List<String> taskMainOrderNos = mainOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());

        // 查询所有子单（用于 dispatchNum）
        List<TmsCustomerOrderEntity> allSubOrders;
        if (CollUtil.isNotEmpty(taskMainOrderNos)) {
            LambdaQueryWrapper<TmsCustomerOrderEntity> subWrapper = new LambdaQueryWrapper<>();
            subWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .and(w -> {
                        for (String mainNo : taskMainOrderNos) {
                            w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                        }
                    });
            allSubOrders = customerOrderMapper.selectList(subWrapper);
        } else {
            allSubOrders = Collections.emptyList();
        }
        int dispatchNum = allSubOrders.size();

        // 查询当前未完成子单列表（排除当前已扫描）
        List<TmsCustomerOrderEntity> unScannedOrders;
        if (CollUtil.isNotEmpty(taskMainOrderNos)) {
            LambdaQueryWrapper<TmsCustomerOrderEntity> unscanWrapper = new LambdaQueryWrapper<>();
            unscanWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true)
                    //.lt(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_DELIVERY.getCode())
                    .eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.WAREHOUSE_RECEIVE.getCode())
                    .notIn(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNos) // 排除当前已扫描的
                    .and(w -> {
                        for (String mainNo : taskMainOrderNos) {
                            w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                        }
                    });
            unScannedOrders = customerOrderMapper.selectList(unscanWrapper);
        } else {
            unScannedOrders = Collections.emptyList();
        }

        // === 已扫描数量 = 数据库中已扫 + 本次新扫（排除重复） ===
        long scanedNum;
        if (CollUtil.isNotEmpty(taskMainOrderNos)) {
            LambdaQueryWrapper<TmsCustomerOrderEntity> scannedWrapper = new LambdaQueryWrapper<>();
            scannedWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .ge(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_DELIVERY.getCode())
                    .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.WAREHOUSE_RECEIVE.getCode())
                    .and(w -> {
                        for (String mainNo : taskMainOrderNos) {
                            w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                        }
                    });

            List<String> dbScannedOrderNos = customerOrderMapper.selectList(scannedWrapper).stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            Set<String> scannedOrderSet = new HashSet<>(entrustedOrderNos);
            long newlyScanned = scannedOrderSet.stream()
                    .filter(orderNo -> !dbScannedOrderNos.contains(orderNo))
                    .count();

            scanedNum = dbScannedOrderNos.size() + newlyScanned;
        } else {
            scanedNum = 0L;
        }

        // === 组装报告结果 ===
        TmsReportManagementEntity report = new TmsReportManagementEntity();
        report.setReportType(2); // 派送类型
        report.setDispatchNum(dispatchNum); // 固定派单总数
        report.setScanedNum((int) scanedNum);            // 累计扫描数量
        report.setUnscanNum((int) (dispatchNum - scanedNum)); // 未扫数量
        report.setReturnNum(returnOrderNos.size());   // 退还数量

        Map<String, Object> result = new HashMap<>();
        result.put("report", report);
        Set<String> scannedOrderList= new HashSet<>(entrustedOrderNos);
        result.put("scannedOrders", scannedOrderList);         // 已扫描列表
        result.put("unScannedOrders", unScannedOrders);    // 当前未完成子单
        result.put("returnOrders", returnOrderNos);        // 本次退还子单

        return R.ok(result);
    }




/*
    public R generateDeliveryReport(Long driverId, List<String> entrustedOrderNos,List<String> returnOrderNos) {
        // 去掉子单后三位获取主单
        List<String> mainOrderNos = entrustedOrderNos.stream().map(orderNo -> orderNo.length() > 15 ? orderNo.substring(0, orderNo.length() - 3) : orderNo)
                .distinct().collect(Collectors.toList());

        // 查询司机未派送完成的任务单
        LambdaQueryWrapper<TmsTransportTaskOrderEntity> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                .ne(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
                .ne(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.CANCELLED.getCode())
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.DELIVERY.getCode());
        List<TmsTransportTaskOrderEntity> taskOrders = transportTaskOrderMapper.selectList(wrapper1);

        if (taskOrders.isEmpty()) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.complete", "");
        }

        // 待揽收订单
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper2 = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder)
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.DELIVERY.getCode())
                .lt(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_DELIVERY.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId);

        List<TmsCustomerOrderEntity> dmainOrderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper2);
        // 拿到所有主单号
        List<String> dmainOrderNoList = dmainOrderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        LambdaQueryWrapper<TmsCustomerOrderEntity> subWq2 = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(dmainOrderNoList)) {
            subWq2.eq(TmsCustomerOrderEntity::getSubFlag, true)
                    // 排除已揽收的子单
                    .notIn(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNos)
                    .and(subWrapper2 -> {
                        for (String mainOrder : dmainOrderNoList) {
                            subWrapper2.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrder);
                        }
                    });
        } else {
            subWq2.eq(TmsCustomerOrderEntity::getId, -1L);
        }
        // 未揽数量
        int unScanedNum = customerOrderMapper.selectCount(subWq2).intValue();
        // 已揽数量
        int scanedNum = entrustedOrderNos.stream().distinct().collect(Collectors.toList()).size();
        // 未揽订单
        List<TmsCustomerOrderEntity> unScannedOrders = customerOrderMapper.selectList(subWq2);

        // 预览返回派送报告
        TmsReportManagementEntity report = new TmsReportManagementEntity();
        report.setReportType(2);
        // 派单数量
        report.setDispatchNum(scanedNum + unScanedNum);
        report.setScanedNum(scanedNum);
        report.setUnscanNum(unScanedNum);
        report.setReturnNum(returnOrderNos.size());

        Map<String, Object> result = new HashMap<>();
        result.put("report", report);
        result.put("scannedOrders", mainOrderNos);
        result.put("unScannedOrders", unScannedOrders);
        result.put("returnOrders", returnOrderNos);
        return R.ok(result);
    }
*/

    /**
     * app-校验所有面单是否全部已扫描(完成取货)
     * @param driverId
     * @param entrustedOrderNos
     * @return
     */
    @Override
    public R checkAllScanned(Long driverId, List<String> entrustedOrderNos) {
        if (ObjectUtil.isNull(driverId)) {
            return R.failed("driverId not null");
        }
        if (CollUtil.isEmpty(entrustedOrderNos)) {
            return R.failed("entrustedOrderNos not null");
        }
        processOrderNumbersInPlace(entrustedOrderNos,false);
        // 提取主单号（去掉后三位）
        List<String> mainOrderNos = entrustedOrderNos.stream()
                .map(orderNo -> orderNo.length() > 15 ? orderNo.substring(0, orderNo.length() - 3) : orderNo)
                .distinct()
                .collect(Collectors.toList());

        // 查询主单，验证 driverId 合法性
        LambdaQueryWrapper<TmsCustomerOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNos)
                .eq(TmsCustomerOrderEntity::getDeliveryDriverId, driverId);
        List<TmsCustomerOrderEntity> orders = customerOrderMapper.selectList(qw);
        if (CollUtil.isEmpty(orders)) {
            return LocalizedR.failed("tms.app.sub.order.not.exist", "");
        }

        // 检查是否有未传入的子单号
        List<String> allMissingSubOrders = new ArrayList<>();
        for (String mainOrderNo : mainOrderNos) {
            MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())
                    .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder);
            List<TmsCustomerOrderEntity> allSubOrders = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper);

            List<String> subOrderNosInDB = allSubOrders.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            // 找出数据库中存在但未传入的子单号
            List<String> missingSubOrders = subOrderNosInDB.stream()
                    .filter(subNo -> !entrustedOrderNos.contains(subNo))
                    .collect(Collectors.toList());

            allMissingSubOrders.addAll(missingSubOrders);
        }

        if (CollUtil.isNotEmpty(allMissingSubOrders)) {
            return LocalizedR.failed("tms.app.driver.order.not.scan.pickup", allMissingSubOrders);
        }

        return R.ok(Boolean.TRUE);
    }

/*    public R checkAllScanned(Long driverId, List<String> entrustedOrderNos) {
        if (ObjectUtil.isNull(driverId)) {
            return R.failed("driverId not null");
        }
        // 去掉子单后三位获取主单
        List<String> mainOrderNos = entrustedOrderNos.stream().map(orderNo -> orderNo.length() > 15 ? orderNo.substring(0, orderNo.length() - 3) : orderNo)
                .distinct().collect(Collectors.toList());

        LambdaQueryWrapper<TmsCustomerOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.in(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNos)
                .eq(TmsCustomerOrderEntity::getDeliveryDriverId, driverId);
        List<TmsCustomerOrderEntity> orders = customerOrderMapper.selectList(qw);

        if (CollUtil.isEmpty(orders)) {
            return LocalizedR.failed("tms.app.sub.order.not.exist", "");
        }

        // 查询并判断该主单的所有子单是否都已扫描取货，若未全部扫描，则返回具体的子单号，即面单
        if (CollUtil.isNotEmpty(mainOrderNos)) {
            for (String mainOrderNo : mainOrderNos) {
                // 判断主单是否还有无未扫描的子单号
                MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
                wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
                        .ne(TmsCustomerOrderEntity::getIsScan, 1)
                        .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder);
                List<TmsCustomerOrderEntity> subOrderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, wrapper);
                if (!subOrderList.isEmpty()) {
                    // 返回对应未扫描的子单号集合
                    return LocalizedR.failed("tms.app.driver.order.not.scan.pickup",
                            subOrderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList()));
                }
            }
        }

        return R.ok(Boolean.TRUE);
    }*/

    // 生成报告单号
    private String generateReportOrderNo(String type) {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = type.toUpperCase(); // S揽收  D派送

        String redisKey = REDIS_REPORT_SEQ_KEY_PREFIX + prefix + ":" + date;

        // Redis 原子自增，初始为1
        Long sequence = stringRedisTemplate.opsForValue().increment(redisKey);

        // 设置过期时间为2天，防止key堆积
        if (sequence != null && sequence == 1L) {
            stringRedisTemplate.expire(redisKey, Duration.ofDays(2));
        }

        // 序列号和校验位
        String sequenceStr = String.format("%03d", sequence); // 001
        String checksumStr = String.format("%02d", sequence + 10); // 简单校验位

        return String.format("%s%s%s%s", prefix, date, sequenceStr, checksumStr);
    }

    /**
     * 配送失败
     * @param request
     * @return
     */
/*    @Transactional
    @Override
    public R deliveryFailed(DeliveryFailedRequest request) {
        List<String> entrustedOrderNo = request.getEntrustedOrderNo();
        String deliveryFailedProof = request.getDeliveryFailedProof();
        String failedReason = request.getFailedReason();
        Integer deliveryOption = request.getDeliveryOption();
        // 查询订单信息
        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectJoinOne(TmsCustomerOrderEntity.class, new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .selectAll(TmsCustomerOrderEntity.class)
                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder)
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNo)
        );
        if (ObjectUtil.isNotNull(customerOrder) && customerOrder.getOrderStatus().equals(NewOrderStatus.AWAITING_RETURN.getCode())) {
            return LocalizedR.failed("tms.app.driver.has.returned.order", entrustedOrderNo);
        }

        if (ObjectUtil.isNull(customerOrder)) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", entrustedOrderNo);
        }

        // 走再次配送,派送尝试次数+1,
        if (deliveryOption.equals(1) && customerOrder.getDeliveryTry() < 3) {
            // 插入失败记录
            TmsDeliveryFailedProofEntity proof = new TmsDeliveryFailedProofEntity();
            proof.setEntrustedOrderNo(entrustedOrderNo);
            proof.setDeliveryTry(customerOrder.getDeliveryTry() + 1); // 当前即将尝试次数 +1
            proof.setProofImageUrl(deliveryFailedProof);
            tmsDeliveryFailedProofMapper.insert(proof);

            // 更新尝试次数 +1
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNo)
                    .setSql("delivery_try = delivery_try + 1")
                    .set(TmsCustomerOrderEntity::getDeliveryFailedProof, deliveryFailedProof)
            );
        }

        // 如果派送尝试次数等于2，修改订单状态为待返仓,且将派送任务单号置空，走无法配送

        // 走无法配送（再次配送超过第3次，前端按钮只保留无法配送）  // todo：应产品要求，先将无法配送删除任务批次和路径规划操作注释
        if (deliveryOption.equals(2)) {
            // 更新失败原因
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNo)
                    .set(TmsCustomerOrderEntity::getFailedReason, failedReason)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_RETURN.getCode()));
                    //.set(TmsCustomerOrderEntity::getDTaskOrder, null));
        }
        return R.ok(customerOrder);
    }*/

    @Override
    public R deliveryFailed(DeliveryFailedRequest request) {
        // 子单号列表
        List<String> subOrderNos = request.getSubOrderNos();
        //配送失败选项：1=再次配送，2=配送失败，3=无法配送
        Integer deliveryOption = request.getDeliveryOption();

        if (CollUtil.isEmpty(subOrderNos)) {
            return R.failed("请输入相关单号！");
        }

        // 查询所有订单信息
        List<TmsCustomerOrderEntity> customerOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrderNos)
        );

        if (CollUtil.isEmpty(customerOrderList)) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", subOrderNos);
        }
        // 再次配送
        if (deliveryOption.equals(1)) {
            // 先找出尝试次数已达 3 次的订单
            List<String> overLimitOrderNos = customerOrderList.stream()
                    .filter(order -> order.getDeliveryTry() >= 2)
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(overLimitOrderNos)) {
                String overLimitStr = String.join(",", overLimitOrderNos);
                return LocalizedR.failed("tms.app.driver.It.has.been.delivered.three.times", overLimitStr);
            }

            // 校验订单（已完成 或 已返仓）
            List<String> invalidStatusOrders = customerOrderList.stream()
                    .filter(order -> NewOrderStatus.COMPLETED.getCode().equals(order.getOrderStatus())
                            || NewOrderStatus.RETURNED.getCode().equals(order.getOrderStatus()))
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(invalidStatusOrders)) {
                String invalidStr = String.join(",", invalidStatusOrders);
                return LocalizedR.failed("tms.app.driver.has.returned.order.not.delivery" ,invalidStr);
            }

            for (TmsCustomerOrderEntity order : customerOrderList) {
                String orderNo = order.getEntrustedOrderNumber();
                // 更新尝试次数
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                        .setSql("delivery_try = delivery_try + 1")
                        .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_DELIVERY.getCode()));
            }
        }

        // 走配送失败逻辑
        if (deliveryOption.equals(2)){
            // 配送失败证明（最多6张图片，逗号分割）
            String deliveryFailedProof = request.getDeliveryFailedProof();
            // 派送失败原因
            Integer failedReason = request.getFailedReason();

            // 检查是否有待提货的订单
            List<String> pickupOrders = customerOrderList.stream()
                    .filter(order -> NewOrderStatus.AWAITING_DELIVERY.getCode()>order.getOrderStatus())
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pickupOrders)) {
                return LocalizedR.failed("tms.app.driver.has.pickup.order", pickupOrders);
            }

            // 检查是否有待提货的订单(根据后续新增状态仓库收件)
            List<String> undeliveredOrders = customerOrderList.stream()
                    .filter(order -> NewOrderStatus.WAREHOUSE_RECEIVE.getCode().equals(order.getOrderStatus()))
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(undeliveredOrders)) {
                return LocalizedR.failed("tms.app.driver.has.pickup.order", pickupOrders);
            }

            // 检查是否有已待返仓或已返仓的订单
            List<String> warehouseOrders = customerOrderList.stream()
                    .filter(order -> NewOrderStatus.AWAITING_RETURN.getCode().equals(order.getOrderStatus())
                            || NewOrderStatus.RETURNED.getCode().equals(order.getOrderStatus()))
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(warehouseOrders)) {
                return LocalizedR.failed("tms.app.driver.has.returned.warehouse.order", warehouseOrders);
            }

            // 检查是否有已配送失败的订单
            List<String> returnedOrders = customerOrderList.stream()
                    .filter(order -> NewOrderStatus.FAILED_DELIVERY.getCode().equals(order.getOrderStatus()))
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(returnedOrders)) {
                return LocalizedR.failed("tms.app.driver.has.returned.order", returnedOrders);
            }

            // 检查是否有已完成的订单
            List<String> completeOrders = customerOrderList.stream()
                    .filter(order -> NewOrderStatus.COMPLETED.getCode().equals(order.getOrderStatus()))
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(completeOrders)) {
                return LocalizedR.failed("tms.app.driver.has.complete.order", completeOrders);
            }

            // 遍历处理每个订单
            for (TmsCustomerOrderEntity order : customerOrderList) {
                String orderNo = order.getEntrustedOrderNumber();
                String customerOrderNo = order.getCustomerOrderNumber();
                Integer deliveryTry = Optional.ofNullable(order.getDeliveryTry()).orElse(0);

                // 更新订单状态
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                        .set(TmsCustomerOrderEntity::getFailedReason, failedReason)
                        .set(TmsCustomerOrderEntity::getDeliveryFailedProof, deliveryFailedProof)
                        .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.FAILED_DELIVERY.getCode())
                );

                // 根据失败次数生成轨迹信息
                Integer trackLine;
                switch (deliveryTry) {
                    case 0:
                        trackLine = OrderTrackLink.FAILED_DELIVERY_1ST.getCode();
                        break;
                    case 1:
                        trackLine = OrderTrackLink.FAILED_DELIVERY_2ND.getCode();
                        break;
                    default:
                        trackLine = OrderTrackLink.FAILED_DELIVERY_RETURN_WAREHOURE.getCode();
                        break;
                }

                // 插入轨迹记录
                orderTrackService.saveTrack(orderNo, customerOrderNo, NewOrderStatus.FAILED_DELIVERY.getValue(),trackLine);

                // 插入失败证明记录
                TmsDeliveryFailedProofEntity proof = new TmsDeliveryFailedProofEntity();
                proof.setEntrustedOrderNo(orderNo);
                proof.setDeliveryTry(deliveryTry + 1);
                proof.setProofImageUrl(deliveryFailedProof);
                proof.setFailedReason(failedReason);
                tmsDeliveryFailedProofMapper.insert(proof);
            }
        }

        // 走无法配送逻辑
        if (deliveryOption.equals(3)){
            // 1. 校验不允许“无法配送”的状态
            List<String> invalidStatusOrders = customerOrderList.stream()
                    .filter(order ->
                            NewOrderStatus.RETURNED.getCode().equals(order.getOrderStatus()) ||          // 已返仓
                                    NewOrderStatus.AWAITING_RETURN.getCode().equals(order.getOrderStatus()) ||   // 待返仓
                                    !(NewOrderStatus.FAILED_DELIVERY.getCode().equals(order.getOrderStatus()))   // 过滤不等于配送失败的订单
                    )
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(invalidStatusOrders)) {
                String invalidStr = String.join(",", invalidStatusOrders);
                return LocalizedR.failed("tms.app.driver.has.returned.order.not.cannot.delivery",invalidStr);
            }

            // 提取全部单号
            List<String> entrustedOrderNos = customerOrderList.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
            // 更新订单状态为待返仓
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNos)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_RETURN.getCode()));

            // 为成功更新的订单添加轨迹记录
            customerOrderList.stream().filter(order -> NewOrderStatus.AWAITING_RETURN.getCode().equals(order.getOrderStatus()))
                    .forEach(order -> {
                        String orderNo = order.getEntrustedOrderNumber();
                        String customerOrderNo = order.getCustomerOrderNumber();
                        orderTrackService.saveTrack(orderNo, customerOrderNo, NewOrderStatus.AWAITING_RETURN.getValue(), OrderTrackLink.PARCEL_RETURNED_SORTING.getCode());
                    });
        }

        return R.ok("操作成功");
    }


    // 揽收批量上传取货证明
    @Override
    public R batchUploadPickupProof(TmsAppCollectionPickupVo vo) {
        // 获取全部主单号并去重
        List<String> entrustedOrderNumbers = vo.getEntrustedOrderNumbers()
                .stream()
                .distinct()
                .collect(Collectors.toList());
        processOrderNumbersInPlace(entrustedOrderNumbers,true);
        // 根据主单号查询订单信息
        List<TmsCustomerOrderEntity> mainOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers)
                .eq(TmsCustomerOrderEntity::getSubFlag, false));

        if (CollUtil.isEmpty(mainOrders)) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", entrustedOrderNumbers);
        }

        // 过滤符合条件的订单（已扫描、状态为"待揽收"）
        List<TmsCustomerOrderEntity> filteredOrders = mainOrders.stream()
                .filter(order -> Boolean.TRUE.equals(order.getIsScan())
                        && NewOrderStatus.AWAITING_PICKUP.getCode().equals(order.getOrderStatus()))
                .collect(Collectors.toList());

        // 获取不符合条件的订单：mainOrders - filteredOrders
        List<TmsCustomerOrderEntity> invalidOrders = new ArrayList<>(mainOrders);
        invalidOrders.removeAll(filteredOrders);

        // 收集所有未扫描子单号
        List<String> unscannedSubOrderNos = new ArrayList<>();
        // 如果存在不符合条件的订单，则先处理其已扫描的子单（更新提货凭证）
        if (CollUtil.isNotEmpty(invalidOrders)) {
            for (TmsCustomerOrderEntity invalidOrder : invalidOrders) {
                // 查询该无效主单下的所有子单
                List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(
                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber,
                                        invalidOrder.getEntrustedOrderNumber())
                                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                );

                // 找出已扫描的子单
                List<TmsCustomerOrderEntity> scannedSubOrders = subOrders.stream()
                        .filter(sub -> Boolean.TRUE.equals(sub.getIsScan()))
                        .collect(Collectors.toList());

                // 找出未扫描的子单号
                List<String> currentUnscannedSubNos = subOrders.stream()
                        .filter(sub -> !Boolean.TRUE.equals(sub.getIsScan()))
                        .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                        .collect(Collectors.toList());

                // 将未扫描的子单号添加到总集合中
                unscannedSubOrderNos.addAll(currentUnscannedSubNos);
                // 更新已扫描子单的提货凭证（如果未设置）
                if (CollUtil.isNotEmpty(scannedSubOrders) && StrUtil.isNotBlank(vo.getCollectionPickupProof())) {
                    // 分组处理：没有凭证的和已有凭证的
                    List<Long> noProofSubIds = scannedSubOrders.stream()
                            .filter(sub -> StrUtil.isBlank(sub.getCollectionPickupProof()))
                            .map(TmsCustomerOrderEntity::getId)
                            .collect(Collectors.toList());

                    // 更新没有提货凭证的订单
                    if (CollUtil.isNotEmpty(noProofSubIds)) {
                        customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                                .in(TmsCustomerOrderEntity::getId, noProofSubIds)
                                .set(TmsCustomerOrderEntity::getCollectionPickupProof, vo.getCollectionPickupProof())
                        );
                    }
                }
            }
        }

        // 如果没有有效订单，直接返回
        if (CollUtil.isEmpty(filteredOrders)) {
            return CollUtil.isNotEmpty(unscannedSubOrderNos)
                    ? LocalizedR.ok("tms.app.driver.order.not.scan.pickup", String.join(",", unscannedSubOrderNos))
                    : LocalizedR.ok(Boolean.TRUE);
        }

        // 提取可用单号和任务单号集合
        List<String> validEntrustedNos = filteredOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());

        Set<String> taskOrderNos = filteredOrders.stream()
                .map(TmsCustomerOrderEntity::getPTaskOrder)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 构建任务单号和跟踪单号的关联
        Map<String, String> entrustedToTaskOrderMap = filteredOrders.stream()
                .filter(order -> StrUtil.isNotBlank(order.getPTaskOrder()))
                .collect(Collectors.toMap(
                        TmsCustomerOrderEntity::getEntrustedOrderNumber,
                        TmsCustomerOrderEntity::getPTaskOrder
                ));

        // 更新主单状态、提货凭证（批量）
        customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, validEntrustedNos)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode())
                .set(StrUtil.isNotBlank(vo.getCollectionPickupProof()),
                        TmsCustomerOrderEntity::getCollectionPickupProof, vo.getCollectionPickupProof())
        );

        // 更新子单状态（批量）
        customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getSubFlag, true)  //子单
                .and(wrapper -> {
                    for (String condition : validEntrustedNos) {
                        wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, condition);
                    }
                })
                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode())
        );
        // 单独更新没有凭证的子单
        if (StrUtil.isNotBlank(vo.getCollectionPickupProof())) {
            customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getSubFlag, true)  //子单
                    .and(wrapper -> {
                        for (String condition : validEntrustedNos) {
                            wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, condition);
                        }
                    })
                    .and(wrapper -> wrapper
                            .isNull(TmsCustomerOrderEntity::getCollectionPickupProof)
                            .or()
                            .eq(TmsCustomerOrderEntity::getCollectionPickupProof, "")
                    )
                    .set(TmsCustomerOrderEntity::getCollectionPickupProof, vo.getCollectionPickupProof())
            );
        }

        // 更新任务单状态（批量）
        if (CollUtil.isNotEmpty(taskOrderNos)) {
            transportTaskOrderMapper.update(null, new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                    .in(TmsTransportTaskOrderEntity::getTaskOrderNo, taskOrderNos)
                    .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode()));
        }

        // 批量记录轨迹
        for (TmsCustomerOrderEntity order : filteredOrders) {
            orderTrackService.saveTrack(
                    order.getEntrustedOrderNumber(),
                    order.getCustomerOrderNumber(),
                    NewOrderStatus.AWAITING_TRANSPORTATION.getValue(),
                    OrderTrackLink.AWAITING_TRANSPORTATION.getCode()
            );
        }

        // 保存司机揽收扫描记录
        if (CollUtil.isNotEmpty(taskOrderNos)) {
            // 查询任务单信息，构造任务单号->任务单对象Map
            Map<String, TmsTransportTaskOrderEntity> taskOrderMap = transportTaskOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                            .in(TmsTransportTaskOrderEntity::getTaskOrderNo, taskOrderNos)
            ).stream().collect(Collectors.toMap(
                    TmsTransportTaskOrderEntity::getTaskOrderNo, t -> t
            ));
            // 收集所有driverId
            Set<Long> driverIds = filteredOrders.stream()
                    .map(order -> taskOrderMap.get(order.getPTaskOrder()))
                    .filter(Objects::nonNull)
                    .map(TmsTransportTaskOrderEntity::getDriverId)
                    .collect(Collectors.toSet());
            // 批量查司机表，构建driverId到司机信息的map
            Map<Long, TmsLmdDriverEntity> driverMap = driverMapper.selectBatchIds(driverIds)
                    .stream()
                    .collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, d -> d));
            // 构建司机扫描记录
            List<TmsOrderScanRecordEntity> scanRecords = filteredOrders.stream()
                    .filter(order -> StrUtil.isNotBlank(order.getPTaskOrder()))
                    .map(order -> {
                        TmsTransportTaskOrderEntity task = taskOrderMap.get(order.getPTaskOrder());
                        if (task == null){
                            return null;
                        }
                        TmsLmdDriverEntity tmsLmdDriverEntity = driverMap.get(task.getDriverId());
                        TmsOrderScanRecordEntity record = new TmsOrderScanRecordEntity();
                        record.setOrderNo(order.getEntrustedOrderNumber());
                        record.setDriverId(tmsLmdDriverEntity.getDriverId());
                        record.setDriverName(tmsLmdDriverEntity.getDriverName());
                        record.setDriverNum(tmsLmdDriverEntity.getDriverNum());
                        record.setScanTime(LocalDateTime.now());
                        record.setScanType(1);
                        return record;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!scanRecords.isEmpty()) {
                tmsOrderScanRecordService.saveBatch(scanRecords);
            }
        }

        return CollUtil.isNotEmpty(unscannedSubOrderNos)
                ? LocalizedR.ok("tms.app.driver.order.not.scan.pickup", String.join(",", unscannedSubOrderNos))
                : LocalizedR.ok(Boolean.TRUE);
    }


    // 获取待提货数量
    @Override
    public R getWaitScanCount(Long driverId) {
        // 查询揽收任务子单未提货数量
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getPTaskOrder)
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.COLLECTION.getCode())
                .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId);

        List<TmsCustomerOrderEntity> pmainOrderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class,wrapper);
        // 拿到所有主单号
        List<String> pmainOrderNoList = pmainOrderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        LambdaQueryWrapper<TmsCustomerOrderEntity> subWq1 = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(pmainOrderNoList)) {
            subWq1.eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .and(subWrapper1 -> {
                        for (String mainOrder : pmainOrderNoList) {
                            subWrapper1.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrder);
                        }
                    });
        } else {
            subWq1.eq(TmsCustomerOrderEntity::getId, -1L);
        }

        // 查询派送任务子单未提货数量
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper2 = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsCustomerOrderEntity::getDTaskOrder)
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.DELIVERY.getCode())
                .and(a -> {
                    a.lt(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_DELIVERY.getCode()).or()
                            .eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.WAREHOUSE_RECEIVE.getCode());
                })
                .ne(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                ;

        List<TmsCustomerOrderEntity> dmainOrderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class,wrapper2);
        // 拿到所有主单号
        List<String> dmainOrderNoList = dmainOrderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        LambdaQueryWrapper<TmsCustomerOrderEntity> subWq2 = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(dmainOrderNoList)) {
            subWq2.eq(TmsCustomerOrderEntity::getSubFlag, true)
                    .eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.WAREHOUSE_RECEIVE.getCode())
                    .and(subWrapper2 -> {
                        for (String mainOrder : dmainOrderNoList) {
                            subWrapper2.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrder);
                        }
                    });
        } else {
            subWq2.eq(TmsCustomerOrderEntity::getId, -1L);
        }

        //查询此时干线该司机的待扫描的容器标签数

        //查询司机此时的所有待提货状态的干线任务
        List<TmsLineHaulOrderEntity> lineHaulOrder = lineHaulOrderMapper.selectList(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getDriverId, driverId)
                        .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode()));
        //查询待扫描的标签(待提货的干线任务就是待提货扫描的)
        List<String> lineHaulNos = lineHaulOrder.stream()
                .map(TmsLineHaulOrderEntity::getLineHaulNo).collect(Collectors.toList());
        List<TmsLabelEntity> tmsLabelEntities =new ArrayList<>();
        if(ObjectUtil.isNotNull(lineHaulNos) && CollUtil.isNotEmpty(lineHaulNos)){
            tmsLabelEntities  = labelMapper.selectList(new LambdaQueryWrapper<TmsLabelEntity>()
                    .in(TmsLabelEntity::getLabelCode, lineHaulNos)
                    //干线未扫描提货的标签
                    .eq(TmsLabelEntity::getIsPickupProof, LineHaulIsPickConstant.WAIT_PICK_UP)
            );
        }
        // 揽收数量
        Long pickupWaitScanCount = customerOrderMapper.selectCount(subWq1);
        // 派送数量
        Long deliveryWaitScanCount = customerOrderMapper.selectCount(subWq2);

        // 封装返回
        Map<String, Long> result = new HashMap<>();
        result.put("pickupWaitScanCount", pickupWaitScanCount);
        result.put("deliveryWaitScanCount", deliveryWaitScanCount);
        //干线待扫描容器数
        result.put("lineWaitScanCount", (long) tmsLabelEntities.size());

        return R.ok(result);
    }



    // 单个主单查询子单号（查询扫描列表）
    @Override
    public R getSubFlag(String entrustedOrderNos) {
        // 根据逗号分割参数
        List<String> entrustedOrderNosList = Arrays.stream(entrustedOrderNos.trim().split(","))
                .map(String::trim)
                .collect(Collectors.toList());

        // 过滤非主单号
        List<String> validMainNos = entrustedOrderNosList.stream()
                .filter(no -> no.length() >= 13)
                .collect(Collectors.toList());

        if (validMainNos.isEmpty()) {
            return R.failed("请传入正确格式的主单号");
        }

        // 构造查询条件，查出所有主单号下的子单
        List<TmsCustomerOrderEntity> allSubOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .and(wrapper -> {
                            for (String mainNo : validMainNos) {
                                wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                            }
                        })
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
        );

        // 按主单号分组
//        Map<String, List<TmsCustomerOrderEntity>> resultMap = new HashMap<>();
//        for (String mainNo : validMainNos) {
//            List<TmsCustomerOrderEntity> subList = allSubOrders.stream()
//                    .filter(order -> order.getEntrustedOrderNumber().startsWith(mainNo))
//                    .collect(Collectors.toList());
//            resultMap.put(mainNo, subList);
//        }
        return R.ok(allSubOrders);
    }

    // 根据派送任务号查询子单号
    @Override
    public R getDeliverySubFlag(String taskNo) {
        // 根据派送任务单号查询主单
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getDTaskOrder, taskNo);
        queryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, false);

        List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(queryWrapper);

        // 判断是否有主单
        if (CollUtil.isEmpty(customerOrders)) {
            return R.ok("");
        }

        // 获取全部主单号
        List<String> validMainNos = customerOrders.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .filter(orderNo -> !orderNo.isEmpty())
                .collect(Collectors.toList());

        // 构造查询条件，查出所有主单号下的子单
        List<TmsCustomerOrderEntity> allSubOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .and(wrapper -> {
                            for (String mainNo : validMainNos) {
                                wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                            }
                        })
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
        );

        return R.ok(allSubOrders);
    }


    // 揽收-根据司机id和状态查询子单号（查询扫描列表）
    @Override
    public R getPickUpSubFlag(Integer driverId, Integer isTask) {
        // 根据司机id与任务类型查询子单号
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> traskWrapper = new MPJLambdaWrapper<TmsTransportTaskOrderEntity>()
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                .eq(TmsTransportTaskOrderEntity::getTaskType, isTask)
                .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode())
                .isNotNull(TmsCustomerOrderEntity::getEntrustedOrderNumber);   // 避免空值

        // 如果是揽收则使用p_task_order字段关联
        if  (isTask == 1) {
            traskWrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getPTaskOrder,TmsTransportTaskOrderEntity::getTaskOrderNo);
        } else if (isTask == 2) {
            // 如果是派送则使用d_task_order字段关联
            traskWrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getDTaskOrder,TmsTransportTaskOrderEntity::getTaskOrderNo);
        }

        List<Map<String, Object>> maps = transportTaskOrderMapper.selectMaps(traskWrapper);

        if (null == maps || maps.isEmpty()) {
            return R.ok("");
        }

        List<String> entrustedOrderNos = maps.stream()
                .map(map -> map.get("entrusted_order_number").toString())
                .collect(Collectors.toList());


        // 过滤非主单号
        List<String> validMainNos = entrustedOrderNos.stream()
                .filter(no -> no.length() >= 13)
                .collect(Collectors.toList());

        if (validMainNos.isEmpty()) {
            return R.failed("请传入正确格式的主单号");
        }

        // 构造查询条件，查出所有主单号下的子单
        List<TmsCustomerOrderEntity> allSubOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .and(wrapper -> {
                            for (String mainNo : validMainNos) {
                                wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainNo);
                            }
                        })
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
        );

        return R.ok(allSubOrders);
    }

    // app-入库扫描单号
    @Override
    public R ibScan(String orderNo,Long warehouseId) {
        orderNo=isCustomerOrder(orderNo,false);
        // 根据单号查询订单信息
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
        );
        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return R.failed("未查询到相关订单");
        }
        // 判断订单是否是未入库
        TmsStorageandorderEntity tmsStorageandorderEntity = storageandorderMapper.selectOne(new LambdaQueryWrapper<TmsStorageandorderEntity>()
                .eq(TmsStorageandorderEntity::getOrderNo, orderNo)
                .eq(TmsStorageandorderEntity::getWarehouseId, warehouseId)
                .isNotNull(TmsStorageandorderEntity::getStorageOrderNo)
                .isNull(TmsStorageandorderEntity::getOutboundOrderNo).last("limit 1"));
        if (ObjectUtil.isNotNull(tmsStorageandorderEntity)) {
            return LocalizedR.failed("tms.app.driver.storage.order.no.error", orderNo);
        }
        return R.ok(Boolean.TRUE);
    }

    // app-仓库扫描入库
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R ibScanWarehouse(TmsStorageRecordAddVo vo) {
        if (CollUtil.isEmpty(vo.getOrderNo())) {
            return R.failed("入库订单为空");
        }
        if (ObjectUtil.isNull(vo.getWarehouseId())) {
            return R.failed("未选择仓库");
        }

        processOrderNumbersInPlace(vo.getOrderNo(),false);

        // 将vo.getOrderNo()全部转为主单，并将查询出来的主单结果去重
        List<String> orderNoList = vo.getOrderNo().stream().map(orderNo -> orderNo.substring(0, orderNo.length() - 3)).distinct().collect(Collectors.toList());

        // ***执行入库操作***
        // 根据单号查询订单信息
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getOrderNo())
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
        );


        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return R.failed("未查询到相关订单");
        }

        // 汇总总体积、数量、重量
        SummaryResultVo summaryResultVo = calculateSummary(orderList);

        // 生成入库批次号
        String storageOrderNumber = generateStorageOrderNumber();
        try {
            // ***生成入库记录***
            // 入库时间
            vo.setStorageTime(LocalDateTime.now());
            // 入库批次号
            vo.setStorageOrderNumber(storageOrderNumber);
            // 总数量
            vo.setCargoQuantity(summaryResultVo.getTotalQuantity());
            // 总重量
            vo.setTotalWeight(summaryResultVo.getTotalWeight());
            // 总体积
            vo.setTotalVolume(summaryResultVo.getTotalVolume());

            storageRecordMapper.insert(vo);

            // 订单绑定入库批次号
            List<TmsStorageandorderEntity> relationList = orderList.stream()
                    .map(order -> {
                        TmsStorageandorderEntity relation = new TmsStorageandorderEntity();
                        relation.setOrderNo(order.getEntrustedOrderNumber());
                        relation.setStorageOrderNo(storageOrderNumber);
                        relation.setWarehouseId(vo.getWarehouseId());
                        relation.setWarehouseType(vo.getWarehouseType());
                        return relation;
                    })
                    .collect(Collectors.toList());

            // 批量插入库存关系表
            storageandorderMapper.insert(relationList);

            // 查询是否已存在库存记录
            TmsInventoryManagementEntity inventory = inventoryManagementMapper.selectOne(
                    new LambdaQueryWrapper<TmsInventoryManagementEntity>()
                            .eq(TmsInventoryManagementEntity::getSiteId, vo.getWarehouseId())
            );

            // 如果不存在，插入新记录
            if (inventory == null) {
                TmsInventoryManagementEntity newInventory = new TmsInventoryManagementEntity();
                newInventory.setSiteId(vo.getWarehouseId());
                newInventory.setWarehouseType(vo.getWarehouseType());
                newInventory.setCargoQuantity(summaryResultVo.getTotalQuantity());
                newInventory.setTotalWeight(summaryResultVo.getTotalWeight());
                newInventory.setTotalVolume(summaryResultVo.getTotalVolume());
                newInventory.setOrderNum(orderList.size());
                inventoryManagementMapper.insert(newInventory);
            } else {
                // 如果已存在，则累加更新
                inventoryManagementMapper.update(
                        new LambdaUpdateWrapper<TmsInventoryManagementEntity>()
                                .eq(TmsInventoryManagementEntity::getSiteId, vo.getWarehouseId())
                                .setSql("cargo_quantity = cargo_quantity + " + summaryResultVo.getTotalQuantity())
                                .setSql("total_weight = total_weight + " + summaryResultVo.getTotalWeight())
                                .setSql("total_volume = total_volume + " + summaryResultVo.getTotalVolume())
                                .setSql("order_num = order_num + " + orderNoList.size())
                );
            }
            //当货物在一级仓进行扫码入库操作时,新增轨迹
            //判断当前入库的是否一级仓（此时是属于特殊情况的轨迹记录-送货到一级仓的时候）
            TmsSiteEntity siteEntity = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>().eq(TmsSiteEntity::getId, vo.getWarehouseId()),false);
            if(ObjectUtil.isNotNull(siteEntity) && siteEntity.getSiteType().equals(WarehouseType.ONE_LEVEL.getCode())){
                //查询出入库记录表
                List<String> entrustedOrderNumbers = orderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
//                //保证是主单（13位，过滤掉15位的）--因为此时orderList里全是子单，所以无需截取
//                entrustedOrderNumbers = entrustedOrderNumbers.stream().filter(orderNo -> orderNo.length()==13).collect(Collectors.toList());
                Map<String, TmsStorageandorderEntity> storageAndorderMap = storageandorderMapper.selectList(new LambdaQueryWrapper<TmsStorageandorderEntity>()
                                .in(TmsStorageandorderEntity::getOrderNo, entrustedOrderNumbers))
                        .stream().collect(Collectors.toMap(TmsStorageandorderEntity::getOrderNo,Function.identity()));
                Map<String, String> saved = new HashMap<>();
                for (TmsCustomerOrderEntity tmsCustomerOrderEntity : orderList) {
                    // 判断是属于一级仓并且是第一次入库的时候记录轨迹(送货到仓到一级仓的情况)
                    if(tmsCustomerOrderEntity.getReceiveType().equals(ReceiveType.DELIVERY_TO_WAREHOUSE.getCode())
                            && ObjectUtil.isNull(storageAndorderMap.get(tmsCustomerOrderEntity.getEntrustedOrderNumber()))){
                        String subOrderNo=tmsCustomerOrderEntity.getEntrustedOrderNumber();
                        if(tmsCustomerOrderEntity.getEntrustedOrderNumber().length()>15){
                            subOrderNo = tmsCustomerOrderEntity.getEntrustedOrderNumber().substring(0, 13);
                        }
                        if(!saved.containsKey(subOrderNo)){
                            //保证是送货到一级仓的情况（扫描入库的时候仓库是一级仓并且是第一次入库记录轨迹），防止一单多票重复记录轨迹
                            orderTrackService.saveTrack(subOrderNo, tmsCustomerOrderEntity.getCustomerOrderNumber(), NewOrderStatus.IN_TRANSIT.getValue(),OrderTrackLink.WAREHOUSE_RECEIVED.getCode());
                            saved.put(subOrderNo, subOrderNo);
                        }

                    }
                }
            }

        } catch (Exception e) {
            log.error("创建入库记录失败: {}", e.getMessage(), e);
            return LocalizedR.failed("tms.failed.to.create.storage.record", "");
        }

        return LocalizedR.ok("tms.successfully.created.record", "");
    }


    //把客户单号替换成主单号或者子单号
    public List<String> processOrderNumbersInPlace(List<String> orderNoList,Boolean needEntrustedOrderNumberFlag) {
        if (orderNoList == null || orderNoList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> updatedList = new ArrayList<>();
        for (String orderNo : orderNoList) {
            TmsCustomerOrderEntity customerOrder = tmsCustomerOrderService.getByOrderByCustomerOrderNumber(orderNo);
            if (customerOrder != null && customerOrder.getId() != null && customerOrder.getIsCustomerLabel()) {
                updatedList.add(needEntrustedOrderNumberFlag ? OrderTools.safeSubstringOrder(customerOrder.getEntrustedOrderNumber()) :customerOrder.getEntrustedOrderNumber());
                continue;
            }
            // 保留原始单号
            updatedList.add(orderNo);
        }
        // 替换原列表内容
        orderNoList.clear();
        orderNoList.addAll(updatedList);
        return orderNoList;
    }

    // app-出库扫描单号
    @Override
    public R outScan(String orderNo, Long warehouseId) {
        orderNo=isCustomerOrder(orderNo,false);
        // 根据单号查询订单信息
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
        );
        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return R.failed("未查询到相关订单");
        }
        // 判断扫描单号是否属于当前仓库
        TmsStorageandorderEntity tmsStorageandorderEntity = storageandorderMapper.selectOne(new LambdaQueryWrapper<TmsStorageandorderEntity>()
                .eq(TmsStorageandorderEntity::getOrderNo, orderNo)
                .eq(TmsStorageandorderEntity::getWarehouseId, warehouseId)
                .isNotNull(TmsStorageandorderEntity::getStorageOrderNo)
                .isNull(TmsStorageandorderEntity::getOutboundOrderNo), false);
        if (ObjectUtil.isNull(tmsStorageandorderEntity)) {
            return LocalizedR.failed("tms.app.driver.order.not.storage", orderNo);
        }
        return R.ok(Boolean.TRUE);
    }

    // app-扫描生成出库记录
    @Override
    public R outScanWarehouse(TmsOutboundRecordDto vo) {
        if (CollUtil.isEmpty(vo.getEntrustedOrderNumbers())) {
            return R.failed("出库订单为空");
        }
        if (ObjectUtil.isNull(vo.getWarehouseId())) {
            return R.failed("未选择仓库");
        }
        processOrderNumbersInPlace(vo.getEntrustedOrderNumbers(),false);

        // 将vo.getEntrustedOrderNumbers()全部转为主单，并将查询出来的主单结果去重
        List<String> orderNoList = vo.getEntrustedOrderNumbers().stream().map(orderNo -> orderNo.substring(0, orderNo.length() - 3))
                .distinct().collect(Collectors.toList());

        // ***执行出库操作***
        // 根据单号查询订单信息
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumbers())
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
        );

        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return R.failed("未查询到相关订单");
        }


        // 汇总总体积、数量、重量
        SummaryResultVo summaryResultVo = calculateSummary(orderList);

        // 出库单号（出库批次号）
        String outboundOrderNumber = generateOutboundOrderNumber();
        try {
            //新增出库记录
            TmsOutboundRecordEntity tmsOutboundRecordEntity = new TmsOutboundRecordEntity();
            //出库时间
            tmsOutboundRecordEntity.setOutboundTime(LocalDateTime.now());
            //出库批次号
            tmsOutboundRecordEntity.setOutboundOrderNumber(outboundOrderNumber);
            tmsOutboundRecordEntity.setWarehouseId(vo.getWarehouseId());
            //仓库名称
            tmsOutboundRecordEntity.setWarehouseName(vo.getWarehouseName());
            //仓库类型
            tmsOutboundRecordEntity.setWarehouseType(vo.getWarehouseType());
            //出库货品数量
            tmsOutboundRecordEntity.setCargoQuantity(summaryResultVo.getTotalQuantity());
            tmsOutboundRecordEntity.setTotalWeight(summaryResultVo.getTotalWeight());
            tmsOutboundRecordEntity.setTotalVolume(summaryResultVo.getTotalVolume());
            outboundRecordMapper.insert(tmsOutboundRecordEntity);

            // 修改库存关系表，填充出库单号
            for (String subOrder : vo.getEntrustedOrderNumbers()) {
                // 直接更新出库单号
                TmsStorageandorderEntity updateEntity = new TmsStorageandorderEntity();
                updateEntity.setOutboundOrderNo(outboundOrderNumber);
                storageandorderMapper.update(updateEntity, new LambdaUpdateWrapper<TmsStorageandorderEntity>()
                        .eq(TmsStorageandorderEntity::getOrderNo,subOrder)
                        .isNotNull(TmsStorageandorderEntity::getStorageOrderNo)   // 必须已经入库
                        .isNull(TmsStorageandorderEntity::getOutboundOrderNo)     // 不能出过库
                        .eq(TmsStorageandorderEntity::getWarehouseId, vo.getWarehouseId()));
            }


            //减库存
            TmsInventoryManagementEntity tmsInventoryManagementEntity = inventoryManagementMapper.selectOne(new LambdaQueryWrapper<TmsInventoryManagementEntity>()
                    .eq(TmsInventoryManagementEntity::getSiteId, vo.getWarehouseId()), false);
            if (ObjectUtil.isNotNull(tmsInventoryManagementEntity)) {
                tmsInventoryManagementEntity.setCargoQuantity(tmsInventoryManagementEntity.getCargoQuantity() - summaryResultVo.getTotalQuantity());
                tmsInventoryManagementEntity.setTotalWeight(tmsInventoryManagementEntity.getTotalWeight().subtract(summaryResultVo.getTotalWeight()));
                tmsInventoryManagementEntity.setTotalVolume(tmsInventoryManagementEntity.getTotalVolume().subtract(summaryResultVo.getTotalVolume()));
                //票数
                tmsInventoryManagementEntity.setOrderNum(tmsInventoryManagementEntity.getOrderNum() - orderNoList.size());
                inventoryManagementMapper.updateById(tmsInventoryManagementEntity);
            }

        } catch (Exception e) {
            log.error("创建出库记录失败: {}", e.getMessage(), e);
            return LocalizedR.failed(e.getMessage(), "tms.failed.to.create.outbound.record", "");
        }
        return LocalizedR.ok("tms.successfully.created.record", "");
    }

    // app-笼车扫描
    @Override
    public R cageScan(String cageCode) {
        // 根据标签编码查询是否存在
        TmsLabelEntity tmsLabel = labelMapper.selectOne(new LambdaQueryWrapper<TmsLabelEntity>()
                .eq(TmsLabelEntity::getLabelCode, cageCode),false);

        if (ObjectUtil.isNull(tmsLabel)) {
            return LocalizedR.failed("tms.app.driver.cage.not.exist", cageCode);
        }
        // 根据铁笼仓库id查询仓库信息
        TmsSiteEntity tmsSiteEntity = tmsSiteMapper.selectById(tmsLabel.getSiteId());
        TmsSiteEntity tmsSiteStart = tmsSiteMapper.selectById(tmsLabel.getStartSiteId());
        TmsSiteEntity tmsSiteEnd = tmsSiteMapper.selectById(tmsLabel.getEndSiteId());

        Map<String, Object> map = new HashMap<>();
        map.put("siteName", tmsSiteEntity.getSiteName()==null?"":tmsSiteEntity.getSiteName());
        map.put("startSiteName", tmsSiteStart.getSiteName()==null?"":tmsSiteStart.getSiteName());
        map.put("endSiteName", tmsSiteEnd.getSiteName()==null?"":tmsSiteEnd.getSiteName());

        return R.ok(map);
    }

    // app-笼车扫描单号
    @Override
    public R orderScan(String orderNo,String labelCode) {
        orderNo=isCustomerOrder(orderNo,false);
        // 判断订单是否存在
        TmsCustomerOrderEntity order = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo), false);
        if (ObjectUtil.isNull(order)) {
            return LocalizedR.failed("tms.app.driver.cage.order.not.exist", orderNo);
        }

        // 确保截取主单进行操作
        if (orderNo.length() > 15){
            orderNo = orderNo.substring(0, orderNo.length() - 3);
        }
        // 判断订单是否已经存在入笼记录
        TmsCageAndOrderEntity cageAndOrder = cageAndOrderMapper.selectOne(new LambdaQueryWrapper<TmsCageAndOrderEntity>()
                .eq(TmsCageAndOrderEntity::getOrderNo, orderNo)
                .eq(TmsCageAndOrderEntity::getStatus, 0), false);
        if (ObjectUtil.isNotNull(cageAndOrder)) {
            return LocalizedR.failed("tms.app.driver.cage.order.record.exist", order.getEntrustedOrderNumber());
        }

        // 获取订单当前轨迹节点编号
/*        Integer currentNodeCode = Optional.ofNullable(orderTrackService.getMaxStatusCodeByOrderNo(orderNo))
                .map(TmsOrderTrackEntity::getStatusCode)
                .orElse(0);*/

        if (order.getOrderStatus() < 4){
            // 状态节点小于4运输中，则走第一条干线逻辑，二级仓前往一级仓
            return R.ok(Boolean.TRUE);
        }else{
            // 状态节点大于4则走第二条干线逻辑，一级仓前往二级仓，根据订单目的地与标签目的地仓库比较
            ArrayList<String> messageList = new ArrayList<>();
            //获取发货地邮编和收货地邮编
            String startPostalPrefix = Optional.ofNullable(order.getShipperPostalCode())
                    .map(code -> code.substring(0, Math.min(3, code.length())))
                    .orElse("null");
            String destPostalPrefix = Optional.ofNullable(order.getDestPostalCode())
                    .map(code -> code.substring(0, Math.min(3, code.length())))
                    .orElse("null");
            boolean startFlag = checkRoute(labelCode, startPostalPrefix, true);
            boolean endFlag = checkRoute(labelCode, destPostalPrefix, false);
            if (endFlag) {
                System.out.println("验证通过");
            } else {
                if (order.getEntrustedOrderNumber().length()>15){
                    return LocalizedR.failed("tms.app.driver.cage.order.not.mismatching", order.getEntrustedOrderNumber());
                }
                //根据主单号获取子单号
                List<TmsCustomerOrderEntity> subOrderNoByTrackNo = tmsCustomerOrderService.getSubOrderNoByTrackNo(order.getEntrustedOrderNumber());
                return LocalizedR.failed("tms.app.driver.cage.order.not.mismatching", subOrderNoByTrackNo.get(0).getEntrustedOrderNumber());
            }

        }
        return R.ok(Boolean.TRUE);
    }

    // app扫描入笼
    @Transactional
    @Override
    public R scanOrderAndCage(TmsAppCageAndOrderVo vo) {
        processOrderNumbersInPlace(vo.getEntrustedOrderNumber(),false);
        //先查询一下该笼子编码对应的干线是否存在（笼子编码跟干线任务单号一一对应）
        TmsLineHaulOrderEntity lineHaulOrder = lineHaulOrderMapper.selectOne(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .eq(TmsLineHaulOrderEntity::getLineHaulNo, vo.getCageCode()));
        if (ObjectUtil.isNotNull(lineHaulOrder)) {
            //运输中、已完成的标签不允许入笼
            if(lineHaulOrder.getTaskStatus().equals(TransportTaskStatus.DELIVERED.getCode())
                    || lineHaulOrder.getTaskStatus().equals(TransportTaskStatus.IN_TRANSIT.getCode())){
                return R.failed(LocalizedR.getMessage("tms.app.driver.label.task.in.progress", null));
            }
        }


        if (ObjectUtil.isNull(vo)) {
            return R.failed("Please select the order.");
        }

        // 将vo.orderNo()全部转为主单，并将查询出来的主单结果去重      >15则截取
        List<String> orderNoList = vo.getEntrustedOrderNumber().stream()
                .map(orderNo -> {
                    if (orderNo.length() > 15) {
                        return orderNo.substring(0, orderNo.length() - 3);  // 长度>15则截取
                    } else {
                        return orderNo;  // 长度≤15则保留原样
                    }
                })
                .distinct()
                .collect(Collectors.toList());

        // 根据单号查询订单信息
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoList)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
        );

        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return R.failed("未查询到相关订单");
        }


        //记录这个笼子里的订单的订单数量、货物数量、总重量、总体积
        int totalQuantity = 0;
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        // 插入入笼记录
        List<TmsCageAndOrderEntity> cageAndOrderList = new ArrayList<>();
        for (TmsCustomerOrderEntity order : orderList) {
            TmsCageAndOrderEntity tmsCageAndOrderEntity = new TmsCageAndOrderEntity();
            tmsCageAndOrderEntity.setCageCode(vo.getCageCode());
            tmsCageAndOrderEntity.setOrderNo(order.getEntrustedOrderNumber());
            tmsCageAndOrderEntity.setBindTime(LocalDateTime.now());
            cageAndOrderList.add(tmsCageAndOrderEntity);
            totalQuantity += order.getCargoQuantity();
            totalWeight = totalWeight.add(order.getTotalWeight());
            totalVolume = totalVolume.add(order.getTotalVolume());
        }
        cageAndOrderMapper.insert(cageAndOrderList);

        // 入笼成功更新当前标签编码的订单数量
        labelMapper.update(new LambdaUpdateWrapper<TmsLabelEntity>()
                .eq(TmsLabelEntity::getLabelCode, vo.getCageCode())
//                .set(TmsLabelEntity::getOrderNumber, orderList.size()));
                .setSql("order_number=order_number+" + orderList.size()));

        //标记需要干线运输
        //当前笼车
        TmsLabelEntity tmsLabel = labelMapper.selectOne(new LambdaQueryWrapper<TmsLabelEntity>()
                .eq(TmsLabelEntity::getLabelCode, vo.getCageCode()), false);

        if (ObjectUtil.isNull(tmsLabel)) {
            return R.failed(LocalizedR.getMessage("tms.app.driver.label.not.exist", null));
        }
        //标签的始发仓库
        TmsSiteEntity siteEntity = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getId, tmsLabel.getStartSiteId()), false);

        if (ObjectUtil.isNull(siteEntity)) {
            return R.failed(LocalizedR.getMessage("tms.app.driver.start.site.not.exist", null));
        }
        //确定干线的始发仓库
        //筛选出需要干线运输的订单（目的地不是当前标签的始发仓库的订单）
        List<TmsCustomerOrderEntity> customerOrders = orderList.stream()
                .filter(customerOrder -> !(customerOrder.getDeliveryWarehouseId().equals(siteEntity.getId()))).collect(Collectors.toList());
        if (ObjectUtil.isNotNull(customerOrders)) {
            //将这些订单标记为需要干线运输（指派后建立干线任务与订单的关系表）
            for (TmsCustomerOrderEntity customerOrder : customerOrders) {
                //设置需要干线标记(即重新标记为需要干线任务)
                customerOrder.setNeedLineHaul(Boolean.TRUE);
                //用此时标签上的始发仓库作为此次干线任务的起点仓库
                customerOrder.setCurrentWarehouseId(tmsLabel.getStartSiteId());
                customerOrder.setLineHaulDestWarehouseId(tmsLabel.getEndSiteId());
                //更新此时的订单为未扫描状态-多次干线共用订单是否扫描，第一次干线扫码提货后会将其标记为已扫描，第二次干线开始前的扫码入笼操作将其设置为未扫描，可以重新保证第二次干线任务提货时是未扫描状态
                customerOrder.setIsScan(Boolean.FALSE);
            }
        }
        customerOrderMapper.updateById(customerOrders);

        if(ObjectUtil.isNotNull(lineHaulOrder)){
            //干线任务已经存在，此时说明是已经生成过干线任务（两种情况，1：有移出过笼子的再移入进来的情况，2：已经有一些订单了再往笼子里添加一些订单的情况）
            List<TmsOrderLineHaulRelationEntity> tmsOrderLineHaulRelationEntities = new ArrayList<>();
            for (TmsCustomerOrderEntity customerOrder : orderList) {
                //新增订单与干线绑定关系
                TmsOrderLineHaulRelationEntity tmsOrderLineHaulRelationEntity = new TmsOrderLineHaulRelationEntity();
                tmsOrderLineHaulRelationEntity.setCustomerOrderId(customerOrder.getId());
                tmsOrderLineHaulRelationEntity.setCustomerOrderNumber(customerOrder.getCustomerOrderNumber());
                tmsOrderLineHaulRelationEntity.setEntrustedOrderNumber(customerOrder.getEntrustedOrderNumber());
                tmsOrderLineHaulRelationEntity.setLineHaulNo(vo.getCageCode());
                tmsOrderLineHaulRelationEntities.add(tmsOrderLineHaulRelationEntity);
            }
            //新增干线任务与订单关系
            tmsOrderLineHaulRelationMapper.insert(tmsOrderLineHaulRelationEntities);
            //更新统计的数量、重量、体积、货物数量
            lineHaulOrderMapper.update(new LambdaUpdateWrapper<TmsLineHaulOrderEntity>()
                    .eq(TmsLineHaulOrderEntity::getLineHaulNo, vo.getCageCode())
                    .set(TmsLineHaulOrderEntity::getTotalWeight, lineHaulOrder.getTotalWeight().add(totalWeight))
                    .set(TmsLineHaulOrderEntity::getTotalVolume, lineHaulOrder.getTotalVolume().add(totalVolume))
                    .set(TmsLineHaulOrderEntity::getOrderCount, lineHaulOrder.getOrderCount() + orderList.size())
                    .set(TmsLineHaulOrderEntity::getTotalQuantity, lineHaulOrder.getTotalQuantity() + totalQuantity)
            );
        }else{
            //扫描入笼成功后直接根据笼车的编码生成干线任务
            TmsLineHaulOrderEntity tmsLineHaulOrderEntity = new TmsLineHaulOrderEntity();
            //用笼车的标签编码作为干线任务单号
            tmsLineHaulOrderEntity.setLineHaulNo(vo.getCageCode());
            //待提货状态
            tmsLineHaulOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //总重量、总体积、货物数量、订单数量
            tmsLineHaulOrderEntity.setTotalWeight(totalWeight);
            tmsLineHaulOrderEntity.setTotalVolume(totalVolume);
            tmsLineHaulOrderEntity.setTotalQuantity(totalQuantity);
            tmsLineHaulOrderEntity.setOrderCount(orderList.size());
            //起点仓库和终点仓库
            tmsLineHaulOrderEntity.setOriginWarehouseId(tmsLabel.getStartSiteId());
            tmsLineHaulOrderEntity.setDestWarehouseId(tmsLabel.getEndSiteId());
            //干线默认整车
            tmsLineHaulOrderEntity.setTransportType(1);
            //新建干线任务单
            lineHaulOrderMapper.insert(tmsLineHaulOrderEntity);

            //维护干线任务与订单的关系
            //任务单-客户单（多次干线任务）-存中间表
            List<TmsOrderLineHaulRelationEntity> tmsOrderLineHaulRelationEntities = new ArrayList<>();
            orderList.forEach(customerOrder -> {
                TmsOrderLineHaulRelationEntity tmsOrderLineHaulRelationEntity = new TmsOrderLineHaulRelationEntity();
                tmsOrderLineHaulRelationEntity.setCustomerOrderId(customerOrder.getId());
                tmsOrderLineHaulRelationEntity.setCustomerOrderNumber(customerOrder.getCustomerOrderNumber());
                tmsOrderLineHaulRelationEntity.setEntrustedOrderNumber(customerOrder.getEntrustedOrderNumber());
                tmsOrderLineHaulRelationEntity.setLineHaulNo(vo.getCageCode());
                tmsOrderLineHaulRelationEntities.add(tmsOrderLineHaulRelationEntity);
            });
            //新建干线任务与订单关系
            tmsOrderLineHaulRelationMapper.insert(tmsOrderLineHaulRelationEntities);
        }
        //扫码入笼后，标签标记为已使用状态
        labelMapper.update(new LambdaUpdateWrapper<TmsLabelEntity>()
                .eq(TmsLabelEntity::getLabelCode, vo.getCageCode())
                .set(TmsLabelEntity::getLabelStatus, 1)
        );

        return R.ok();
    }


    //校验是路线是否正确
    private boolean checkRoute(String labelCode, String zipCode,boolean isStart) {
        Long id=0L;
        TmsLabelEntity label = tmsLabelService.getByLabelCode(labelCode);
        if (isStart){
            id= label.getStartSiteId();
        }else {
            id= label.getEndSiteId();
        }
        if (label != null) {
            // 通过仓库ID获取对应的覆盖区域
            TmsOverAreaEntity overArea = tmsOverAreaService.getBySiteId(id);
            if (overArea != null && overArea.getZip() != null) {
                // 格式化 ZIPCODE（全部转为大写）
                String formattedZipCode = zipCode != null ? zipCode.toUpperCase() : "";
                String formattedAreaZip = overArea.getZip().toUpperCase();
                if (formattedAreaZip.contains(formattedZipCode)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 生成入库单号
     * 格式：IB + 年月日 + Snowflake ID
     *
     * @return
     */
    private synchronized String generateStorageOrderNumber() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        return "IB" + datePart + shortId;
    }

    /**
     * 生成出库单号
     * 格式：CK + 年月日 + Snowflake ID
     *
     * @return
     */
    private synchronized String generateOutboundOrderNumber() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        return "CK" + datePart + shortId;
    }

    /**
     * 汇总总体积、数量、重量
     */
    private SummaryResultVo calculateSummary(List<TmsCustomerOrderEntity> customerList) {
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        Integer totalQuantity = 0;
        for (TmsCustomerOrderEntity customerOrder : customerList) {
            // 直接累加货物的重量
            totalWeight = totalWeight.add(customerOrder.getTotalWeight());
            // 直接累加货物的体积
            totalVolume = totalVolume.add(customerOrder.getTotalVolume());
            // 直接累加货物的数量
            totalQuantity = totalQuantity + customerOrder.getCargoQuantity();
        }
        SummaryResultVo result = new SummaryResultVo();
        result.setTotalWeight(totalWeight);
        result.setTotalVolume(totalVolume);
        result.setTotalQuantity(totalQuantity);

        return result;
    }

    /**
     * 根据状态查询干线单列表
     *
     * @param status
     * @param driverId
     * @return
     */
    @Override
    public R getLineHaulOrderListByStatus(Integer status, Long driverId) {
        // 1.查询干线任务单列表
        MPJLambdaWrapper<TmsLineHaulOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsLineHaulOrderEntity.class)
                .eq(TmsLineHaulOrderEntity::getTaskStatus, status)
                .eq(TmsLineHaulOrderEntity::getDriverId, driverId)
                .orderByDesc(TmsLineHaulOrderEntity::getCreateTime);
        List<TmsLineHaulOrderEntity> lineHaulOrderList = lineHaulOrderMapper.selectList(wrapper);
        // 2.收集所有仓库ID
        Set<Long> siteIds = lineHaulOrderList.stream()
                .flatMap(order -> Stream.of(order.getOriginWarehouseId(), order.getDestWarehouseId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 3.批量查询仓库信息
        Map<Long, TmsSiteEntity> siteMap = siteIds.isEmpty() ?
                Collections.emptyMap() : tmsSiteMapper.selectList(new LambdaQueryWrapper<TmsSiteEntity>()
                .in(TmsSiteEntity::getId, siteIds)
                .select(
                        TmsSiteEntity::getId,
                        TmsSiteEntity::getSiteName,
                        TmsSiteEntity::getSiteAddress,
                        TmsSiteEntity::getSiteLng,
                        TmsSiteEntity::getSiteLat
                )
        ).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));

        // 4. 构建返回结果
        List<Map<String, Object>> result = lineHaulOrderList.stream().map(order -> {
            Map<String, Object> orderMap = new LinkedHashMap<>();
            // 干线任务单信息
            orderMap.put("id", order.getId());
            orderMap.put("lineHaulNo", order.getLineHaulNo());
            orderMap.put("transportType", order.getTransportType());
            orderMap.put("taskStatus", order.getTaskStatus());
            orderMap.put("driverId", order.getDriverId());
            orderMap.put("contactPhone", order.getContactPhone());
            orderMap.put("licensePlate", order.getLicensePlate());
            orderMap.put("originWarehouseId", order.getOriginWarehouseId());
            orderMap.put("destWarehouseId", order.getDestWarehouseId());
            orderMap.put("plannedDepartureStartTime", order.getPlannedDepartureStartTime());
            orderMap.put("plannedDepartureEndTime", order.getPlannedDepartureEndTime());
            orderMap.put("totalQuantity", order.getTotalQuantity());
            orderMap.put("totalVolume", order.getTotalVolume());
            orderMap.put("totalWeight", order.getTotalWeight());
            orderMap.put("pickupProof", order.getPickupProof());
            orderMap.put("deliveryProof", order.getDeliveryProof());
            orderMap.put("pickupTime", order.getPickupTime());
            orderMap.put("deliveryTime", order.getDeliveryTime());
            orderMap.put("createTime", order.getCreateTime());
            orderMap.put("isScan", order.getIsScan());

            // 添加始发仓信息
            if (order.getOriginWarehouseId() != null && siteMap.containsKey(order.getOriginWarehouseId())) {
                TmsSiteEntity originSite = siteMap.get(order.getOriginWarehouseId());
                Map<String, Object> originInfo = new LinkedHashMap<>();
                originInfo.put("originWarehouseId", originSite.getId());
                originInfo.put("originSiteName", originSite.getSiteName());
                originInfo.put("originSiteAddress", originSite.getSiteAddress());
                originInfo.put("originSiteLng", originSite.getSiteLng());
                originInfo.put("originSiteLat", originSite.getSiteLat());
                orderMap.put("originSite", originInfo);
            }

            // 添加目的仓信息
            if (order.getDestWarehouseId() != null && siteMap.containsKey(order.getDestWarehouseId())) {
                TmsSiteEntity destSite = siteMap.get(order.getDestWarehouseId());
                Map<String, Object> destInfo = new LinkedHashMap<>();
                destInfo.put("destWarehouseId", destSite.getId());
                destInfo.put("destSiteName", destSite.getSiteName());
                destInfo.put("destSiteAddress", destSite.getSiteAddress());
                destInfo.put("destSiteLng", destSite.getSiteLng());
                destInfo.put("destSiteLat", destSite.getSiteLat());
                orderMap.put("destSite", destInfo);
            }
            return orderMap;
        }).collect(Collectors.toList());

        return R.ok(result);
    }


    /**
     * 干线上传提货凭证
     *
     * @param lineHaulOrderNo
     * @param pickupProof
     * @return
     */
    @Transactional
    @Override
    public R uploadLinePickupProof(String lineHaulOrderNo, String pickupProof) {

        // 1. 查询干线任务单
        TmsLineHaulOrderEntity lineHaulOrder = lineHaulOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulOrderNo)
        );

        if (lineHaulOrder == null) {
            return LocalizedR.failed("tms.app.driver.order.not.found", lineHaulOrderNo);
        }
//        //查询干线单下的所有客户订单（主单和子单）
//        Set<String> customerOrderNumbers = tmsOrderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
//                        .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, lineHaulOrderNo)).stream()
//                .map(TmsOrderLineHaulRelationEntity::getCustomerOrderNumber).collect(Collectors.toSet());
//        List<TmsCustomerOrderEntity> allCustomerOrders = new ArrayList<>();
//        if (ObjectUtil.isNotNull(customerOrderNumbers) && CollUtil.isNotEmpty(customerOrderNumbers)) {
//            //根据客户单号查询出所有客户单（主单主单和子单）
//            allCustomerOrders = customerOrderMapper.selectList(
//                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
//                            .in(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumbers)
//            );
//        }
//        if (CollUtil.isEmpty(allCustomerOrders)) {
//            return LocalizedR.failed("tms.app.driver.order.not.found", "");
//        }
//        //主单(过滤掉子单)
//        List<TmsCustomerOrderEntity> mainOrders = allCustomerOrders.stream()
//                .filter(customerOrder -> customerOrder.getSubFlag().equals(Boolean.FALSE)).collect(Collectors.toList());


        // 4. 更新干线任务单的取货证明
        if (StrUtil.isNotBlank(pickupProof)) {
            lineHaulOrder.setPickupProof(pickupProof);
            //保存提货时间
            lineHaulOrder.setPickupTime(LocalDateTime.now());
            lineHaulOrderMapper.updateById(lineHaulOrder);
        }

//        // 5. 批量更新关联订单状态
//        List<String> orderNumbers = allCustomerOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
//
//        int updateCount = customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
//                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNumbers)
//                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.IN_TRANSIT.getCode())
//        );
//
//        if (updateCount == 0) {
//            return LocalizedR.failed("tms.app.driver.order.update.status.error", "");
//        }

//        // 7. 更新干线任务单状态为运输中
//        lineHaulOrder.setTaskStatus(TransportTaskStatus.IN_TRANSIT.getCode());
//        lineHaulOrderMapper.updateById(lineHaulOrder);
        return R.ok(Boolean.TRUE);
    }


    /**
     * 干线上传送货凭证
     *
     * @param lineHaulOrderNo
     * @param deliveryProof
     * @return
     */
    @Override
    public R uploadLineDeliveryProof(String lineHaulOrderNo, String deliveryProof) {
        // 1. 查询干线任务单
        TmsLineHaulOrderEntity lineHaulOrder = lineHaulOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulOrderNo)
        );

        if (lineHaulOrder == null) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.app.driver.order.not.found",null));
        }

        //将该干线任务单修改为已完成状态
        lineHaulOrder.setTaskStatus(TransportTaskStatus.DELIVERED.getCode());

        // 2. 查询干线单下的所有客户订单（主单和子单）
        Set<String> entrustedOrderNumbers = tmsOrderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
                        .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, lineHaulOrderNo)).stream()
                .map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toSet());

        List<TmsCustomerOrderEntity> allCustomerOrders = new ArrayList<>();
        if (ObjectUtil.isNotNull(entrustedOrderNumbers) && CollUtil.isNotEmpty(entrustedOrderNumbers)) {
            //根据跟踪单号查询出所有客户单（主单主单和子单）
            allCustomerOrders = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers)
            );
        }
        if (CollUtil.isEmpty(allCustomerOrders)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.app.driver.order.not.found", null));
        }
        // 4. 更新干线任务单的取货证明
        if (StrUtil.isNotBlank(deliveryProof)) {
            lineHaulOrder.setDeliveryProof(deliveryProof);
            //保存送货时间
            lineHaulOrder.setDeliveryTime(LocalDateTime.now());
        }

        // 5. 批量更新关联订单状态(主子单)
        List<String> orderNumbers = allCustomerOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());

        //主单(过滤掉子单)
        List<TmsCustomerOrderEntity> mainOrders = allCustomerOrders.stream()
                .filter(customerOrder -> customerOrder.getSubFlag().equals(Boolean.FALSE)).collect(Collectors.toList());
        //修改为运输中
        int updateCount = customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNumbers)
                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.IN_TRANSIT.getCode())
        );

        // 保存轨迹
        //判断当前干线任务的目的地仓库是一级仓还是二级仓
        TmsSiteEntity siteEntity = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getId, lineHaulOrder.getDestWarehouseId()));
        if (ObjectUtil.isNotNull(siteEntity)) {
            if (siteEntity.getSiteType().equals(WarehouseType.ONE_LEVEL.getCode())) {
                //干线任务从二级仓送到一级仓
                //记录轨迹（主单和子单分别记录）
                mainOrders.forEach(order -> {
                    if (order.getReceiveType().equals(ReceiveType.DOOR_PICKUP.getCode())) {
                        //上门揽收
                        orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
                                NewOrderStatus.IN_TRANSIT.getValue(), OrderTrackLink.WAREHOUSE_RECEIVED.getCode());

                    }
//                    else{
//                        //送货到仓-不会存在送货到仓二级转一级的情况
//                        orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
//                                NewOrderStatus.IN_TRANSIT.getValue(), "", "到达派送站点(最后一公里配送中心)", 0);
//                    }
                });

            } else {
                //干线任务从一级仓送到二级仓
                mainOrders.forEach(order -> {
                    if (order.getReceiveType().equals(ReceiveType.DOOR_PICKUP.getCode())) {
                        //上门揽收
                        orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
                                NewOrderStatus.IN_TRANSIT.getValue(), OrderTrackLink.TWO_WAREHOUSE_RECEIVED.getCode());

                    } else {
                        //送货到仓
                        orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
                                NewOrderStatus.IN_TRANSIT.getValue(),OrderTrackLink.TWO_WAREHOUSE_RECEIVED.getCode());
                    }
                });
            }
        }

        //将干线任务批次对应订单所绑定的标签、容器解绑
        Set<String> mainOrderEntrustedOrderNumbers = mainOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toSet());
        if (ObjectUtil.isNotNull(mainOrderEntrustedOrderNumbers) && CollUtil.isNotEmpty(mainOrderEntrustedOrderNumbers)) {
            List<TmsCageAndOrderEntity> tmsCageAndOrderEntities = cageAndOrderMapper.selectList(new LambdaQueryWrapper<TmsCageAndOrderEntity>()
                    .in(TmsCageAndOrderEntity::getOrderNo, mainOrderEntrustedOrderNumbers)
                    .eq(TmsCageAndOrderEntity::getStatus, 0));
            for (TmsCageAndOrderEntity tmsCageAndOrderEntity : tmsCageAndOrderEntities) {
                //设置容器为为解绑状态
                tmsCageAndOrderEntity.setStatus(1);
            }
            //更新-解绑容器和订单
            cageAndOrderMapper.updateById(tmsCageAndOrderEntities);
//            //收集容器编码（标签编码）
//            Set<String> cageCodes = tmsCageAndOrderEntities.stream().map(TmsCageAndOrderEntity::getCageCode).collect(Collectors.toSet());
//            //更新标签状态未已使用
//            labelMapper.update(new TmsLabelEntity(),
//                    new LambdaUpdateWrapper<TmsLabelEntity>()
//                            .in(TmsLabelEntity::getLabelCode, cageCodes)
//                            .set(TmsLabelEntity::getLabelStatus, 1)
//            );
        }
        //
        List<String> waitSign = new ArrayList<>();
        //干线任务完成后将客户订单的已扫描改回未扫描状态，给派送任务扫描标记使用或者下一次干线任务扫描使用
        for (TmsCustomerOrderEntity order : mainOrders) {
            //增加一个是否能路径规划判断字段（如果此时的干线任务的目的地仓库是订单的最终的派送目的地仓库则标记为客路径规划）
            if(siteEntity.getId().equals(order.getDeliveryWarehouseId())){
                waitSign.add(order.getEntrustedOrderNumber());
            }
            //改回未扫描状态
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber())
                    .set(TmsCustomerOrderEntity::getIsScan, Boolean.FALSE));
        }
        //更新客户订单
        if(ObjectUtil.isNotNull(waitSign) && CollUtil.isNotEmpty(waitSign)){
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, waitSign)
                    .set(TmsCustomerOrderEntity::getIsCanPathPlan, Boolean.TRUE));
        }


        int update = lineHaulOrderMapper.updateById(lineHaulOrder);
        if (update > 0) {
            // 干线任务运输完成自动执行入库操作allCustomerOrders
            Boolean b = lineAuthStorageRecord(lineHaulOrderNo,allCustomerOrders);
        }
        if (updateCount == 0) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.app.driver.order.update.status.error", null));
        }
        return R.ok(Boolean.TRUE);
    }


    /**
     * app-根据干线任务单查询详情
     *
     * @param lineHaulOrderNo
     * @return
     */
    @Override
    public R getLineHaulOrderDetail(String lineHaulOrderNo) {
        TmsAppMainLineTaskOrderDetailVo tmsAppMainLineTaskOrderDetailVo = new TmsAppMainLineTaskOrderDetailVo();
        TmsLineHaulOrderEntity lineHaulOrder = lineHaulOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulOrderNo)
        );

        if (ObjectUtil.isNull(lineHaulOrder)) {
            return R.failed(50008,"未找到该干线任务单");
        }
        //查询司机信息
        TmsLmdDriverEntity driver = this.getOne(
                new LambdaQueryWrapper<TmsLmdDriverEntity>()
                        .eq(TmsLmdDriverEntity::getDriverId, lineHaulOrder.getDriverId())
        );
        //始发地仓库
        TmsSiteEntity originWarehouse = tmsSiteMapper.selectOne(
                new LambdaQueryWrapper<TmsSiteEntity>()
                        .eq(TmsSiteEntity::getId, lineHaulOrder.getOriginWarehouseId())
        );
        //目的地仓库
        TmsSiteEntity destinationWarehouse = tmsSiteMapper.selectOne(
                new LambdaQueryWrapper<TmsSiteEntity>()
                        .eq(TmsSiteEntity::getId, lineHaulOrder.getDestWarehouseId())
        );
        //查询干线单下的所有客户订单(主单)
        Set<String> customerOrderNumbers = tmsOrderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
                        .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, lineHaulOrderNo)).stream()
                .map(TmsOrderLineHaulRelationEntity::getCustomerOrderNumber).collect(Collectors.toSet());
        List<TmsCustomerOrderEntity> allCustomerOrders = new ArrayList<>();
        if (ObjectUtil.isNotNull(customerOrderNumbers) && CollUtil.isNotEmpty(customerOrderNumbers)) {
            //根据客户单号查询出所有客户单（主单）
            allCustomerOrders = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumbers)
                            .eq(TmsCustomerOrderEntity::getSubFlag, false)
            );
        }
        //查询异常信息（如果有的话）
        List<TmsExceptionManagementEntity> tmsExceptionManagementEntityList = tmsExceptionManagementMapper.selectList(new LambdaQueryWrapper<TmsExceptionManagementEntity>()
                .eq(TmsExceptionManagementEntity::getDispatchOrderNo, lineHaulOrderNo));
        //过滤掉不是当前司机的异常信息
        List<TmsExceptionManagementEntity> filterExceptionManagementList = tmsExceptionManagementEntityList.stream()
                .filter(item -> item.getDriverName().equals(driver.getDriverName())).collect(Collectors.toList());
        //回填数据
        tmsAppMainLineTaskOrderDetailVo.setLineHaulOrder(lineHaulOrder);
        tmsAppMainLineTaskOrderDetailVo.setAllOrders(allCustomerOrders);
        tmsAppMainLineTaskOrderDetailVo.setTmsExceptionManagementEntity(filterExceptionManagementList);
        tmsAppMainLineTaskOrderDetailVo.setOriginWarehouse(originWarehouse);
        tmsAppMainLineTaskOrderDetailVo.setDestinationWarehouse(destinationWarehouse);
        return R.ok(tmsAppMainLineTaskOrderDetailVo);
    }

    //app干线任务单异常上报
    @Override
    public R appUploadException(TmsAppExceptionUploadDto tmsAppExceptionUploadDto) {
        //查询司机信息
        TmsLmdDriverEntity driver = this.getOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverId, tmsAppExceptionUploadDto.getDriverId()));
        if (ObjectUtil.isNull(driver)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.driver.not.exist", null));
        }
        //车辆信息
        TmsVehicleInfoEntity vehicleInfo = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                .eq(TmsVehicleInfoEntity::getDriverId, driver.getDriverId()));
        if (ObjectUtil.isNull(vehicleInfo)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.vehicle.not.exist",null));
        }
//        //判断该任务单是否上传过异常（只允许上传一次）
//        boolean exists = tmsExceptionManagementMapper.exists(new LambdaQueryWrapper<TmsExceptionManagementEntity>()
//                .eq(TmsExceptionManagementEntity::getExceptionOrderNo, tmsAppExceptionUploadDto.getTaskOrderNo()));
//        if(exists){
//            return R.failed(LocalizedR.getMessage("tms.exception.taskOrder.already.uploaded", null));
//        }
        TmsExceptionManagementEntity tmsExceptionManagementEntity = new TmsExceptionManagementEntity();
        //异常单号
        tmsExceptionManagementEntity.setExceptionOrderNo(generateExceptionId());
        //跟踪单号
        tmsExceptionManagementEntity.setDispatchOrderNo(tmsAppExceptionUploadDto.getTaskOrderNo());
        //司机
        tmsExceptionManagementEntity.setDriverName(driver.getDriverName());
        //车牌号
        tmsExceptionManagementEntity.setLicensePlate(vehicleInfo.getLicensePlate());
        //异常上报时间
        tmsExceptionManagementEntity.setExceptionTime(LocalDateTime.now());
        //异常上报地点
        //tmsExceptionManagementEntity.setExceptionLocation(tmsAppExceptionUploadDto.getExceptionLocation());
        //异常类型
        tmsExceptionManagementEntity.setExceptionType(tmsAppExceptionUploadDto.getExceptionType());
        //异常描述
        tmsExceptionManagementEntity.setExceptionDescription(tmsAppExceptionUploadDto.getExceptionDescription());
        if (StrUtil.isNotBlank(tmsAppExceptionUploadDto.getImageUrls())) {
            //现场图片（最多六张，用逗号分隔）
            tmsExceptionManagementEntity.setImageUrls(tmsAppExceptionUploadDto.getImageUrls());
        }
        //异常状态(0:待处理, 1:已处理)
        tmsExceptionManagementEntity.setExceptionStatus(0);
        int insert = tmsExceptionManagementMapper.insert(tmsExceptionManagementEntity);
        if (insert < 1) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.exception.reporting.failed",null));
        }
        return LocalizedR.ok();
    }

    @Override
    public R getDriverTaskWaitScanLabel(Long driverId) {
        //查询司机此时的所有待提货状态的干线任务
        List<TmsLineHaulOrderEntity> lineHaulOrder = lineHaulOrderMapper.selectList(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getDriverId, driverId)
                        .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode()));
        //查询待扫描的标签(待提货的干线任务就是待提货扫描的)
        List<String> lineHaulNos = lineHaulOrder.stream().map(TmsLineHaulOrderEntity::getLineHaulNo).collect(Collectors.toList());
        List<TmsLabelEntity> tmsLabelEntities =new ArrayList<>();
        if(ObjectUtil.isNotNull(lineHaulNos) && CollUtil.isNotEmpty(lineHaulNos)){
            tmsLabelEntities  = labelMapper.selectList(new LambdaQueryWrapper<TmsLabelEntity>().in(TmsLabelEntity::getLabelCode, lineHaulNos));
        }
        return R.ok(tmsLabelEntities);
    }

    @Transactional
    @Override
    public R scanPickup(String labelNo) {
        //找到该标签，将其设置为干线已扫描提货状态
        TmsLabelEntity tmsLabel = labelMapper.selectOne(new LambdaUpdateWrapper<TmsLabelEntity>().eq(TmsLabelEntity::getLabelCode, labelNo));
        if(ObjectUtil.isNull(tmsLabel)){
            throw new CustomBusinessException(LocalizedR.getMessage("tms.app.driver.label.not.exist", null));
        }
        if(tmsLabel.getIsPickupProof().equals(1)){
            throw new CustomBusinessException(LocalizedR.getMessage("tms.app.driver.label.already.scanned", null));
        }
        //更新标签状态为干线已扫描提货
        tmsLabel.setIsPickupProof(1);
        labelMapper.updateById(tmsLabel);
        return R.ok(Boolean.TRUE);
    }

    @Transactional
    @Override
    public R confirmScanPickup(Long driverId) {
        //查询司机此时的所有待提货状态的干线任务
        List<TmsLineHaulOrderEntity> lineHaulOrder = lineHaulOrderMapper.selectList(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getDriverId, driverId)
                        .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode()));
        //查询待扫描的标签(待提货的干线任务就是待提货扫描的)
        List<String> lineHaulNos = lineHaulOrder.stream().map(TmsLineHaulOrderEntity::getLineHaulNo).collect(Collectors.toList());
        List<TmsLabelEntity> tmsLabelEntities = labelMapper.selectList(new LambdaQueryWrapper<TmsLabelEntity>().in(TmsLabelEntity::getLabelCode, lineHaulNos));
        //检查是否此时司机的笼子任务是否全部扫描
        boolean isAllScanned = tmsLabelEntities.stream().allMatch(item -> item.getIsPickupProof() == 1);
        if(!isAllScanned){
            throw new CustomBusinessException(LocalizedR.getMessage("tms.app.driver.label.not.all.scanned",null));
        }
        for (TmsLabelEntity tmsLabel : tmsLabelEntities) {
            String labelNo = tmsLabel.getLabelCode();
            //干线任务
            TmsLineHaulOrderEntity tmsLineHaulOrderEntity = lineHaulOrderMapper.selectOne(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                    .eq(TmsLineHaulOrderEntity::getLineHaulNo, labelNo), false);
            //将其对应的干线任务改为派送中并记录扫描提货时间
            lineHaulOrderMapper.update(new LambdaUpdateWrapper<TmsLineHaulOrderEntity>()
                    .eq(TmsLineHaulOrderEntity::getLineHaulNo, labelNo)
                    .set(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode())
                    .set(TmsLineHaulOrderEntity::getPickupTime, LocalDateTime.now()));

            //将该批次（笼）的订单设置为已扫描状态并设置为运输中状态
            List<TmsCageAndOrderEntity> tmsCageAndOrderEntities = cageAndOrderMapper.selectList(new LambdaQueryWrapper<TmsCageAndOrderEntity>()
                    .eq(TmsCageAndOrderEntity::getCageCode, tmsLabel.getLabelCode()));
            List<String> orderNos = tmsCageAndOrderEntities.stream().map(TmsCageAndOrderEntity::getOrderNo).collect(Collectors.toList());
            //设置为已扫描状态并设置为运输中状态(主单)
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos)
                    .set(TmsCustomerOrderEntity::getIsScan, 1)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.IN_TRANSIT.getCode()));
            // 获取司机信息（driverId、driverName、driverNum）
            TmsLmdDriverEntity tmsLmdDriverEntity = driverMapper.selectById(driverId);
            // 获取干线司机扫描记录
            List<TmsOrderScanRecordEntity> scanRecordList = new ArrayList<>();
            for (String orderNo : orderNos) {
                TmsOrderScanRecordEntity record = new TmsOrderScanRecordEntity();
                record.setOrderNo(orderNo);
                record.setDriverId(tmsLmdDriverEntity.getDriverId());
                record.setDriverName(tmsLmdDriverEntity.getDriverName());
                record.setDriverNum(tmsLmdDriverEntity.getDriverNum());
                record.setScanTime(LocalDateTime.now());
                record.setScanType(2);
                scanRecordList.add(record);
            }
            // 批量插入扫描记录
            tmsOrderScanRecordService.saveBatch(scanRecordList);
            //找到该容器中所有的订单用来记录轨迹
            List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos)
                    .eq(TmsCustomerOrderEntity::getSubFlag, false));
            //上传提货证明后自动出库减库存（扫码提货后自动出库减库存）
            AtomicInteger quantity = new AtomicInteger(0);
            BigDecimal totalWeight = BigDecimal.ZERO;
            BigDecimal totalVolume = BigDecimal.ZERO;
            // 出库单号（出库批次号）
            String outboundOrderNumber = generateOutboundOrderNumber();
            //用主单统计
            for (TmsCustomerOrderEntity tmsCustomerOrderEntity : customerOrders) {
                quantity.addAndGet(tmsCustomerOrderEntity.getCargoQuantity());
                totalWeight = totalWeight.add(tmsCustomerOrderEntity.getTotalWeight());
                totalVolume = totalVolume.add(tmsCustomerOrderEntity.getTotalVolume());
            }
//        //更新订单状态为运输中（主子单）并回填出库批次号  todo 加中间表后要修改
//        for (TmsCustomerOrderEntity customerOrder : allCustomerOrders) {
//            customerOrder.setOutboundOrderNumber(outboundOrderNumber);
//            customerOrder.setOrderStatus(NewOrderStatus.IN_TRANSIT.getCode());
//        }
//        //更新订单信息(主子单均更新-运输中-回填出库单号（出库批次号）)
//        customerOrderMapper.updateById(allCustomerOrders);

            // 定义最终要绑定的订单集合（主单的所有子单）
            List<TmsCustomerOrderEntity> subOrderList = new ArrayList<>();

            if (CollUtil.isNotEmpty(customerOrders)) {
                for (TmsCustomerOrderEntity mainOrder : customerOrders) {
                    // 根据主单号查子单列表
                    List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(
                            new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrder.getEntrustedOrderNumber())
                                    .eq(TmsCustomerOrderEntity::getSubFlag, true) // 子单
                    );
                    if (CollUtil.isNotEmpty(subOrders)) {
                        subOrderList.addAll(subOrders);
                    }
                }
            }
            //是否自动出库标记
            boolean autoOutboundFlag = true;
            // 校验是否存在已出库的记录--判断是否要自动出库
            if (CollUtil.isNotEmpty(subOrderList)) {
                //将涉及的订单的子单也修改为运输中（上面只是将主单修改为运输中）-----删除请注意
                for (TmsCustomerOrderEntity tmsCustomerOrderEntity : subOrderList) {
                    tmsCustomerOrderEntity.setOrderStatus( NewOrderStatus.IN_TRANSIT.getCode());
                }
                customerOrderMapper.updateById(subOrderList);

                List<String> subOrderNos = subOrderList.stream()
                        .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                        .collect(Collectors.toList());

                Long alreadyOutboundCount = storageandorderMapper.selectCount(
                        new LambdaQueryWrapper<TmsStorageandorderEntity>()
                                .in(TmsStorageandorderEntity::getOrderNo, subOrderNos)
                                .isNotNull(TmsStorageandorderEntity::getOutboundOrderNo)
                                .eq(TmsStorageandorderEntity::getWarehouseId, tmsLineHaulOrderEntity.getOriginWarehouseId())
                );
                if (alreadyOutboundCount > 0) {
                    // 存在已出库记录，跳过
                    autoOutboundFlag=false;
                }
            }
            //自动出库
            if(autoOutboundFlag){
                // 修改库存关系表，填充出库单号
                for (TmsCustomerOrderEntity subOrder : subOrderList) {
                    // 直接更新出库单号
                    TmsStorageandorderEntity updateEntity = new TmsStorageandorderEntity();
                    updateEntity.setOutboundOrderNo(outboundOrderNumber);
                    storageandorderMapper.update(updateEntity, new LambdaUpdateWrapper<TmsStorageandorderEntity>()
                            .eq(TmsStorageandorderEntity::getOrderNo, subOrder.getEntrustedOrderNumber())
                            .isNotNull(TmsStorageandorderEntity::getStorageOrderNo)   // 必须已经入库
                            .isNull(TmsStorageandorderEntity::getOutboundOrderNo)     // 不能出过库
                            .eq(TmsStorageandorderEntity::getWarehouseId, tmsLineHaulOrderEntity.getOriginWarehouseId()));
                }
                //干线的起始地仓库做出库的仓库
                TmsSiteEntity siteEntity = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                        .eq(TmsSiteEntity::getId, tmsLineHaulOrderEntity.getOriginWarehouseId()));
                //新增出库记录
                TmsOutboundRecordEntity tmsOutboundRecordEntity = new TmsOutboundRecordEntity();
                //出库时间
                tmsOutboundRecordEntity.setOutboundTime(LocalDateTime.now());
                //出库批次号
                tmsOutboundRecordEntity.setOutboundOrderNumber(outboundOrderNumber);
                tmsOutboundRecordEntity.setWarehouseId(siteEntity.getId());
                //仓库名称
                tmsOutboundRecordEntity.setWarehouseName(siteEntity.getSiteName());
                //仓库类型
                tmsOutboundRecordEntity.setWarehouseType(siteEntity.getSiteType());
                //出库货品数量
                tmsOutboundRecordEntity.setCargoQuantity(quantity.get());
                tmsOutboundRecordEntity.setTotalWeight(totalWeight);
                tmsOutboundRecordEntity.setTotalVolume(totalVolume);
                outboundRecordMapper.insert(tmsOutboundRecordEntity);

                //减库存
                TmsInventoryManagementEntity tmsInventoryManagementEntity = inventoryManagementMapper.selectOne(new LambdaQueryWrapper<TmsInventoryManagementEntity>()
                        .eq(TmsInventoryManagementEntity::getSiteId, siteEntity.getId())
                        .eq(TmsInventoryManagementEntity::getWarehouseType, siteEntity.getSiteType()));
                if (ObjectUtil.isNotNull(tmsInventoryManagementEntity)) {
                    tmsInventoryManagementEntity.setCargoQuantity(tmsInventoryManagementEntity.getCargoQuantity() - quantity.get());
                    tmsInventoryManagementEntity.setTotalWeight(tmsInventoryManagementEntity.getTotalWeight().subtract(totalWeight));
                    tmsInventoryManagementEntity.setTotalVolume(tmsInventoryManagementEntity.getTotalVolume().subtract(totalVolume));
                    //主单票数
                    tmsInventoryManagementEntity.setOrderNum(tmsInventoryManagementEntity.getOrderNum() - customerOrders.size());
                    inventoryManagementMapper.updateById(tmsInventoryManagementEntity);
                }
            }

        }


        return R.ok(Boolean.TRUE);
    }

    // 干线任务完成执行自动入库操作
    private Boolean lineAuthStorageRecord(String lineHaulOrderNo,List<TmsCustomerOrderEntity> orderList) {
        TmsLineHaulOrderEntity lineHaulOrder = lineHaulOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                        .eq(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulOrderNo), false
        );

        // ***执行入库操作***
        // 根据单号查询订单信息
        //订单和干线中间表查询-所有订单的跟踪单号
//        List<String> entrustedOrderNumber = tmsOrderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
//                        .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, lineHaulOrderNo))
//                .stream().map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toList());
//
//        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new MPJLambdaWrapper<TmsCustomerOrderEntity>()
//                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
//                .eq(TmsCustomerOrderEntity::getSubFlag, false)
//        );


        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return Boolean.FALSE;
        }

        // 汇总总体积、数量、重量
        SummaryResultVo summaryResultVo = calculateSummary(orderList);

        // 生成入库批次号
        String storageOrderNumber = generateStorageOrderNumber();
        TmsStorageRecordEntity vo = new TmsStorageRecordEntity();
        // ***生成入库记录***
        // 入库仓库id
        vo.setWarehouseId(lineHaulOrder.getDestWarehouseId());
        // 根据仓库id查询仓库类型
        TmsSiteEntity tmsWarehouseEntity = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getId, lineHaulOrder.getDestWarehouseId()));
        // 入库仓库类型
        vo.setWarehouseType(tmsWarehouseEntity.getSiteType());
        // 入库时间
        vo.setStorageTime(LocalDateTime.now());
        // 入库批次号
        vo.setStorageOrderNumber(storageOrderNumber);
        // 总数量
        vo.setCargoQuantity(summaryResultVo.getTotalQuantity());
        // 总重量
        vo.setTotalWeight(summaryResultVo.getTotalWeight());
        // 总体积
        vo.setTotalVolume(summaryResultVo.getTotalVolume());

        storageRecordMapper.insert(vo);

        // 定义最终要绑定的订单集合（主单的所有子单）
        List<TmsCustomerOrderEntity> subOrderList = new ArrayList<>();

        if (CollUtil.isNotEmpty(orderList)) {
            for (TmsCustomerOrderEntity mainOrder : orderList) {
                // 根据主单号查子单列表
                List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(
                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrder.getEntrustedOrderNumber())
                                .eq(TmsCustomerOrderEntity::getSubFlag, true) // 子单
                );
                if (CollUtil.isNotEmpty(subOrders)) {
                    subOrderList.addAll(subOrders);
                }
            }
        }

        // 自动入库前判断订单是否已经入库
        if (CollUtil.isNotEmpty(subOrderList)) {
            Long existCount = storageandorderMapper.selectCount(
                    new LambdaQueryWrapper<TmsStorageandorderEntity>()
                            .in(TmsStorageandorderEntity::getOrderNo,subOrderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList()))
                            .eq(TmsStorageandorderEntity::getWarehouseId, lineHaulOrder.getDestWarehouseId())
            );
            if (existCount > 0) {
                // 已存在入库记录，跳过
                return Boolean.FALSE;
            }
        }


        // 订单绑定入库批次号
        List<TmsStorageandorderEntity> relationList = subOrderList.stream()
                .map(order -> {
                    TmsStorageandorderEntity relation = new TmsStorageandorderEntity();
                    relation.setOrderNo(order.getEntrustedOrderNumber());
                    relation.setStorageOrderNo(storageOrderNumber);
                    relation.setWarehouseId(tmsWarehouseEntity.getId());
                    relation.setWarehouseType(tmsWarehouseEntity.getSiteType());
                    return relation;
                })
                .collect(Collectors.toList());

        // 批量插入库存关系表
        storageandorderMapper.insert(relationList);


        // 查询是否已存在库存记录
        TmsInventoryManagementEntity inventory = inventoryManagementMapper.selectOne(
                new LambdaQueryWrapper<TmsInventoryManagementEntity>()
                        .eq(TmsInventoryManagementEntity::getSiteId, vo.getWarehouseId())
        );

        // 如果不存在，插入新记录
        if (inventory == null) {
            TmsInventoryManagementEntity newInventory = new TmsInventoryManagementEntity();
            newInventory.setSiteId(vo.getWarehouseId());
            newInventory.setWarehouseType(vo.getWarehouseType());
            newInventory.setCargoQuantity(summaryResultVo.getTotalQuantity());
            newInventory.setTotalWeight(summaryResultVo.getTotalWeight());
            newInventory.setTotalVolume(summaryResultVo.getTotalVolume());
            newInventory.setOrderNum(orderList.size());
            inventoryManagementMapper.insert(newInventory);
        } else {
            // 如果已存在，则累加更新
            inventoryManagementMapper.update(
                    new LambdaUpdateWrapper<TmsInventoryManagementEntity>()
                            .eq(TmsInventoryManagementEntity::getSiteId, vo.getWarehouseId())
                            .setSql("cargo_quantity = cargo_quantity + " + summaryResultVo.getTotalQuantity())
                            .setSql("total_weight = total_weight + " + summaryResultVo.getTotalWeight())
                            .setSql("total_volume = total_volume + " + summaryResultVo.getTotalVolume())
                            .setSql("order_num = order_num + " + orderList.size())
            );
        }
        return Boolean.TRUE;
    }


    /**
     * app-仓库人工分拣
     * @param dto
     * @return
     */
    @Override
    public R manualSorting(TmsManualSortingRecordDto dto) {
        if (ObjectUtil.isNull(dto) || CollUtil.isEmpty(dto.getEntrustedOrderNos())) {
            return R.failed("Please select the order.");
        }

        // 原始子单号列表
        List<String> orderNoList = dto.getEntrustedOrderNos().stream().distinct().collect(Collectors.toList());

        //单号处理
        dto.setEntrustedOrderNos(processOrderNumbersInPlace(orderNoList, false));

        // 查询这些单号对应的订单信息（主单或子单都查出来）
        List<TmsCustomerOrderEntity> allOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoList));

        if (CollUtil.isEmpty(allOrderList)) {
            return LocalizedR.failed("tms.no.order.info", "");
        }

        // 前置校验：是否存在无法匹配到的订单号
        List<String> foundOrderNos = allOrderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        List<String> notFoundOrderNos = orderNoList.stream().filter(no -> !foundOrderNos.contains(no)).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notFoundOrderNos)) {
            return LocalizedR.failed("tms.invalid.order", String.join(", ", notFoundOrderNos));
        }

        // 校验是否都是子单号（subFlag 为 true）
        List<String> notSubOrderNos = allOrderList.stream().filter(o -> orderNoList.contains(o.getEntrustedOrderNumber())).filter(o -> !Boolean.TRUE.equals(o.getSubFlag()))
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notSubOrderNos)) {
            return LocalizedR.failed("tms.tracking.number.not.compliant", String.join(", ", notSubOrderNos));
        }

        // 查询有效子单，且状态符合要求
        List<TmsCustomerOrderEntity> orderList = allOrderList.stream()
                .filter(o -> orderNoList.contains(o.getEntrustedOrderNumber()))
                .filter(o -> Boolean.TRUE.equals(o.getSubFlag()))
//                .filter(o -> !NewOrderStatus.AWAITING_PICKUP.getCode().equals(o.getOrderStatus()))
                .filter(o -> !NewOrderStatus.COMPLETED.getCode().equals(o.getOrderStatus()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(orderList)) {
            return LocalizedR.failed("tms.no.sorting.order", "");
        }

        List<String> taskIsWan = dto.getEntrustedOrderNos().stream()
                .filter(StrUtil::isNotBlank)
                .map(orderNo -> orderNo.length() > 15 ? orderNo.substring(0, orderNo.length() - 3) : orderNo)
                .distinct()
                .collect(Collectors.toList());
        // 截取子单查询任务单号，判断揽收任务状态
        List<TmsCustomerOrderEntity> allOrderMain = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, taskIsWan)
                .eq(TmsCustomerOrderEntity::getReceiveType, ReceiveType.DOOR_PICKUP.getCode())
        );

        if (CollUtil.isNotEmpty(allOrderMain)) {
            // 获取所有揽收任务单号
            List<String> taskOrderNos = allOrderMain.stream().map(TmsCustomerOrderEntity::getPTaskOrder).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

            // 如果不存在揽收任务单则直接结束揽收任务，并且修改订单状态
            if (CollUtil.isEmpty(taskOrderNos)){
                tmsLmdSortingService.updateOrderDeliverStatus(orderList);
            }else {
                // 查询揽收任务是否存在未完成的
                List<TmsTransportTaskOrderEntity> taskOrderList = transportTaskOrderMapper.selectList(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                        .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.COLLECTION.getCode())
                        .in(TmsTransportTaskOrderEntity::getTaskStatus, Arrays.asList(
                                TransportTaskStatus.IN_TRANSIT.getCode(),
                                TransportTaskStatus.PENDING_PICKUP.getCode()
                        ))
                        .in(TmsTransportTaskOrderEntity::getTaskOrderNo, taskOrderNos)
                        .ne(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));

                // 如果不为空，则提示订单中存在未完成的揽收任务并判断是否结束揽收任务
                if (CollUtil.isNotEmpty(taskOrderList)) {
                    // 从未完成的任务中提取 taskOrderNo
                    Set<String> uncompletedTaskNos = taskOrderList.stream()
                            .map(TmsTransportTaskOrderEntity::getTaskOrderNo)
                            .collect(Collectors.toSet());
                    // 去根据任务单号查询订单
                    List<TmsCustomerOrderEntity> uncompletedOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getPTaskOrder, uncompletedTaskNos)
                            .eq(TmsCustomerOrderEntity::getSubFlag, false)
                    );
                    tmsLmdSortingService.updateOrderDeliverStatus(uncompletedOrderList);
                }
            }
            //return LocalizedR.failed("tms.no.order.info", "");
        }

        // 校验对应单号是否存在
//        List<String> batchOrderNo = tmsLmdSortingService.checkBatchInfo(dto.getBatchNo(), taskIsWan);
//        if (CollUtil.isNotEmpty(batchOrderNo)){
//            return LocalizedR.failed("tms.batch.no.exist", String.join(",", batchOrderNo));
//        }

        List<TmsManualSortingRecordEntity> recordList = new ArrayList<>();
        Map<Long, String> warehouseNameMap = new HashMap<>(); // 用于存储仓库名称
        // 4. 批量插入分拣记录
        for (TmsCustomerOrderEntity order : orderList) {
            // 获取收货地邮编
            String destPostalPrefix = Optional.ofNullable(order.getDestPostalCode())
                    .map(code -> code.substring(0, Math.min(3, code.length())))
                    .orElse("null");

            // 查询订单的目的地邮编匹配的格口
            LambdaQueryWrapper<TmsOverAreaEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(TmsOverAreaEntity::getZip, destPostalPrefix);
            TmsOverAreaEntity overArea = tmsOverAreaService.getOne(wrapper, false);

            // 通过仓库ID获取仓库名称
            TmsSiteEntity site = tmsSiteMapper.selectById(overArea.getWarehouseId());
            String warehouseName = site.getSiteName();
            TmsManualSortingRecordEntity record = new TmsManualSortingRecordEntity();
            if (null != overArea) {
                // 查询该订单已有的分拣记录（按分拣次数降序）
                List<TmsManualSortingRecordEntity> existingRecords = tmsManualSortingRecordMapper.selectList(new LambdaQueryWrapper<TmsManualSortingRecordEntity>()
                        .eq(TmsManualSortingRecordEntity::getEntrustedOrderNo, order.getEntrustedOrderNumber())
                        .orderByDesc(TmsManualSortingRecordEntity::getSortingFrequency)
                        .last("LIMIT 1")
                );

                // 计算新的分拣次数
                int newFrequency = existingRecords.isEmpty() ? 1 : existingRecords.get(0).getSortingFrequency() + 1;

                // 通过仓库ID获取对应格口信息
                TmsSortingGridEntity grid = tmsSortingGridMapper.selectOne(new LambdaQueryWrapper<TmsSortingGridEntity>()
                        .eq(TmsSortingGridEntity::getWarehouseId, overArea.getWarehouseId()), false);
                if (null == grid) {
                    return LocalizedR.failed("tms.no.sorting.grid", warehouseName);
                }

                List<String> zipList = Arrays.asList(overArea.getZip().toUpperCase().split(","));
                if (zipList.contains(destPostalPrefix.toUpperCase())) {
                    // 证明该订单目的仓库在覆盖范围内
                    record.setSortingGridCode(grid.getGridCode());
                    record.setWarehouseId(overArea.getWarehouseId());
                    record.setSortingWarehouseId(dto.getSortingWarehouseId());
                    record.setEntrustedOrderNo(order.getEntrustedOrderNumber());
                    record.setCustomerOrderNumber(order.getCustomerOrderNumber());
                    record.setSortingTime(LocalDateTime.now());
                    record.setCreateTime(LocalDateTime.now());

                    // 设置分拣次数（新增）
                    record.setSortingFrequency(newFrequency);
                    // 路线编号
                    record.setRouteNumber(overArea.getRouteNumber());
                    recordList.add(record);

                    warehouseNameMap.put(Optional.ofNullable(grid.getWarehouseId()).orElse(0L), warehouseName);
                    // 先添加对应的仓库ID
                    order.setWarehouseId(Optional.ofNullable(grid.getWarehouseId()).orElse(0L));
                    log.info("格口邮编验证通过");
                } else {
                    return LocalizedR.failed("tms.app.driver.grid.order.not.mismatching", destPostalPrefix);
                }
            } else {
                throw new CustomBusinessException(LocalizedR.getMessage("tms.area.not.match.postal.code", null));
            }

        }
        tmsManualSortingRecordMapper.insert(recordList);
        // 分拣完成自动入库(确保orderList是子单列表)
        tmsStorageRecordService.manualSortingAutoStorage(orderList, dto.getSortingWarehouseId());
        // 分拣记录插入完成之后更新订单状态
        tmsLmdSortingService.updateOrderStatusBySorting(orderList);
        // 分拣完成之后添加对应的批次
        tmsLmdSortingService.saveBatchInfo(dto.getBatchNo(),taskIsWan);


        Map<String, Object> result = new HashMap<>();
        result.put("records", recordList);
        result.put("warehouseNames", warehouseNameMap);
        return R.ok(result, "tms.sorting.successful");
    }

    /**
     * 上传任务维度的司机车辆经纬度（任务开始到任务结束的经纬度记录）
     */
    @Transactional
    @Override
    public R uploadDriverCarLocation(TmsLargeDriverRealTimeLocationDto tmsLargeDriverRealTimeLocationDto) {
        //查询此时司机正在运输的任务（揽收、干线、派送）
        //1、揽收任务
        List<TmsTransportTaskOrderEntity> collectTaskList = transportTaskOrderMapper.selectList(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.COLLECTION.getCode())
                .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, tmsLargeDriverRealTimeLocationDto.getDriverId()));
        if(ObjectUtil.isNotNull(collectTaskList) && !collectTaskList.isEmpty()){
            //保存揽收任务经纬度
            saveDriverLocation(tmsLargeDriverRealTimeLocationDto,1,collectTaskList,null);
        }
        //2、派送任务
        List<TmsTransportTaskOrderEntity> deliveryTaskList = transportTaskOrderMapper.selectList(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.DELIVERY.getCode())
                .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, tmsLargeDriverRealTimeLocationDto.getDriverId()));
        if(ObjectUtil.isNotNull(deliveryTaskList) && !deliveryTaskList.isEmpty()){
            //保存派送任务经纬度
            saveDriverLocation(tmsLargeDriverRealTimeLocationDto,2,deliveryTaskList,null);
        }
        //3、干线任务
        List<TmsLineHaulOrderEntity> lineHaulTaskList = lineHaulOrderMapper.selectList(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.IN_TRANSIT.getCode())
                .eq(TmsLineHaulOrderEntity::getDriverId, tmsLargeDriverRealTimeLocationDto.getDriverId()));
        if(ObjectUtil.isNotNull(lineHaulTaskList) && !lineHaulTaskList.isEmpty()){
            //保存干线任务经纬度
            saveDriverLocation(tmsLargeDriverRealTimeLocationDto,3,null,lineHaulTaskList);
        }
        //上传经纬度的同时更新一份此时司机的实时位置经纬度给轨迹使用
        TmsDriverLocationEntity location = driverLocationMapper.selectById(tmsLargeDriverRealTimeLocationDto.getDriverId());
        if (location == null) {
            // 插入新位置
            location = new TmsDriverLocationEntity();
            location.setDriverId(tmsLargeDriverRealTimeLocationDto.getDriverId());
            location.setLatitude(tmsLargeDriverRealTimeLocationDto.getLat());
            location.setLongitude(tmsLargeDriverRealTimeLocationDto.getLng());
            driverLocationMapper.insert(location);
        } else {
            // 更新位置
            location.setLatitude(tmsLargeDriverRealTimeLocationDto.getLat());
            location.setLongitude(tmsLargeDriverRealTimeLocationDto.getLng());
            driverLocationMapper.updateById(location);
        }
        return R.ok(Boolean.TRUE);
    }

    /**
     * 根据任务单号和任务类型查询任务单车辆轨迹
     */
    @Override
    public R getVehicleTrailByTaskNo(String taskNo, Integer taskType) {
        TmsLargeDriverRealTimeLocationVo tmsLargeDriverRealTimeLocationVo = new TmsLargeDriverRealTimeLocationVo();
        QueryWrapper<TmsLargeDriverRealTimeLocation> wrapper = new QueryWrapper<>();
        wrapper.eq(TmsLargeDriverRealTimeLocation::getTaskOrderNo, taskNo)
                .eq(TmsLargeDriverRealTimeLocation::getTaskType, taskType)
                .orderByAsc(TmsLargeDriverRealTimeLocation::getUploadTime);
        List<TmsLargeDriverRealTimeLocation> tmsLargeDriverRealTimeLocations = tmsLargeDriverRealTimeLocationService.list(wrapper);
        List<TmsCustomerOrderEntity> tmsCustomerOrders=new ArrayList<>();
        if(taskType.equals(1)){
            //揽收任务-根据任务单号查询出所有订单-取订单的经纬度
            tmsCustomerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getPTaskOrder, taskNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE));
        }
        if(taskType.equals(2)){
            //派送任务-根据任务单号查询出所有订单-取订单的经纬度
            tmsCustomerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getDTaskOrder, taskNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE));
        }
        if(taskType.equals(3)){
            //干线任务-查询干线任务与订单的中间表
            List<TmsOrderLineHaulRelationEntity> tmsOrderLineHaulRelationEntities = tmsOrderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
                    .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, taskNo));
            if(ObjectUtil.isNotNull(tmsOrderLineHaulRelationEntities)){
                Set<String> entrustedOrderNumbers = tmsOrderLineHaulRelationEntities.stream().map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toSet());
                tmsCustomerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
            }
        }
        tmsLargeDriverRealTimeLocationVo.setCustomerOrders(tmsCustomerOrders);
        tmsLargeDriverRealTimeLocationVo.setTaskType(taskType);
        tmsLargeDriverRealTimeLocationVo.setTmsLatitudeAndLongitudes(tmsLargeDriverRealTimeLocations);
        return R.ok(tmsLargeDriverRealTimeLocationVo);
    }

    // 地图揽收任务运输中和待提货列表
    @Override
    public R getCollectionOrderList(Long driverId) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsTransportTaskOrderEntity.class)
                //.selectAll(TmsCustomerOrderEntity.class)
                .selectAs(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsTransportTaskOrderDto.Fields.entrustedOrderNumber)
                .selectAs(TmsCustomerOrderEntity::getOrderStatus, TmsTransportTaskOrderDto.Fields.orderStatus)
                .selectAs(TmsCustomerOrderEntity::getShipperName, TmsTransportTaskOrderDto.Fields.shipperName)
                .selectAs(TmsCustomerOrderEntity::getShipperAddress, TmsTransportTaskOrderDto.Fields.shipperAddress)
                .selectAs(TmsCustomerOrderEntity::getCargoQuantity, TmsTransportTaskOrderDto.Fields.cargoQuantity)
                .selectAs(TmsCustomerOrderEntity::getTotalWeight, TmsTransportTaskOrderDto.Fields.totalWeight)
                .selectAs(TmsCustomerOrderEntity::getTotalVolume, TmsTransportTaskOrderDto.Fields.totalVolume)
                .selectAs(TmsCustomerOrderEntity::getShipperLatLng, TmsTransportTaskOrderDto.Fields.shipperLatLng)
                .selectAs(TmsCustomerOrderEntity::getReceiverLatLng, TmsTransportTaskOrderDto.Fields.receiverLatLng)
                .selectAs(TmsCustomerOrderEntity::getCargoType, TmsTransportTaskOrderDto.Fields.cargoType)
                .selectAs(TmsCustomerOrderEntity::getShipperPhone, TmsTransportTaskOrderDto.Fields.shipperPhone)
                .selectAs(TmsCustomerOrderEntity::getReceiverPhone, TmsTransportTaskOrderDto.Fields.receiverPhone)
                .selectAs(TmsCustomerOrderEntity::getIsOneTicketMany, TmsTransportTaskOrderDto.Fields.isOneTicketMany);
        wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getPTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo);
        wrapper.eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.COLLECTION.getCode());  // 表示揽收订单
        wrapper.in(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode(), TransportTaskStatus.IN_TRANSIT.getCode())
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime);

        List<TmsTransportTaskOrderDto> tmsTransportTaskOrderDtos = transportTaskOrderMapper.selectJoinList(TmsTransportTaskOrderDto.class, wrapper);
        return R.ok(tmsTransportTaskOrderDtos);
    }

    /**
     * 将任务的经纬度保存到mongo中
     */
    public  void saveDriverLocation(TmsLargeDriverRealTimeLocationDto tmsLargeDriverRealTimeLocationDto,Integer type, List<TmsTransportTaskOrderEntity> collectOrDeliveryTaskList,List<TmsLineHaulOrderEntity> lineHaulTaskList ) {
        try {
            if(!type.equals(3)){
                //揽收任务或者派送任务
                for (TmsTransportTaskOrderEntity collectTask : collectOrDeliveryTaskList) {
                    //将此时的揽收任务或者派送任务的司机车辆的经纬度保存到mongo中
                    TmsLargeDriverRealTimeLocation tmsLargeDriverRealTimeLocation = new TmsLargeDriverRealTimeLocation();
                    tmsLargeDriverRealTimeLocation.setTaskType(type);
                    tmsLargeDriverRealTimeLocation.setTaskOrderNo(collectTask.getTaskOrderNo());
                    tmsLargeDriverRealTimeLocation.setLat(tmsLargeDriverRealTimeLocationDto.getLat());
                    tmsLargeDriverRealTimeLocation.setLng(tmsLargeDriverRealTimeLocationDto.getLng());
                    tmsLargeDriverRealTimeLocation.setUploadTime(LocalDateTime.now());
                    tmsLargeDriverRealTimeLocation.setDriverId(tmsLargeDriverRealTimeLocationDto.getDriverId());
                    tmsLargeDriverRealTimeLocationService.save(tmsLargeDriverRealTimeLocation);
                }

            }else {
                //干线任务
                for (TmsLineHaulOrderEntity tmsLineHaulOrder : lineHaulTaskList) {
                    //将此时的干线任务的司机经纬度保存到mongo中
                    TmsLargeDriverRealTimeLocation tmsLargeDriverRealTimeLocation = new TmsLargeDriverRealTimeLocation();
                    tmsLargeDriverRealTimeLocation.setTaskType(type);
                    tmsLargeDriverRealTimeLocation.setTaskOrderNo(tmsLineHaulOrder.getLineHaulNo());
                    tmsLargeDriverRealTimeLocation.setLat(tmsLargeDriverRealTimeLocationDto.getLat());
                    tmsLargeDriverRealTimeLocation.setLng(tmsLargeDriverRealTimeLocationDto.getLng());
                    tmsLargeDriverRealTimeLocation.setUploadTime(LocalDateTime.now());
                    tmsLargeDriverRealTimeLocation.setDriverId(tmsLargeDriverRealTimeLocationDto.getDriverId());
                    tmsLargeDriverRealTimeLocationService.save(tmsLargeDriverRealTimeLocation);
                }
            }
        } catch (Exception e) {
            log.error("保存司机经纬度至mongo失败,原因："+e.toString() + "------时间："+LocalDate.now().toString());
            throw new RuntimeException(e);
        }
    }

}
