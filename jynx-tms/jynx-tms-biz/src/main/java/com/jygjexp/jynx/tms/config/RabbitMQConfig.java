package com.jygjexp.jynx.tms.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ 配置类
 * 用于 Best Office 订单同步佳邮系统的消息队列配置
 * 
 * <AUTHOR>
 * @date 2025/07/31
 */
@Configuration
public class RabbitMQConfig {

    // ==================== Best Office 订单同步相关配置 ====================
    
    /**
     * Best Office 订单同步交换机
     */
    public static final String BEST_OFFICE_ORDER_EXCHANGE = "best.office.order.exchange";
    
    /**
     * Best Office 订单同步队列
     */
    public static final String BEST_OFFICE_ORDER_QUEUE = "best.office.order.queue";
    
    /**
     * Best Office 订单同步路由键
     */
    public static final String BEST_OFFICE_ORDER_ROUTING_KEY = "best.office.order.sync";
    
    /**
     * Best Office 订单同步死信交换机
     */
    public static final String BEST_OFFICE_ORDER_DLX_EXCHANGE = "best.office.order.dlx.exchange";
    
    /**
     * Best Office 订单同步死信队列
     */
    public static final String BEST_OFFICE_ORDER_DLX_QUEUE = "best.office.order.dlx.queue";
    
    /**
     * Best Office 订单同步死信路由键
     */
    public static final String BEST_OFFICE_ORDER_DLX_ROUTING_KEY = "best.office.order.dlx";

    // ==================== 交换机配置 ====================
    
    /**
     * 创建 Best Office 订单同步交换机
     */
    @Bean
    public DirectExchange bestOfficeOrderExchange() {
        return new DirectExchange(BEST_OFFICE_ORDER_EXCHANGE, true, false);
    }
    
    /**
     * 创建 Best Office 订单同步死信交换机
     */
    @Bean
    public DirectExchange bestOfficeOrderDlxExchange() {
        return new DirectExchange(BEST_OFFICE_ORDER_DLX_EXCHANGE, true, false);
    }

    // ==================== 队列配置 ====================
    
    /**
     * 创建 Best Office 订单同步队列
     * 配置死信队列和消息TTL
     */
    @Bean
    public Queue bestOfficeOrderQueue() {
        return QueueBuilder.durable(BEST_OFFICE_ORDER_QUEUE)
                // 配置死信交换机
                .withArgument("x-dead-letter-exchange", BEST_OFFICE_ORDER_DLX_EXCHANGE)
                // 配置死信路由键
                .withArgument("x-dead-letter-routing-key", BEST_OFFICE_ORDER_DLX_ROUTING_KEY)
                // 配置消息TTL（30分钟）
                .withArgument("x-message-ttl", 30 * 60 * 1000)
                // 配置队列最大长度
                .withArgument("x-max-length", 10000)
                .build();
    }
    
    /**
     * 创建 Best Office 订单同步死信队列
     */
    @Bean
    public Queue bestOfficeOrderDlxQueue() {
        return QueueBuilder.durable(BEST_OFFICE_ORDER_DLX_QUEUE).build();
    }

    // ==================== 绑定配置 ====================
    
    /**
     * 绑定 Best Office 订单同步队列到交换机
     */
    @Bean
    public Binding bestOfficeOrderBinding() {
        return BindingBuilder
                .bind(bestOfficeOrderQueue())
                .to(bestOfficeOrderExchange())
                .with(BEST_OFFICE_ORDER_ROUTING_KEY);
    }
    
    /**
     * 绑定 Best Office 订单同步死信队列到死信交换机
     */
    @Bean
    public Binding bestOfficeOrderDlxBinding() {
        return BindingBuilder
                .bind(bestOfficeOrderDlxQueue())
                .to(bestOfficeOrderDlxExchange())
                .with(BEST_OFFICE_ORDER_DLX_ROUTING_KEY);
    }

    // ==================== RabbitTemplate 配置 ====================

    @Bean
    public MessageConverter jacksonMessageConverter() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory,
                                                                               MessageConverter messageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        return factory;
    }

    /**
     * 配置 RabbitTemplate
     * 设置消息转换器和确认机制
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, MessageConverter messageConverter) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        
        // 设置 JSON 消息转换器
        template.setMessageConverter(messageConverter);
        
        // 开启发送确认
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                // 消息发送成功
                if (correlationData != null) {
                    // 可以在这里记录成功日志
                }
            } else {
                // 消息发送失败
                // 可以在这里记录失败日志和原因
            }
        });
        
        // 开启返回确认（当消息无法路由到队列时触发）
        template.setReturnsCallback(returned -> {
            // 消息无法路由到队列时的处理逻辑
            // 可以在这里记录路由失败的日志
        });
        
        return template;
    }
    
    /**
     * 配置监听器容器工厂
     * 设置消息转换器和并发配置
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        
        // 设置 JSON 消息转换器
        factory.setMessageConverter(new Jackson2JsonMessageConverter());
        
        // 设置并发消费者数量
        factory.setConcurrentConsumers(2);
        factory.setMaxConcurrentConsumers(5);
        
        // 设置预取数量
        factory.setPrefetchCount(10);
        
        // 开启手动确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        
        return factory;
    }




}
