package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsStoreEmployeeDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreEmployeeEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreEntity;

import java.util.ArrayList;

public interface TmsStoreEmployeeService extends IService<TmsStoreEmployeeEntity> {

    R saveEmployee(TmsStoreEmployeeDTO tmsStoreEmployee);

    R updateEmployee(TmsStoreEmployeeEntity tmsStoreEmployee);

    R removeBatchEmployee(Long[] list);

    R updateStatus(TmsStoreEmployeeEntity tmsStore);

    IPage<TmsStoreEmployeeEntity> getPage(Page page, TmsStoreEmployeeEntity tmsStoreEmployee);
}