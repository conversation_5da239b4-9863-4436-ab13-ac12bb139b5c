package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.tms.config.RabbitMQConfig;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.task.TmsBestOfficeOrderSyncJyTask;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Best Office 订单同步消息消费者
 * 负责消费 RabbitMQ 队列中的订单同步消息并执行佳邮推送逻辑
 * 
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BestOfficeOrderSyncConsumer {

    private final TmsBestOfficeOrderSyncJyTask bestOfficeOrderSyncJyTask;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final BestOfficeOrderSyncProducer syncProducer;

    /**
     * 消息幂等性控制缓存
     * 使用 ConcurrentHashMap 存储已处理的消息ID，防止重复处理
     */
    private final ConcurrentHashMap<String, Long> processedMessages = new ConcurrentHashMap<>();

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    /**
     * 重试延迟时间（秒）
     * 第1次重试：30秒，第2次重试：60秒，第3次重试：120秒
     */
    private static final int[] RETRY_DELAYS = {30, 60, 120};

    /**
     * 消费 Best Office 订单同步消息
     * 
     * @param syncMessage 同步消息
     * @param message 原始消息
     * @param channel 消息通道
     */
    @RabbitListener(queues = RabbitMQConfig.BEST_OFFICE_ORDER_QUEUE, containerFactory = "rabbitListenerContainerFactory")
    public void consumeOrderSyncMessage(BestOfficeOrderSyncMessage syncMessage, Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            log.info("开始处理 Best Office 订单同步消息 - 消息ID: {}, 订单号: {}, 来源: {}, 重试次数: {}", 
                    syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber(), 
                    syncMessage.getSourceType(), syncMessage.getRetryCount());

            // 幂等性检查
            if (isDuplicateMessage(syncMessage.getMessageId())) {
                log.warn("重复消息，跳过处理 - 消息ID: {}, 订单号: {}", 
                        syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber());
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 验证消息数据
            if (!validateMessage(syncMessage)) {
                log.error("消息数据验证失败 - 消息ID: {}, 订单号: {}", 
                        syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber());
                channel.basicReject(deliveryTag, false); // 拒绝消息，不重新入队
                return;
            }

            // 查询订单信息
            TmsCustomerOrderEntity order = getOrderById(syncMessage.getOrderId());
            if (order == null) {
                log.error("订单不存在 - 消息ID: {}, 订单ID: {}, 订单号: {}", 
                        syncMessage.getMessageId(), syncMessage.getOrderId(), syncMessage.getEntrustedOrderNumber());
                channel.basicReject(deliveryTag, false); // 拒绝消息，不重新入队
                return;
            }

            // 检查订单是否已经同步过
            if (StrUtil.isNotBlank(order.getJyOrderNo())) {
                log.info("订单已同步，跳过处理 - 消息ID: {}, 订单号: {}, 佳邮单号: {}", 
                        syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber(), order.getJyOrderNo());
                markMessageAsProcessed(syncMessage.getMessageId());
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 执行佳邮同步逻辑
            boolean syncResult = executeBestOfficeSync(order);
            
            if (syncResult) {
                // 同步成功
                log.info("Best Office 订单同步成功 - 消息ID: {}, 订单号: {}", 
                        syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber());
                markMessageAsProcessed(syncMessage.getMessageId());
                channel.basicAck(deliveryTag, false);
            } else {
                // 同步失败，判断是否需要重试
                handleSyncFailure(syncMessage, deliveryTag, channel);
            }

        } catch (Exception e) {
            log.error("处理 Best Office 订单同步消息异常 - 消息ID: {}, 订单号: {}, 错误: {}", 
                    syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber(), e.getMessage(), e);
            
            try {
                // 异常情况下的重试处理
                handleSyncFailure(syncMessage, deliveryTag, channel);
            } catch (IOException ioException) {
                log.error("处理消息确认异常 - 消息ID: {}, 错误: {}", 
                        syncMessage.getMessageId(), ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 消费死信队列消息
     * 用于处理最终失败的消息，进行告警或记录
     * 
     * @param syncMessage 同步消息
     * @param message 原始消息
     * @param channel 消息通道
     */
    @RabbitListener(queues = RabbitMQConfig.BEST_OFFICE_ORDER_DLX_QUEUE)
    public void consumeDeadLetterMessage(BestOfficeOrderSyncMessage syncMessage, Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            log.error("处理死信队列消息 - Best Office 订单同步最终失败 - 消息ID: {}, 订单号: {}, 重试次数: {}", 
                    syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber(), syncMessage.getRetryCount());

            // 这里可以实现告警逻辑，比如：
            // 1. 发送邮件通知
            // 2. 记录到失败日志表
            // 3. 发送钉钉/企业微信告警
            // 4. 写入监控系统
            
            // 确认消息处理完成
            channel.basicAck(deliveryTag, false);
            
        } catch (Exception e) {
            log.error("处理死信队列消息异常 - 消息ID: {}, 错误: {}", 
                    syncMessage.getMessageId(), e.getMessage(), e);
            try {
                channel.basicReject(deliveryTag, false);
            } catch (IOException ioException) {
                log.error("拒绝死信消息异常 - 消息ID: {}, 错误: {}", 
                        syncMessage.getMessageId(), ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 检查是否为重复消息
     */
    private boolean isDuplicateMessage(String messageId) {
        return processedMessages.containsKey(messageId);
    }

    /**
     * 标记消息为已处理
     */
    private void markMessageAsProcessed(String messageId) {
        processedMessages.put(messageId, System.currentTimeMillis());
        
        // 定期清理过期的消息ID（这里简单实现，生产环境建议使用 Redis 或定时任务清理）
        if (processedMessages.size() > 10000) {
            long expireTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000; // 24小时前
            processedMessages.entrySet().removeIf(entry -> entry.getValue() < expireTime);
        }
    }

    /**
     * 验证消息数据
     */
    private boolean validateMessage(BestOfficeOrderSyncMessage syncMessage) {
        return syncMessage != null 
                && StrUtil.isNotBlank(syncMessage.getMessageId())
                && syncMessage.getOrderId() != null
                && StrUtil.isNotBlank(syncMessage.getEntrustedOrderNumber())
                && syncMessage.getCustomerId() != null;
    }

    /**
     * 根据订单ID查询订单信息
     */
    private TmsCustomerOrderEntity getOrderById(Long orderId) {
        return customerOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getId, orderId)
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
        );
    }

    /**
     * 执行 Best Office 同步逻辑
     * 复用现有的定时任务逻辑
     */
    private boolean executeBestOfficeSync(TmsCustomerOrderEntity order) {
        try {
            // 调用现有的同步逻辑
            bestOfficeOrderSyncJyTask.syncCreateOrder(Collections.singletonList(order));
            return true;
        } catch (Exception e) {
            log.error("执行 Best Office 同步逻辑失败 - 订单号: {}, 错误: {}", 
                    order.getEntrustedOrderNumber(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理同步失败的情况
     */
    private void handleSyncFailure(BestOfficeOrderSyncMessage syncMessage, long deliveryTag, Channel channel) throws IOException {
        int currentRetryCount = syncMessage.getRetryCount() == null ? 0 : syncMessage.getRetryCount();
        
        if (currentRetryCount < MAX_RETRY_COUNT) {
            // 还可以重试
            int delaySeconds = RETRY_DELAYS[Math.min(currentRetryCount, RETRY_DELAYS.length - 1)];
            
            log.warn("Best Office 订单同步失败，准备重试 - 消息ID: {}, 订单号: {}, 当前重试次数: {}, 延迟: {}秒", 
                    syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber(), currentRetryCount, delaySeconds);
            
            // 发送重试消息
            syncProducer.sendRetryMessage(syncMessage, delaySeconds);
            
            // 确认当前消息
            channel.basicAck(deliveryTag, false);
        } else {
            // 超过最大重试次数，拒绝消息让其进入死信队列
            log.error("Best Office 订单同步失败，超过最大重试次数 - 消息ID: {}, 订单号: {}, 重试次数: {}", 
                    syncMessage.getMessageId(), syncMessage.getEntrustedOrderNumber(), currentRetryCount);
            
            channel.basicReject(deliveryTag, false);
        }
    }
}
