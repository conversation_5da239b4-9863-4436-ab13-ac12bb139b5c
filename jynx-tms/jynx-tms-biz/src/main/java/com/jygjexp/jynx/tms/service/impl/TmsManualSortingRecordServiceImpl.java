package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsManualSortingRecordEntity;
import com.jygjexp.jynx.tms.entity.TmsSiteEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingGridEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsManualSortingRecordMapper;
import com.jygjexp.jynx.tms.service.TmsManualSortingRecordService;
import com.jygjexp.jynx.tms.vo.TmsManualSortingRecordPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsManualSortingRecordExcelVo;
import lombok.RequiredArgsConstructor;
import net.sf.jsqlparser.expression.operators.relational.LikeExpression;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 中大件人工分拣记录
 *
 * <AUTHOR>
 * @date 2025-04-23 17:59:39
 */
@Service
@RequiredArgsConstructor
public class TmsManualSortingRecordServiceImpl extends ServiceImpl<TmsManualSortingRecordMapper, TmsManualSortingRecordEntity> implements TmsManualSortingRecordService {
    private final TmsManualSortingRecordMapper manualSortingRecordMapper;
    private final TmsCustomerOrderMapper customerOrderMapper;

    // 分拣管理分页查询
    @Override
    public Page<TmsManualSortingRecordPageVo> search(Page page, TmsManualSortingRecordPageVo vo) {
        // 字符串时间转换为 Date 类型（假设格式为 "yyyy-MM-dd HH:mm:ss"）
        Date sortingStart = StrUtil.isNotBlank(vo.getSortingStartTime()) ? DateUtil.parse(vo.getSortingStartTime(), "yyyy-MM-dd HH:mm:ss") : null;
        Date sortingEnd = StrUtil.isNotBlank(vo.getSortingEndTime()) ? DateUtil.parse(vo.getSortingEndTime(), "yyyy-MM-dd HH:mm:ss") : null;

        // 查询每个 entrustedOrderNo 最新一条记录（分页） 查询出最新的分拣记录
        List<Long> latestRecordIds = manualSortingRecordMapper.selectLatestRecordIdsByEntrustedOrderNo(vo.getEntrustedOrderNo(), vo.getSortingGridCode(), vo.getWarehouseId(),
                sortingStart, sortingEnd, page);

        if (CollUtil.isEmpty(latestRecordIds)) {
            return new Page<>(page.getCurrent(), page.getSize(), 0);
        }

        MPJLambdaWrapper<TmsManualSortingRecordEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsManualSortingRecordEntity.class)
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsCustomerOrderEntity::getIsBatch, TmsCustomerOrderEntity::getRouteNumber, TmsCustomerOrderEntity::getCustomerOrderNumber)
                .selectAs(TmsSiteEntity::getSiteName, TmsManualSortingRecordPageVo.Fields.warehouseName)
                .selectAs(TmsSortingGridEntity::getId, TmsManualSortingRecordPageVo.Fields.gridId)
                .selectAs(TmsSortingGridEntity::getGridName, TmsManualSortingRecordPageVo.Fields.gridName)
                .selectAs(TmsSortingGridEntity::getGridCode, TmsManualSortingRecordPageVo.Fields.gridCode)
                .selectAs(TmsCustomerOrderEntity::getIsBatch, TmsManualSortingRecordPageVo.Fields.isBatch)
                .selectAs(TmsCustomerOrderEntity::getRouteNumber, TmsManualSortingRecordPageVo.Fields.routeNumber)
                .eq(ObjectUtil.isNotNull(vo.getIsBatch()), TmsCustomerOrderEntity::getIsBatch, vo.getIsBatch())
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNo()), TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNo())  // 子单号
                .like(StrUtil.isNotBlank(vo.getMainOrderNo()), TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getMainOrderNo())    // 主单号
                .like(StrUtil.isNotBlank(vo.getSortingGridCode()), TmsManualSortingRecordEntity::getSortingGridCode, vo.getSortingGridCode())
                .eq(ObjectUtil.isNotNull(vo.getWarehouseId()), TmsManualSortingRecordEntity::getWarehouseId, vo.getWarehouseId())
                .between(ObjectUtil.isNotNull(vo.getSortingStartTime()) && ObjectUtil.isNotNull(vo.getSortingEndTime()),
                        TmsManualSortingRecordEntity::getSortingTime, vo.getSortingStartTime(), vo.getSortingEndTime())
                // 按最新记录ID筛选
                .in(TmsManualSortingRecordEntity::getId, latestRecordIds)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsManualSortingRecordEntity::getEntrustedOrderNo)
                .leftJoin(TmsSortingGridEntity.class, TmsSortingGridEntity::getGridCode, TmsManualSortingRecordEntity::getSortingGridCode)
                .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsSortingGridEntity::getWarehouseId)
                .orderByDesc(TmsManualSortingRecordEntity::getSortingTime);
        // 分页查询
        Page<TmsManualSortingRecordPageVo> resultPage = manualSortingRecordMapper.selectJoinPage(page, TmsManualSortingRecordPageVo.class, wrapper);
        List<TmsManualSortingRecordPageVo> records = resultPage.getRecords();

        if (CollUtil.isNotEmpty(records)) {
            // 构建待查订单号（子单 + 推测主单）
            Set<String> orderNos = new HashSet<>();
            for (TmsManualSortingRecordPageVo record : records) {
                String subNo = record.getEntrustedOrderNo();
                orderNos.add(subNo);
                if (StrUtil.isNotBlank(subNo) && subNo.length() > 3) {
                    orderNos.add(subNo.substring(0, subNo.length() - 3));
                }
            }
            // 批量查询订单信息
            List<TmsCustomerOrderEntity> allOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>().in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos));

            // 构建订单号 -> subFlag 映射
            Map<String, Boolean> subFlagMap = allOrders.stream().collect(Collectors.toMap(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsCustomerOrderEntity::getSubFlag, (a, b) -> a));

            // 构建子单 -> 主单映射
            Map<String, String> subToMainMap = new HashMap<>();
            for (String subNo : orderNos) {
                if (Boolean.TRUE.equals(subFlagMap.get(subNo)) && subNo.length() > 3) {
                    String mainNo = subNo.substring(0, subNo.length() - 3);
                    if (Boolean.FALSE.equals(subFlagMap.get(mainNo))) {
                        subToMainMap.put(subNo, mainNo);
                    }
                }
            }
            // 设置主单号到分页记录
            for (TmsManualSortingRecordPageVo record : records) {
                String subNo = record.getEntrustedOrderNo();
                String mainNo = subToMainMap.get(subNo);
                record.setMainOrderNo(StrUtil.isNotBlank(mainNo) ? mainNo : subNo);
                // 设置面单
                record.setWaybillNumber(subNo);
            }
        }
        // 设置分页总数
        resultPage.setTotal(page.getTotal());
        return resultPage;
    }

    // 导出分拣管理列表
    @Override
    public List<TmsManualSortingRecordExcelVo> getManualSortingRecordExcel(TmsManualSortingRecordPageVo vo, Long[] ids) {
        // 字符串时间转换为 Date 类型（假设格式为 "yyyy-MM-dd HH:mm:ss"）
        Date sortingStart = StrUtil.isNotBlank(vo.getSortingStartTime()) ? DateUtil.parse(vo.getSortingStartTime(), "yyyy-MM-dd HH:mm:ss") : null;
        Date sortingEnd = StrUtil.isNotBlank(vo.getSortingEndTime()) ? DateUtil.parse(vo.getSortingEndTime(), "yyyy-MM-dd HH:mm:ss") : null;

        // 查询每个 entrustedOrderNo 最新一条记录（不分页）
        List<Long> latestRecordIds = manualSortingRecordMapper.selectLatestRecordIdsByEntrustedOrderNo(
                vo.getEntrustedOrderNo(), vo.getSortingGridCode(), vo.getWarehouseId(),
                sortingStart, sortingEnd, null
        );

        if (CollUtil.isEmpty(latestRecordIds)) {
            return Collections.emptyList();
        }

        MPJLambdaWrapper<TmsManualSortingRecordEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsManualSortingRecordEntity.class)
                .select(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsCustomerOrderEntity::getIsBatch, TmsCustomerOrderEntity::getRouteNumber)
                .selectAs(TmsSiteEntity::getSiteName, TmsManualSortingRecordPageVo.Fields.warehouseName)
                .selectAs(TmsSortingGridEntity::getId, TmsManualSortingRecordPageVo.Fields.gridId)
                .selectAs(TmsSortingGridEntity::getGridName, TmsManualSortingRecordPageVo.Fields.gridName)
                .selectAs(TmsSortingGridEntity::getGridCode, TmsManualSortingRecordPageVo.Fields.gridCode)
                .selectAs(TmsCustomerOrderEntity::getIsBatch, TmsManualSortingRecordPageVo.Fields.isBatch)
                .selectAs(TmsCustomerOrderEntity::getRouteNumber, TmsManualSortingRecordPageVo.Fields.routeNumber)
                .eq(ObjectUtil.isNotNull(vo.getIsBatch()), TmsCustomerOrderEntity::getIsBatch, vo.getIsBatch())
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNo()), TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNo())
                .like(StrUtil.isNotBlank(vo.getMainOrderNo()), TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getMainOrderNo())
                .like(StrUtil.isNotBlank(vo.getSortingGridCode()), TmsManualSortingRecordEntity::getSortingGridCode, vo.getSortingGridCode())
                .eq(ObjectUtil.isNotNull(vo.getWarehouseId()), TmsManualSortingRecordEntity::getWarehouseId, vo.getWarehouseId())
                .between(ObjectUtil.isNotNull(vo.getSortingStartTime()) && ObjectUtil.isNotNull(vo.getSortingEndTime()),
                        TmsManualSortingRecordEntity::getSortingTime, vo.getSortingStartTime(), vo.getSortingEndTime())
                // 按最新记录ID筛选
                .in(TmsManualSortingRecordEntity::getId, latestRecordIds)
                // 导出时额外加上 in (ids)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsManualSortingRecordEntity::getId, ids)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsManualSortingRecordEntity::getEntrustedOrderNo)
                .leftJoin(TmsSortingGridEntity.class, TmsSortingGridEntity::getGridCode, TmsManualSortingRecordEntity::getSortingGridCode)
                .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsSortingGridEntity::getWarehouseId)
                .orderByDesc(TmsManualSortingRecordEntity::getSortingTime);

        // 直接查出列表返回，不分页
        List<TmsManualSortingRecordExcelVo> records = manualSortingRecordMapper.selectJoinList(
                TmsManualSortingRecordExcelVo.class, wrapper
        );

        // 处理主单号和面单逻辑（与分页逻辑相同）
        if (CollUtil.isNotEmpty(records)) {
            Set<String> orderNos = new HashSet<>();
            for (TmsManualSortingRecordExcelVo record : records) {
                String subNo = record.getEntrustedOrderNo();
                orderNos.add(subNo);
                if (StrUtil.isNotBlank(subNo) && subNo.length() > 3) {
                    orderNos.add(subNo.substring(0, subNo.length() - 3));
                }
            }

            List<TmsCustomerOrderEntity> allOrders = customerOrderMapper.selectList(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos)
            );

            Map<String, Boolean> subFlagMap = allOrders.stream()
                    .collect(Collectors.toMap(
                            TmsCustomerOrderEntity::getEntrustedOrderNumber,
                            TmsCustomerOrderEntity::getSubFlag,
                            (a, b) -> a
                    ));

            Map<String, String> subToMainMap = new HashMap<>();
            for (String subNo : orderNos) {
                if (Boolean.TRUE.equals(subFlagMap.get(subNo)) && subNo.length() > 3) {
                    String mainNo = subNo.substring(0, subNo.length() - 3);
                    if (Boolean.FALSE.equals(subFlagMap.get(mainNo))) {
                        subToMainMap.put(subNo, mainNo);
                    }
                }
            }

            for (TmsManualSortingRecordExcelVo record : records) {
                String subNo = record.getEntrustedOrderNo();
                String mainNo = subToMainMap.get(subNo);
                record.setMainOrderNo(StrUtil.isNotBlank(mainNo) ? mainNo : subNo);
                record.setWaybillNumber(subNo);
            }
        }

        return records;
    }


}