package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderGoodsEntity;

import java.util.List;
import java.util.Map;

public interface TmsStoreOrderGoodsService extends IService<TmsStoreOrderGoodsEntity> {

    /**
     * 保存订单
     * @param tmsStoreOrderGoods
     */
    void saveStoreOrderGoods(List<TmsStoreOrderGoodsEntity> tmsStoreOrderGoods);

    /**
     * 根据主单号获取订单
     * @param mainEntrustedOrder
     * @return
     */
    List<TmsStoreOrderGoodsEntity> getByMainEntrustedOrder(String mainEntrustedOrder);


    /**
     * 删除订单-主单号
     * @param mainEntrustedOrder
     */
    void deleteByMainEntrustedOrder(String mainEntrustedOrder);

    /**
     * 根据主单号分组
     * @param mainEntrustedOrder
     * @return
     */
    Map<String,TmsStoreOrderGoodsEntity> getMapByMainEntrustedOrder(String mainEntrustedOrder);


}
