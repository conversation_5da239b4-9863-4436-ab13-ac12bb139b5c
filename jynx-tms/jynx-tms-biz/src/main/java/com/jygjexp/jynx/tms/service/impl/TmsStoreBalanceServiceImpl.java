package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceOfflineRecharge;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceRecordEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsStoreBalanceMapper;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceOfflineRechargeService;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceRecordService;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import com.jygjexp.jynx.tms.service.TmsStoreCustomerService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店余额表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:49:59
 */
@Service
@RequiredArgsConstructor
public class TmsStoreBalanceServiceImpl extends ServiceImpl<TmsStoreBalanceMapper, TmsStoreBalanceEntity> implements TmsStoreBalanceService {
    private static final Logger logger = LoggerFactory.getLogger(TmsStoreBalanceServiceImpl.class);

    private final TmsStoreBalanceRecordService tmsStoreBalanceRecordService;
    private final TmsStoreBalanceOfflineRechargeService tmsStoreBalanceOfflineRechargeService;
    private final TmsStoreCustomerService tmsStoreCustomerService;

    /**
     * 线下充值
     *
     * @param offlineRecharge
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean offlineRecharge(Long id, TmsStoreBalanceOfflineRecharge offlineRecharge) {
        TmsStoreBalanceEntity entity = getOptById(id)
                .orElseThrow(() -> new CustomBusinessException("This record not exists."));

        Long storeCustomerId = entity.getStoreCustomerId();
        TmsStoreCustomerEntity storeCustomer = tmsStoreCustomerService.getById(storeCustomerId);
        Long storeId = storeCustomer.getStoreId();

        //流水记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreBalanceId(entity.getId());
        balanceRecord.setStoreCustomerId(storeCustomerId);
        balanceRecord.setBeforeAmount(entity.getAmount());
        balanceRecord.setAfterAmount(entity.getAmount().add(offlineRecharge.getAmount()));
        balanceRecord.setChangeAmount(offlineRecharge.getAmount());
        balanceRecord.setType(5);
        balanceRecord.setSubType(0);
        balanceRecord.setStatus(0);
        balanceRecord.setRemark(offlineRecharge.getNotes());
        tmsStoreBalanceRecordService.save(balanceRecord);

        //流水号
        balanceRecord.setBusinessNumber(StrUtil.format("CZ{}{}", System.currentTimeMillis(), balanceRecord.getId()));
        tmsStoreBalanceRecordService.updateById(balanceRecord);

        //手工调帐记录
        offlineRecharge.setStoreId(storeId);
        offlineRecharge.setStoreCustomerId(storeCustomerId);
        offlineRecharge.setStoreBalanceRecordId(balanceRecord.getId());
        tmsStoreBalanceOfflineRechargeService.save(offlineRecharge);
        return true;
    }

    /**
     * 查询当前用户的余额
     *
     * @return
     */
    @Override
    public TmsStoreBalanceEntity getCurrent() {
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity storeCustomer = tmsStoreCustomerService.getStoreCustomerByUserId(userId);
        LambdaQueryWrapper<TmsStoreBalanceEntity> wrapper = Wrappers.lambdaQuery(TmsStoreBalanceEntity.class)
                .eq(TmsStoreBalanceEntity::getStoreCustomerId, storeCustomer.getId());
        return getOne(wrapper);
    }

    @Override
    public TmsStoreBalanceEntity getByStoreCustomerId(Long storeCustomerId) {
        if(null == storeCustomerId){
            return null;
        }
        LambdaQueryWrapper<TmsStoreBalanceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreBalanceEntity::getStoreCustomerId, storeCustomerId);
        queryWrapper.last("for update");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeOrderDeduction(TmsStoreBalanceEntity storeBalance,BigDecimal deductAmount,String entrustedOrderNumber) {
        if(null == storeBalance || null == deductAmount || StrUtil.isBlank(entrustedOrderNumber)){
            throw new CustomBusinessException("余额变更失败");
        }
        int count = baseMapper.exchangeDeductAmount(storeBalance.getStoreCustomerId(), deductAmount, storeBalance.getAmountHash());
        if(count < 1){
            logger.error("[客户余额变更][金额哈希值不匹配]失败,storeBalance={}",storeBalance);
            throw new CustomBusinessException("余额变更失败");
        }
        // 生成一条扣款记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount().subtract(deductAmount));
        balanceRecord.setChangeAmount(deductAmount);
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.ORDER_DEDUCT.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.CONSUME.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.ORDER_DEDUCT.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);
    }

    /**
     * 初始化
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean init() {
        remove(Wrappers.emptyWrapper());
        List<TmsStoreCustomerEntity> customers = tmsStoreCustomerService.list();
        List<TmsStoreBalanceEntity> collect = customers.stream()
                .filter(customer -> ObjUtil.equal(customer.getStatus(), 1))
                .map(TmsStoreCustomerEntity::getId)
                .map(id -> {
                    TmsStoreBalanceEntity entity = new TmsStoreBalanceEntity();
                    entity.setStoreCustomerId(id);
                    entity.setAmount(BigDecimal.ZERO);
                    entity.setAmountHash(DigestUtil.sha256Hex("0.00"));
                    return entity;
                })
                .collect(Collectors.toList());

        saveBatch(collect);
        return true;
    }

}
