package com.jygjexp.jynx.tms.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Best Office 订单同步消息 DTO
 * 用于 RabbitMQ 消息传递的数据结构
 * 
 * <AUTHOR>
 * @date 2025/07/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Best Office 订单同步消息")
public class BestOfficeOrderSyncMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID（用于幂等性控制）
     */
    @Schema(description = "消息ID")
    private String messageId;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long orderId;

    /**
     * 委托订单号
     */
    @Schema(description = "委托订单号")
    private String entrustedOrderNumber;

    /**
     * 客户订单号
     */
    @Schema(description = "客户订单号")
    private String customerOrderNumber;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 订单创建时间
     */
    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderCreateTime;

    /**
     * 消息创建时间
     */
    @Schema(description = "消息创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime messageCreateTime;

    /**
     * 消息来源类型
     * CREATE - 订单创建
     * IMPORT - 订单导入
     */
    @Schema(description = "消息来源类型")
    private String sourceType;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer retryCount;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remark;

    /**
     * 消息来源类型枚举
     */
    public static class SourceType {
        public static final String CREATE = "CREATE";
        public static final String IMPORT = "IMPORT";
    }

    /**
     * 创建订单创建类型的消息
     */
    public static BestOfficeOrderSyncMessage createOrderMessage(Long orderId, String entrustedOrderNumber, 
                                                               String customerOrderNumber, Long customerId, 
                                                               String customerName, LocalDateTime orderCreateTime) {
        return BestOfficeOrderSyncMessage.builder()
                .messageId(generateMessageId(orderId, SourceType.CREATE))
                .orderId(orderId)
                .entrustedOrderNumber(entrustedOrderNumber)
                .customerOrderNumber(customerOrderNumber)
                .customerId(customerId)
                .customerName(customerName)
                .orderCreateTime(orderCreateTime)
                .messageCreateTime(LocalDateTime.now())
                .sourceType(SourceType.CREATE)
                .retryCount(0)
                .remark("订单创建触发的佳邮同步")
                .build();
    }

    /**
     * 创建订单导入类型的消息
     */
    public static BestOfficeOrderSyncMessage createImportMessage(Long orderId, String entrustedOrderNumber, 
                                                                String customerOrderNumber, Long customerId, 
                                                                String customerName, LocalDateTime orderCreateTime) {
        return BestOfficeOrderSyncMessage.builder()
                .messageId(generateMessageId(orderId, SourceType.IMPORT))
                .orderId(orderId)
                .entrustedOrderNumber(entrustedOrderNumber)
                .customerOrderNumber(customerOrderNumber)
                .customerId(customerId)
                .customerName(customerName)
                .orderCreateTime(orderCreateTime)
                .messageCreateTime(LocalDateTime.now())
                .sourceType(SourceType.IMPORT)
                .retryCount(0)
                .remark("订单导入触发的佳邮同步")
                .build();
    }

    /**
     * 生成消息ID
     * 格式：BEST_OFFICE_{sourceType}_{orderId}_{timestamp}
     */
    private static String generateMessageId(Long orderId, String sourceType) {
        return String.format("BEST_OFFICE_%s_%d_%d", sourceType, orderId, System.currentTimeMillis());
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }

    /**
     * 判断是否超过最大重试次数
     */
    public boolean isExceedMaxRetry(int maxRetry) {
        return this.retryCount != null && this.retryCount >= maxRetry;
    }
}
