package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.RWShelfScanDto;
import com.jygjexp.jynx.tms.dto.TmsBookingPickupDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.enums.OrderTrackLink;
import com.jygjexp.jynx.tms.enums.PickupStatusEnum;
import com.jygjexp.jynx.tms.enums.TransportTaskStatus;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsCustomerMapper;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsSiteMapper;
import com.jygjexp.jynx.tms.mapper.TmsZdjPickupMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.ALiYunSms;
import com.jygjexp.jynx.tms.vo.TmsZdjPickupOWVo;
import com.jygjexp.jynx.tms.vo.TmsZdjPickupPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 中大件自提信息记录表
 *
 * <AUTHOR>
 * @date 2025-06-06 17:03:21
 */
@RequiredArgsConstructor
@Service
public class TmsZdjPickupServiceImpl extends ServiceImpl<TmsZdjPickupMapper, TmsZdjPickupEntity> implements TmsZdjPickupService {

    private final TmsZdjPickupMapper tmsZdjPickupMapper;
    private final TmsCustomerOrderMapper  tmsCustomerOrderMapper;
    private final TmsWarehouseLocationService tmsWarehouseLocationService;
    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final TmsSiteMapper tmsSiteMapper;
    private final TmsTransportTaskOrderService tmsTransportTaskOrderService;
    private final TmsVehicleRouteVisitService tmsVehicleRouteVisitService;
    private final TmsRoutePlanService tmsRoutePlanService;
    private final TmsVehicleRouteService tmsVehicleRouteService;
    private final TmsOrderTrackService orderTrackService;
    private final TmsLmdDriverService tmsLmdDriverService;
    // 分页查询
    @Override
    public Page<TmsZdjPickupEntity> search(Page page, TmsZdjPickupPageVo vo) {
        MPJLambdaWrapper<TmsZdjPickupEntity> wrapper = new MPJLambdaWrapper<TmsZdjPickupEntity>()
                .selectAll(TmsZdjPickupEntity.class)
                .select(TmsSiteEntity::getSiteName)
                // 订单号
                .like(StrUtil.isNotBlank(vo.getOrderNo()),TmsZdjPickupEntity::getOrderNo,vo.getOrderNo())
                // 取件码
                .like(StrUtil.isNotBlank(vo.getPickupCode()),TmsZdjPickupEntity::getPickupCode,vo.getPickupCode())
                // 仓库id
                .like(ObjectUtil.isNotNull(vo.getWarehouseId()),TmsZdjPickupEntity::getWarehouseId,vo.getWarehouseId())
                .like(StrUtil.isNotBlank(vo.getWarehouseLocation()),TmsZdjPickupEntity::getWarehouseLocation,vo.getWarehouseLocation())
                .like(StrUtil.isNotBlank(vo.getRecipientName()),TmsZdjPickupEntity::getRecipientName,vo.getRecipientName())
                .like(StrUtil.isNotBlank(vo.getRecipientPhone()),TmsZdjPickupEntity::getRecipientPhone,vo.getRecipientPhone())
                .eq(ObjectUtil.isNotNull(vo.getIsExpiredDestroy()),TmsZdjPickupEntity::getIsExpiredDestroy,vo.getIsExpiredDestroy())
                //状态
                .eq(ObjectUtil.isNotNull(vo.getPickupStatus()),TmsZdjPickupEntity::getPickupStatus,vo.getPickupStatus())
                .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId,TmsZdjPickupEntity::getWarehouseId)
                .orderByDesc(TmsZdjPickupEntity::getCreateTime);
                return tmsZdjPickupMapper.selectJoinPage(page,TmsZdjPickupPageVo.class, wrapper);
    }

    // 新增时查询订单信息
    @Override
    public R getOrderInfo(String orderNo) {

        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo);
        TmsCustomerOrderEntity order = tmsCustomerOrderMapper.selectOne(wrapper, false);

        //统计订单件数(主子单)
        List<TmsCustomerOrderEntity> customerOrders = tmsCustomerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));

        if (order != null) {
            Map<String, Object> result = new HashMap<>();
            result.put("contact", order.getReceiverName());
            result.put("phone", order.getReceiverPhone());
            result.put("totalCount", customerOrders.size()-1);
            return R.ok(result);
        } else {
            return R.failed("单号不存在");
        }
    }

    /**
     * 自提出仓
     */
    @Transactional
    @Override
    public R pickupOutWarehouse(Long id, String outWarehouseProof) {
        TmsZdjPickupEntity tmsZdjPickupEntity = this.getById(id);
        if(StrUtil.isBlank(outWarehouseProof)){
            throw new CustomBusinessException("请上传出仓证明!");
        }
        if(ObjectUtil.isNotNull(tmsZdjPickupEntity)){
            if(!tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.APPOINTMENT.getCode())){
                throw new CustomBusinessException("订单为预约待取货状态方可自提出仓！");
            }
            //修改自提状态为已取件
            tmsZdjPickupEntity.setPickupStatus(PickupStatusEnum.PICKED_UP.getCode());
            //保存出仓证明（取件证明）
            tmsZdjPickupEntity.setPickupImages(outWarehouseProof);
            //出仓自提时间
            tmsZdjPickupEntity.setPickupTime(LocalDateTime.now());
            //更新
            this.updateById(tmsZdjPickupEntity);

            //自提出仓后，修改该订单为已完成状态
            tmsCustomerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsZdjPickupEntity.getOrderNo())
                    //修改订单的状态为已完成
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())
            );

            // 根据tmsZdjPickupEntity.getOrderNo()查询订单信息
            TmsCustomerOrderEntity customerOrder = tmsCustomerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsZdjPickupEntity.getOrderNo()),false
            );

            if (customerOrder != null){
                // 添加轨迹记录：自提出仓，订单已完成
                orderTrackService.saveTrack(
                        tmsZdjPickupEntity.getOrderNo(),
                        customerOrder.getCustomerOrderNumber(),
                        NewOrderStatus.COMPLETED.getValue(),
                        OrderTrackLink.PICKED_UD.getCode()
                );
            }

            return R.ok();
        }else{
            throw new CustomBusinessException("记录不存在,提交失败！");
        }
    }

    /**
     * 新增中大件自提信息记录表R
     */
    @Transactional
    @Override
    public R add(TmsZdjPickupEntity tmsZdjPickup) {
        //校验是否能够新增自提-上架
        String orderNo = tmsZdjPickup.getOrderNo();
        if(tmsZdjPickup.getOrderNo().length()>15){
            //此时是子单（16位），截取后三位得到主单（13位）
            orderNo = tmsZdjPickup.getOrderNo().substring(tmsZdjPickup.getOrderNo().length()-3);
        }
        List<TmsCustomerOrderEntity> tmsCustomerOrderEntityList = tmsCustomerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));
        //主单
        List<TmsCustomerOrderEntity> tmsCustomerMasterOrderList = tmsCustomerOrderEntityList.stream().filter(item -> item.getSubFlag().equals(Boolean.FALSE)).collect(Collectors.toList());
        TmsCustomerOrderEntity tmsCustomerOrderEntity = tmsCustomerMasterOrderList.get(0);
        if(ObjectUtil.isNull(tmsCustomerOrderEntity)){
            throw new CustomBusinessException("订单【"+orderNo+"】不存在！");
        }
        //判断该订单是否已经有过大中件上架自提信息
        TmsZdjPickupEntity oldZdjPickup = tmsZdjPickupMapper.selectOne(new LambdaQueryWrapper<TmsZdjPickupEntity>()
                .eq(TmsZdjPickupEntity::getOrderNo,  orderNo));
        if(ObjectUtil.isNotNull(oldZdjPickup)){
            throw new CustomBusinessException("该订单已经存在大中件上架自提信息！");
        }
        if(!tmsCustomerOrderEntity.getOrderStatus().equals(NewOrderStatus.AWAITING_RETURN.getCode())){
            //不是待返仓状态的订单不可上架自提
            throw new CustomBusinessException("订单【"+orderNo+"】不是待返仓状态的订单！");
        }
        //仓位id
        Long locationId = tmsZdjPickup.getLocationId();
        //更具仓位id查询仓位信息
        TmsWarehouseLocationEntity tmsWarehouseLocationEntity = tmsWarehouseLocationService.getById(locationId);
        //上架时间
        tmsZdjPickup.setShelvingTime(LocalDateTime.now());
        //仓位名称回填
        tmsZdjPickup.setWarehouseLocation(tmsWarehouseLocationEntity.getLocationCode());
        if(tmsZdjPickup.getScanCount()>tmsZdjPickup.getTotalCount()){
            throw new CustomBusinessException("上架数不能大于总件数！");
        }
        //修改订单的状态为已返仓
        tmsCustomerOrderEntity.setOrderStatus(NewOrderStatus.RETURNED.getCode());
        tmsCustomerOrderMapper.updateById(tmsCustomerOrderEntity);

        // 添加轨迹记录
        orderTrackService.saveTrack(
                tmsCustomerOrderEntity.getEntrustedOrderNumber(),
                tmsCustomerOrderEntity.getCustomerOrderNumber(),
                NewOrderStatus.RETURNED.getValue(),
                OrderTrackLink.SELF_PICKUP.getCode()
        );

        deleteDeliverTaskAndRoutePlanData(Collections.singletonList(tmsCustomerOrderEntity));


        return R.ok(this.save(tmsZdjPickup));
    }

    @Transactional
    @Override
    public R destroy(Long id) {
        TmsZdjPickupEntity tmsZdjPickupEntity = this.getById(id);
        if(tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.PICKED_UP.getCode())){
            throw new CustomBusinessException("订单已取件，不可销毁！");
        }
        if(tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.REDELIVERY.getCode())){
            throw new CustomBusinessException("订单已重新派送，不可销毁！");
        }
        //修改为销毁状态
        this.update(new LambdaUpdateWrapper<TmsZdjPickupEntity>()
                .eq(TmsZdjPickupEntity::getId, id)
                .set(TmsZdjPickupEntity::getPickupStatus, PickupStatusEnum.DESTROYED.getCode())
        );
        //销毁后将相应的订单状态改为已完成
        tmsCustomerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsZdjPickupEntity.getOrderNo())
                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode()));
        return R.ok();
    }


    /**
     * 预约取货时间
     */
    @Transactional
    @Override
    public R bookingPickupTime(TmsBookingPickupDto tmsBookingPickupDto) {
        List<TmsZdjPickupEntity> zdjPickupList = this.listByIds(tmsBookingPickupDto.getIds());
        if(CollectionUtil.isNotEmpty(zdjPickupList)){
            for (TmsZdjPickupEntity tmsZdjPickupEntity : zdjPickupList) {
                if(tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.PICKED_UP.getCode())){
                    throw new CustomBusinessException("订单"+tmsZdjPickupEntity.getOrderNo()+"已取件，请勿重复预约取件时间!");
                }
                if(tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.DESTROYED.getCode())){
                    throw new CustomBusinessException("订单"+tmsZdjPickupEntity.getOrderNo()+"销毁！");
                }
                if(tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.REDELIVERY.getCode())){
                    throw new CustomBusinessException("订单"+tmsZdjPickupEntity.getOrderNo()+"已选择重新派送！");
                }
                //预约时间
                tmsZdjPickupEntity.setAppointmentTime(tmsBookingPickupDto.getAppointmentTime());
                tmsZdjPickupEntity.setAppointmentTimeEnd(tmsBookingPickupDto.getAppointmentTimeEnd());
                //修改状态为预约待取货
                tmsZdjPickupEntity.setPickupStatus(PickupStatusEnum.APPOINTMENT.getCode());

                //取件码
                tmsZdjPickupEntity.setPickupCode(generatePickupCode(tmsZdjPickupEntity.getWarehouseLocation()));
            }
            //更新
            this.updateBatchById(zdjPickupList);
            return R.ok();
        }else{
            throw new CustomBusinessException("所选订单不存在！");
        }
    }

    /**
     * 返仓后-重新派送
     */
    @Transactional
    @Override
    public R rwReDelivery(List<Long> ids) {
        List<TmsZdjPickupEntity> zdjPickupList = this.listByIds(ids);
        if(CollectionUtil.isNotEmpty(zdjPickupList)){
            //记录要重派的跟踪单号
            Set<String> orderNoList = new HashSet<>();
            for (TmsZdjPickupEntity tmsZdjPickupEntity : zdjPickupList) {
                if(tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.REDELIVERY.getCode())){
                    throw new CustomBusinessException("该包裹已重派！");
                }
                //当记录的状态大于等于已取件的整数值2的时候，不允许重派（已取件或者销毁了）
                if(tmsZdjPickupEntity.getPickupStatus()>=PickupStatusEnum.PICKED_UP.getCode()){
                    throw new CustomBusinessException("该包裹已取件或者销毁了，不允许重派！");
                }
                //修改状态为重新派送
                tmsZdjPickupEntity.setPickupStatus(PickupStatusEnum.REDELIVERY.getCode());
                orderNoList.add(tmsZdjPickupEntity.getOrderNo());
            }
            //更新
            this.updateBatchById(zdjPickupList);
            //修改订单的状态为重新派送
            tmsCustomerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoList)
                    //设置为重新派送状态
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.RETURN_TO_SEND)
                    //将其设置回为未路径规划，使其能够重新进行路径规划
                    .set(TmsCustomerOrderEntity::getIsDeliveryRoutePlan, Boolean.TRUE)
            );
        return R.ok();
        }else{
            throw new CustomBusinessException("所选订单不存在！");
        }
    }

    /**
     * 返仓后-发送短信
     */
    @Override
    public R rwSendMessage(List<Long> ids) {
        List<TmsZdjPickupEntity> zdjPickupEntityList = this.listByIds(ids);
        boolean sendSuccess = true;
        //遍历发送短信
        for (TmsZdjPickupEntity tmsZdjPickupEntity : zdjPickupEntityList) {
            if(StrUtil.isBlank(tmsZdjPickupEntity.getPickupCode())){
                throw new CustomBusinessException("该单还没有取件码！");
            }

            if(!tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.APPOINTMENT.getCode())){
                throw new CustomBusinessException("该单还没有取件码！");
            }
            // 暂存短信模版
            String template="Your parcel ({{tracking number}}) is available for pickup at our warehouse.Pick up code: ({Pick up code}). We will hold it for 5 business days before returning it to the sender.\n" +
                    "Please visit us at your earliest convenience to pick it up.";

            template=template.replace("{{tracking number}}", tmsZdjPickupEntity.getOrderNo())
                    .replace("{Pick up code}", tmsZdjPickupEntity.getPickupCode());
            //派送完成给客户发送短信
            R result = ALiYunSms.sentMes(tmsZdjPickupEntity.getRecipientPhone(), template);
            if(!result.isOk()){
                sendSuccess=false;
            }
        }
        if(sendSuccess){
            return  R.ok();
        }else{
            return R.failed("派送完成给客户发送短信失败");
        }
    }

    /**
     * 根据id查询上架自提记录
     */
    @Override
    public R getPickUpCodeById(List<Long> ids) {
        return R.ok(this.listByIds(ids));
    }



        /**
     * 仓库端-返仓上架扫描
     */
    @Transactional
    @Override
    public R returnWareHouseShelfScan(List<RWShelfScanDto> rwShelfScanDtoList) {
        Set<String> entrustedOrderNumbers = rwShelfScanDtoList.stream().map(RWShelfScanDto::getEntrustedOrderNumber).collect(Collectors.toSet());
        //根据传入的跟踪单号查询订单信息
        List<TmsCustomerOrderEntity> orderList = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));

        //根据仓位id列表查询仓位信息
        Set<Long> locationIds = rwShelfScanDtoList.stream().map(RWShelfScanDto::getLocationId).collect(Collectors.toSet());
        if(CollectionUtil.isEmpty(locationIds)){
            throw new CustomBusinessException(LocalizedR.getMessage("error.warehouse.notExist",null));
        }
        Map<Long, TmsWarehouseLocationEntity> warehouseLocationMap = tmsWarehouseLocationService.list(new LambdaQueryWrapper<TmsWarehouseLocationEntity>()
                        .in(TmsWarehouseLocationEntity::getLocationId, locationIds))
                .stream().collect(Collectors.toMap(TmsWarehouseLocationEntity::getLocationId, Function.identity()));

        //查询订单是否已有返仓自提记录
        Map<String, TmsZdjPickupEntity> orderPickupMap = this.list(new LambdaQueryWrapper<TmsZdjPickupEntity>()
                        .in(TmsZdjPickupEntity::getOrderNo, entrustedOrderNumbers))
                .stream().collect(Collectors.toMap(TmsZdjPickupEntity::getOrderNo, Function.identity()));

        List<TmsZdjPickupEntity> tmsZdjPickups = new ArrayList<>();
        List<TmsCustomerOrderEntity> waitUpdateOrderList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(orderList)){
            Map<String, TmsCustomerOrderEntity> orderMap = orderList.stream().collect(Collectors.toMap(TmsCustomerOrderEntity::getEntrustedOrderNumber, Function.identity()));
            for (RWShelfScanDto rwShelfScanDto : rwShelfScanDtoList) {
                //判断是否已经存在上架扫描记录
                if(ObjectUtil.isNotNull(orderPickupMap.get(rwShelfScanDto.getEntrustedOrderNumber()))){
//                    throw new CustomBusinessException("订单【"+rwShelfScanDto.getEntrustedOrderNumber()+"】已存在上架扫描记录！");
                    throw new CustomBusinessException(LocalizedR.getMessage("error.order.alreadyScanned",new Object[]{rwShelfScanDto.getEntrustedOrderNumber()}));
                }
                //根据状态判断是否需要返仓扫描上架-自提
                TmsCustomerOrderEntity tmsCustomerOrderEntity = orderMap.get(rwShelfScanDto.getEntrustedOrderNumber());
                if(ObjectUtil.isNotNull(tmsCustomerOrderEntity)){
                    if(!tmsCustomerOrderEntity.getOrderStatus().equals(NewOrderStatus.AWAITING_RETURN.getCode())){
//                        throw new CustomBusinessException("订单【"+tmsCustomerOrderEntity.getEntrustedOrderNumber()+"】无需返仓上架扫描！");
                        throw new CustomBusinessException(LocalizedR.getMessage("error.order.noNeedScan",new Object[]{tmsCustomerOrderEntity.getEntrustedOrderNumber()}));
                    }
                    //根据传入的仓库、仓位、单号创建自提记录rwShelfScanDto
                    TmsZdjPickupEntity tmsZdjPickupEntity = new TmsZdjPickupEntity();
                    //订单号
                    tmsZdjPickupEntity.setOrderNo(tmsCustomerOrderEntity.getEntrustedOrderNumber());
                    //仓位
                    tmsZdjPickupEntity.setWarehouseLocation(warehouseLocationMap.get(rwShelfScanDto.getLocationId()).getLocationCode());
                    //仓位id
                    tmsZdjPickupEntity.setLocationId(rwShelfScanDto.getLocationId());
                    //仓库id
                    tmsZdjPickupEntity.setWarehouseId(rwShelfScanDto.getWarehouseId());
                    //收件人
                    tmsZdjPickupEntity.setRecipientName(tmsCustomerOrderEntity.getReceiverName());
                    //收件人电话
                    tmsZdjPickupEntity.setRecipientPhone(tmsCustomerOrderEntity.getReceiverPhone());
                    //上架时间
                    tmsZdjPickupEntity.setShelvingTime(LocalDateTime.now());

                    //订单数量
                    tmsZdjPickupEntity.setTotalCount(rwShelfScanDto.getTotalCount());
                    //已扫描数
                    tmsZdjPickupEntity.setScanCount(rwShelfScanDto.getScanCount());

                    //修改订单的状态为已返仓
                    tmsCustomerOrderEntity.setOrderStatus(NewOrderStatus.RETURNED.getCode());
                    waitUpdateOrderList.add(tmsCustomerOrderEntity);
                    tmsZdjPickups.add(tmsZdjPickupEntity);

                    // 添加轨迹记录
                    orderTrackService.saveTrack(
                            tmsCustomerOrderEntity.getEntrustedOrderNumber(),
                            tmsCustomerOrderEntity.getCustomerOrderNumber(),
                            NewOrderStatus.RETURNED.getValue(),
                            OrderTrackLink.SELF_PICKUP.getCode()
                    );
                }
            }
            //批量更新订单-已返仓
            tmsCustomerOrderService.updateBatchById(waitUpdateOrderList);
            //批量新增中大件自提信息
            this.saveBatch(tmsZdjPickups);

            //将在派送任务失败时的相关逻辑迁移到此处（将返仓自提的该单移出派送任务，并将其在路径规划的访问点数据清除）
            deleteDeliverTaskAndRoutePlanData(orderList);
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("error.order.notFound",null));

        }
        return R.ok();
    }

    private void deleteDeliverTaskAndRoutePlanData(List<TmsCustomerOrderEntity> orderList) {
        //提取主单(转主单)
        List<String> entrustedOrderNumberList = orderList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .map(entrustedOrderNumber->{
                    if(entrustedOrderNumber.length()>15){
                        //此时是子单，将其截取后三位成为主单
                        return entrustedOrderNumber.substring(0, entrustedOrderNumber.length() - 3);
                    }else{
                        return entrustedOrderNumber;
                    }
                })
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(entrustedOrderNumberList)){
            //根据主单查询
            List<TmsCustomerOrderEntity> masterOrderList = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumberList));
            //筛选出此时的任务单号(移出派送任务前的所有派送任务单号)
            Set<String> deliveryTaskOrderNos = masterOrderList.stream().map(TmsCustomerOrderEntity::getDTaskOrder).filter(Objects::nonNull).collect(Collectors.toSet());

            //将上述返仓自提的订单移出派送任务即将其任务单号设置为null-移出
            tmsCustomerOrderService.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber,entrustedOrderNumberList)
                    .set(TmsCustomerOrderEntity::getDTaskOrder,null));

            //将规划中的访问点数据清除
            tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getShipmentLabel, entrustedOrderNumberList));

            Map<String, List<TmsCustomerOrderEntity>> waitUpdateCustomerOrderMap=new HashMap<>();
            if(CollectionUtil.isNotEmpty(deliveryTaskOrderNos)){
                //移出后的订单分组map
                waitUpdateCustomerOrderMap = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .in(TmsCustomerOrderEntity::getDTaskOrder, deliveryTaskOrderNos)).stream()
                        .collect(Collectors.groupingBy(TmsCustomerOrderEntity::getDTaskOrder));
            }
            if(MapUtil.isNotEmpty(waitUpdateCustomerOrderMap)){
                for(Map.Entry<String, List<TmsCustomerOrderEntity>> entry : waitUpdateCustomerOrderMap.entrySet()){
                    String key = entry.getKey();
                    List<TmsCustomerOrderEntity> value = entry.getValue();
                    //判断该任务下的订单是否全部完成
                    boolean isAllCustomerOrderFinished = value.stream().allMatch(customerOrder ->
                            customerOrder.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())
                    );

                    //重新统计一下此时该派送任务的相关数据（重量、体积、货物数量、订单数）并更新
                    Double weight = value.stream().mapToDouble(order -> order.getTotalWeight().doubleValue()).sum();
                    //体积
                    Double volume = value.stream().mapToDouble(order -> order.getTotalVolume().doubleValue()).sum();
                    //货物数量
                    int cargoQuantity = value.stream().mapToInt(TmsCustomerOrderEntity::getCargoQuantity).sum();
                    LambdaUpdateWrapper<TmsTransportTaskOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();

                    updateWrapper.eq(TmsTransportTaskOrderEntity::getTaskOrderNo, key)
                            //重量
                            .set(TmsTransportTaskOrderEntity::getTotalWeight, new BigDecimal(weight.toString()))
                            //体积
                            .set(TmsTransportTaskOrderEntity::getTotalVolume, new BigDecimal(volume.toString()))
                            //货物数量
                            .set(TmsTransportTaskOrderEntity::getCargoQuantity, cargoQuantity)
                            //订单数
                            .set(TmsTransportTaskOrderEntity::getOrderCount, value.size());

                    if(isAllCustomerOrderFinished){
                        //如果全部完成则将其任务设置为完成状态
                        updateWrapper.set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode());
                    }
                    //更新
                    tmsTransportTaskOrderService.update(updateWrapper);
                }
            }


            //判断派送任务是否还有订单，如果该派送任务已经没有相应的订单了，则将其派送任务删除
            Set<String> waitDeleteDeliveryTaskOrderNoSet = new HashSet<>();
            for (String deliveryTaskOrderNo : deliveryTaskOrderNos) {
                if(ObjectUtil.isNull(waitUpdateCustomerOrderMap.get(deliveryTaskOrderNo))){
                    waitDeleteDeliveryTaskOrderNoSet.add(deliveryTaskOrderNo);
                }
            }
            //删除没有订单的派送任务并将其对应的规划数据也清除掉
            if(CollectionUtil.isNotEmpty(waitDeleteDeliveryTaskOrderNoSet)){
                List<TmsTransportTaskOrderEntity> waitDeleteDeliveryTaskOrderList = tmsTransportTaskOrderService.list(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                        .in(TmsTransportTaskOrderEntity::getTaskOrderNo, waitDeleteDeliveryTaskOrderNoSet));
                //筛选出此时所有派送任务中的规划名称
                Set<String> planNameSet = waitDeleteDeliveryTaskOrderList.stream().map(TmsTransportTaskOrderEntity::getPlanName).collect(Collectors.toSet());
                //删除没有订单了的派送任务
                tmsTransportTaskOrderService.removeBatchByIds(waitDeleteDeliveryTaskOrderList);
                //规划数据
                List<TmsRoutePlanEntity> routePlanList = tmsRoutePlanService.list(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                        .in(TmsRoutePlanEntity::getPlanName, planNameSet));
                List<Long> routePlanIds = routePlanList.stream().map(TmsRoutePlanEntity::getRoutePlanId).collect(Collectors.toList());
                List<TmsVehicleRouteEntity> vehicleRouteList=new ArrayList<>();
                if(CollectionUtil.isNotEmpty(routePlanIds)){
                    //车辆规划数据
                    vehicleRouteList = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                            .in(TmsVehicleRouteEntity::getRoutePlanId, routePlanIds));
                    List<Long> vehicleRouteIdList = vehicleRouteList.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(vehicleRouteIdList)){
                        //删除访问点数据
                        tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                                .in(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRouteIdList));
                    }
                }
                //删除规划数据
                tmsRoutePlanService.removeBatchByIds(routePlanList);
                tmsVehicleRouteService.removeBatchByIds(vehicleRouteList);
            }
        }
    }

    /**
     *查询返仓记录-跟踪单号、取件码、收件人、收件人电话
     */
    @Override
    public R getRwRecord(String condition) {
        condition=tmsLmdDriverService.isCustomerOrder(condition,false);
        LambdaQueryWrapper<TmsZdjPickupEntity> wrapper = new LambdaQueryWrapper<>();
        //N开头并且是16位时
        if(condition.startsWith("N") && condition.length() == 16){
            //截取后三位得到主单（包裹时子单）
            condition = condition.substring(0,condition.length()-3);
        }
        wrapper.eq(TmsZdjPickupEntity::getOrderNo,condition)
                .or()
                .eq(TmsZdjPickupEntity::getPickupCode,condition)
                .or()
                .eq(TmsZdjPickupEntity::getRecipientName,condition)
                .or()
                .eq(TmsZdjPickupEntity::getRecipientPhone,condition);
        List<TmsZdjPickupEntity> records = this.list(wrapper);
        List<TmsZdjPickupOWVo> tmsZdjPickupOWVos = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(records)){
            //涉及的仓库
            List<Long> warehouseIds = records.stream().map(TmsZdjPickupEntity::getWarehouseId).collect(Collectors.toList());
            //查询涉及的仓库
            Map<Long, TmsSiteEntity> siteMap = tmsSiteMapper.selectList(new LambdaQueryWrapper<TmsSiteEntity>()
                            .in(TmsSiteEntity::getId,warehouseIds))
                    .stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
            //涉及的仓位
            List<Long> locationIds = records.stream().map(TmsZdjPickupEntity::getLocationId).collect(Collectors.toList());
            Map<Long, TmsWarehouseLocationEntity> warehouseLocationMap = tmsWarehouseLocationService.list(new LambdaQueryWrapper<TmsWarehouseLocationEntity>()
                            .in(TmsWarehouseLocationEntity::getLocationId, locationIds))
                    .stream().collect(Collectors.toMap(TmsWarehouseLocationEntity::getLocationId, Function.identity()));
            for (TmsZdjPickupEntity record : records) {
                TmsZdjPickupOWVo tmsZdjPickupOWVo = new TmsZdjPickupOWVo();
                BeanUtils.copyProperties(record,tmsZdjPickupOWVo);
//                if(!record.getPickupStatus().equals(PickupStatusEnum.APPOINTMENT.getCode())){
//                    throw new CustomBusinessException(LocalizedR.getMessage("error.order.notInAppointment",null));
//                }
                if(ObjectUtil.isNotNull(warehouseLocationMap.get(record.getLocationId()).getLocationCode())){
                    //仓位名称
                    tmsZdjPickupOWVo.setLocationName(warehouseLocationMap.get(record.getLocationId()).getLocationCode());
                }
                if(ObjectUtil.isNotNull(siteMap.get(record.getWarehouseId()).getSiteName())){
                    //仓库名称
                    tmsZdjPickupOWVo.setWarehouseName(siteMap.get(record.getWarehouseId()).getSiteName());
                }
                tmsZdjPickupOWVos.add(tmsZdjPickupOWVo);
            }
        }
        return R.ok(tmsZdjPickupOWVos);
    }

    /**
     * 仓库端-提交出仓证明
     */
    @Transactional
    @Override
    public R submitOwProof(Long id,String outWarehouseProof) {
        TmsZdjPickupEntity tmsZdjPickupEntity = this.getById(id);
        if(!tmsZdjPickupEntity.getPickupStatus().equals(PickupStatusEnum.APPOINTMENT.getCode())){
            throw new CustomBusinessException(LocalizedR.getMessage("error.order.notInAppointment",null));
        }
        if(ObjectUtil.isNotNull(tmsZdjPickupEntity)){
            //修改自提状态为已取件
            tmsZdjPickupEntity.setPickupStatus(PickupStatusEnum.PICKED_UP.getCode());
            //保存出仓证明（取件证明）
            tmsZdjPickupEntity.setPickupImages(outWarehouseProof);
            //出仓自提时间
            tmsZdjPickupEntity.setPickupTime(LocalDateTime.now());
            //更新
            this.updateById(tmsZdjPickupEntity);

            //自提出仓后，修改该订单为已完成状态
            tmsCustomerOrderService.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsZdjPickupEntity.getOrderNo())
                    //修改订单的状态为已完成
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())
            );

            // 根据tmsZdjPickupEntity.getOrderNo()查询订单信息
            TmsCustomerOrderEntity customerOrder = tmsCustomerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsZdjPickupEntity.getOrderNo()),false
            );

            if (customerOrder != null){
                // 添加轨迹记录：自提出仓，订单已完成
                orderTrackService.saveTrack(
                        tmsZdjPickupEntity.getOrderNo(),
                        customerOrder.getCustomerOrderNumber(),
                        NewOrderStatus.COMPLETED.getValue(),
                        OrderTrackLink.PICKED_UD.getCode()
                );
            }
            return R.ok(tmsZdjPickupEntity);
        }else{
           throw new CustomBusinessException(LocalizedR.getMessage("error.submit.fail",null));
        }
    }

    /**
     * 仓库端-查看出仓证明
     */
    @Override
    public R getOwProof(Long id) {
        TmsZdjPickupEntity zdjPickup = this.getById(id);
        return R.ok(zdjPickup.getPickupImages());
    }

    @Override
    public R getOrderCountAndCheck(Long locationId,String orderNo) {
        orderNo=tmsLmdDriverService.isCustomerOrder(orderNo,false);
        //检查订单号合格性（子单：N开头,共16位）
        if (!((orderNo.startsWith("N") && orderNo.charAt(5) == 'D') && orderNo.length() == 16)) {
            throw new CustomBusinessException(LocalizedR.getMessage("error.orderNo.format",null));
        }
        //根据仓位id查询仓位
        TmsWarehouseLocationEntity warehouseLocation = tmsWarehouseLocationService.getById(locationId);
        if(ObjectUtil.isNull(warehouseLocation)){
            throw new CustomBusinessException(LocalizedR.getMessage("error.warehouse.notExist",null));
        }
        TmsCustomerOrderEntity order = tmsCustomerOrderService.getOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));
        if(ObjectUtil.isNull(order)){
            throw new CustomBusinessException(LocalizedR.getMessage("error.order.notFound",null));
        }


        //修改此时的子单为已返仓扫描状态(0:未扫描，1：已扫描)
        tmsCustomerOrderService.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                .set(TmsCustomerOrderEntity::getIsRwScan, 1));
        //截取后三位得到主单号
        String masterOrderNo = orderNo.substring(0, orderNo.length() - 3);
        //主子单
        List<TmsCustomerOrderEntity> customerOrderList = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, masterOrderNo));
        if(CollectionUtil.isEmpty(customerOrderList)){
            throw new CustomBusinessException(LocalizedR.getMessage("error.order.notFound",null));
        }
        //校验主单是否已经存在上架记录
        TmsZdjPickupEntity zdjPickupEntity = this.getOne(new LambdaQueryWrapper<TmsZdjPickupEntity>().eq(TmsZdjPickupEntity::getOrderNo, masterOrderNo));
        if(ObjectUtil.isNotNull(zdjPickupEntity)){
            throw new CustomBusinessException(LocalizedR.getMessage("error.order.alreadyOnShelf",null));
        }
        List<TmsCustomerOrderEntity> scanCustomerOrderList = customerOrderList.stream()
                .filter(item -> item.getIsRwScan().equals(1))
                .filter(item -> item.getSubFlag().equals(Boolean.TRUE)).collect(Collectors.toList());
        Map<String, Object> scanReturnMap = new HashMap<>();
        //订单总件数
        scanReturnMap.put("totalCount",customerOrderList.size()-1);
        //订单已扫描件数
        scanReturnMap.put("scanCount",scanCustomerOrderList.size());
        //仓位名称
        scanReturnMap.put("locationCode",warehouseLocation.getLocationCode());
        //仓位id
        scanReturnMap.put("locationId",warehouseLocation.getLocationId());
        //主单号
        scanReturnMap.put("masterOrderNo",masterOrderNo);
        //客户单号
        scanReturnMap.put("customerOrderNo",order.getCustomerOrderNumber());

        if (order.getIsRwScan().equals(1)) {
            scanReturnMap.put("isScan",true);
        }else{
            scanReturnMap.put("isScan",false);
        }

        return R.ok(scanReturnMap);
    }

    @Override
    public R deleteScanDataByOrderNo(String orderNo) {
        //获取所有单号(主子单)
        List<TmsCustomerOrderEntity> allOrderList = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));
        for (TmsCustomerOrderEntity tmsCustomerOrderEntity : allOrderList) {
            //将上架扫描状态修改为未扫描
            tmsCustomerOrderEntity.setIsRwScan(0);
        }
        return R.ok(tmsCustomerOrderService.updateBatchById(allOrderList));
    }

    @Override
    public List<TmsZdjPickupEntity> getPickUpCodeByIdForRomote(List<Long> idList) {
        return this.list(new LambdaQueryWrapper<TmsZdjPickupEntity>().in(TmsZdjPickupEntity::getId, idList));
    }


    /**
     * 生成取件码
     * @param warehouseCode 仓位
     * @return
     */
    public static String generatePickupCode(String warehouseCode) {
        // 5位随机字符串
        String randomStr = RandomUtil.randomString(5);
        String temp= warehouseCode +"-"+ randomStr;
        // 拼接并返回(全部大写)
        return temp.toUpperCase() ;
    }
}