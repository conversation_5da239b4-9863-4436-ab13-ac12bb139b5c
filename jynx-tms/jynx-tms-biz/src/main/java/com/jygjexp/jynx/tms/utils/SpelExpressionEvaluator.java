package com.jygjexp.jynx.tms.utils;

import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SpelExpressionEvaluator {

    private static final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 评估布尔表达式是否成立
     *
     * @param expression SpEL 表达式，例如：(30 < weight && weight <= 68) && (length <= 100)
     * @param variables 变量值 map，例如：{weight=55, length=80}
     * @return true 或 false
     */
    public static boolean evaluate(String expression, Map<String, Object> variables) {
        try {
            StandardEvaluationContext context = new StandardEvaluationContext();
            if (variables != null) {
                variables.forEach(context::setVariable);
            }

            Expression exp = parser.parseExpression(expression);
            Boolean result = exp.getValue(context, Boolean.class);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            System.err.println("表达式计算异常: " + e.getMessage());
            return false;
        }
    }

    public static void main(String[] args) {
        // 示例表达式
        String expression = "(#weight > 68.01 && #weight <= 100) || (#length > 274.01 && #length <= 500) || (#length+(#width+#height)*2 > 419.01 && #length+(#width+#height)*2 <= 512)";

        // 构造变量值
        Map<String, Object> variables = new HashMap<>();
        variables.put("weight", 68.01);
        variables.put("width", 0);
        variables.put("length", 200);
        variables.put("height", 0);

        // 调用表达式求值方法
        boolean result = evaluate(expression, variables);
        System.out.println("表达式结果: " + result);  // 应该输出 true

        // 提取表达式中用到的变量名
        Set<String> usedVariables = extractVariables(expression);
        System.out.println("提取到的变量名: " + usedVariables);  // 应该输出 [weight, length]
    }

    /**
     * 从表达式中提取所有变量名（变量名不能是关键字或数字）
     *
     * @param expression SpEL 表达式
     * @return 使用到的变量名集合（如：weight, length）
     */
    public static Set<String> extractVariables(String expression) {
        Set<String> variables = new LinkedHashSet<>();
        Pattern pattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z_0-9]*)\\b");
        Matcher matcher = pattern.matcher(expression);

        Set<String> reservedWords = new HashSet<>(Arrays.asList(
                "and", "or", "not",
                "true", "false", "null",
                "eq", "ne", "lt", "le", "gt", "ge"
        ));

        while (matcher.find()) {
            String token = matcher.group(1);
            if (!reservedWords.contains(token.toLowerCase()) && !isNumber(token)) {
                variables.add(token);
            }
        }
        return variables;
    }

    private static boolean isNumber(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException ex) {
            return false;
        }
    }
}
