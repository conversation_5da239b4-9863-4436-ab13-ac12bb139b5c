package com.jygjexp.jynx.tms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsPayOrderEntity;
import com.jygjexp.jynx.tms.mapper.TmsPayOrderMapper;
import com.jygjexp.jynx.tms.service.TmsPayOrderService;
import org.springframework.stereotype.Service;
/**
 * 用户充值支付订单表
 *
 * <AUTHOR>
 * @date 2025-07-25 17:23:22
 */
@Service
public class TmsPayOrderServiceImpl extends ServiceImpl<TmsPayOrderMapper, TmsPayOrderEntity> implements TmsPayOrderService {

    // 创建用户充值支付订单
    @Override
    public boolean createPayOrder(TmsPayOrderEntity payOrder) {
        return save(payOrder);
    }
}