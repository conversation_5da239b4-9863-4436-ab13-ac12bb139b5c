package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceOfflineRechargeExportDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsSotreBalanceOfflineRechargeMapper;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceOfflineRechargeService;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceOfflineRechargePageVo;
import com.mongoplus.toolkit.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 手工调账
 *
 * <AUTHOR>
 * @date 2025-07-30 03:25:12
 */
@Service
public class TmsStoreBalanceOfflineRechargeServiceImpl
        extends MPJBaseServiceImpl<TmsSotreBalanceOfflineRechargeMapper, TmsStoreBalanceOfflineRecharge>
        implements TmsStoreBalanceOfflineRechargeService {

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    @Override
    public Page<TmsStoreBalanceOfflineRecharge> search(TmsStoreBalanceOfflineRechargePageVo vo) {
        MPJLambdaWrapper<TmsStoreBalanceOfflineRecharge> wrapper = JoinWrappers.lambda(TmsStoreBalanceOfflineRecharge.class)
                .selectAll(TmsStoreBalanceOfflineRecharge.class)
                .selectAs(TmsStoreCustomerEntity::getName, TmsStoreBalanceOfflineRecharge::getCustomerName)
                .selectAs(TmsStoreEntity::getStoreName, TmsStoreBalanceOfflineRecharge::getStoreName)
                .leftJoin(TmsStoreCustomerEntity.class, TmsStoreCustomerEntity::getId, TmsStoreBalanceOfflineRecharge::getStoreCustomerId)
                .leftJoin(TmsStoreEntity.class, TmsStoreEntity::getId, TmsStoreBalanceOfflineRecharge::getStoreId)
                .eq(ObjUtil.isNotNull(vo.getStoreId()), TmsStoreBalanceOfflineRecharge::getStoreId, vo.getStoreId())
                .eq(ObjUtil.isNotNull(vo.getCustomerId()), TmsStoreBalanceOfflineRecharge::getStoreCustomerId, vo.getCustomerId())
                .eq(ObjUtil.isNotNull(vo.getStatus()), TmsStoreBalanceOfflineRecharge::getStatus, vo.getStatus())
                .between(ObjUtil.isNotNull(vo.getVerifyStartTime()) && ObjUtil.isNotNull(vo.getVerifyStartTime()),
                        TmsStoreBalanceOfflineRecharge::getVerifyTime, vo.getVerifyStartTime(), vo.getVerifyEndTime())
                .between(ObjUtil.isNotNull(vo.getCreateStartTime()) && ObjUtil.isNotNull(vo.getCreateEndTime()),
                        TmsStoreBalanceOfflineRecharge::getCreateTime, vo.getCreateStartTime(), vo.getCreateEndTime())
                .orderByDesc(TmsStoreBalanceOfflineRecharge::getId)
                .logicDelToOn();
        return selectJoinListPage(new Page<>(vo.getCurrent(), vo.getSize()), TmsStoreBalanceOfflineRecharge.class, wrapper);
    }

    /**
     * 审批
     *
     * @param id
     * @param verifyRemarks
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean verify(Long id, String verifyRemarks) {
        TmsStoreBalanceOfflineRecharge entity = getOptById(id)
                .filter(e -> !ObjUtil.equal(e.getStatus(), 1))
                .orElseThrow(() -> new CustomBusinessException("This record is not exist or reviewed and completed"));

        //消费记录
        Long storeBalanceRecordId = entity.getStoreBalanceRecordId();
        TmsStoreBalanceRecordEntity storeBalanceRecord = Db.getById(storeBalanceRecordId, TmsStoreBalanceRecordEntity.class);

        //客户余额
        Long storeBalanceId = storeBalanceRecord.getStoreBalanceId();
        TmsStoreBalanceEntity storeBalance = Db.getById(storeBalanceId, TmsStoreBalanceEntity.class);

        //校验金额
        String storedAmountHash = DigestUtil.sha256Hex(storeBalance.getAmount().toString());
        if (!StrUtil.equals(storedAmountHash, storeBalance.getAmountHash())) {
            throw new CustomBusinessException("amount exception");
        }

        //更新充值记录
        entity.setStatus(1);
        entity.setVerifyTime(LocalDateTime.now());
        entity.setVerifyRemarks(verifyRemarks);
        entity.setVerifyer(SecurityUtils.getUser().getUsername());
        entity.updateById();

        //更新消费记录
        storeBalanceRecord.setStatus(1);
        storeBalanceRecord.updateById();

        //更新余额
        BigDecimal currentAmount = storeBalance.getAmount().add(storeBalanceRecord.getChangeAmount());
        String currentAmountHash = DigestUtil.sha256Hex(currentAmount.toString());
        storeBalance.setAmount(currentAmount);
        storeBalance.setAmountHash(currentAmountHash);
        storeBalance.updateById();
        return true;
    }

    /**
     * 驳回
     *
     * @param id
     * @param verifyRemarks
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reject(Long id, String verifyRemarks) {
        TmsStoreBalanceOfflineRecharge entity = getOptById(id)
                .filter(e -> ObjUtil.equal(e.getStatus(), 0))
                .orElseThrow(() -> new CustomBusinessException("This record is not exist or reviewed and completed"));

        entity.setStatus(2);
        entity.setVerifyTime(LocalDateTime.now());
        entity.setVerifyRemarks(verifyRemarks);
        entity.setVerifyer(SecurityUtils.getUser().getUsername());
        entity.updateById();

        //更新消费记录
        Long storeBalanceRecordId = entity.getStoreBalanceRecordId();
        TmsStoreBalanceRecordEntity storeBalanceRecord = Db.getById(storeBalanceRecordId, TmsStoreBalanceRecordEntity.class);
        storeBalanceRecord.setStatus(-1);
        storeBalanceRecord.updateById();
        return true;
    }

    /**
     * 导出
     *
     * @param vo
     * @return
     */
    @Override
    public List<TmsStoreBalanceOfflineRechargeExportDto> export(TmsStoreBalanceOfflineRechargePageVo vo) {
        MPJLambdaWrapper<TmsStoreBalanceOfflineRecharge> wrapper = JoinWrappers.lambda(TmsStoreBalanceOfflineRecharge.class)
                .selectAll(TmsStoreBalanceOfflineRecharge.class)
                .selectAs(TmsStoreCustomerEntity::getName, TmsStoreBalanceOfflineRecharge::getCustomerName)
                .selectAs(TmsStoreEntity::getStoreName, TmsStoreBalanceOfflineRecharge::getStoreName)
                .leftJoin(TmsStoreCustomerEntity.class, TmsStoreCustomerEntity::getId, TmsStoreBalanceOfflineRecharge::getStoreCustomerId)
                .leftJoin(TmsStoreEntity.class, TmsStoreEntity::getId, TmsStoreBalanceOfflineRecharge::getStoreId)
                .eq(ObjUtil.isNotNull(vo.getStoreId()), TmsStoreBalanceOfflineRecharge::getStoreId, vo.getStoreId())
                .eq(ObjUtil.isNotNull(vo.getCustomerId()), TmsStoreBalanceOfflineRecharge::getStoreCustomerId, vo.getCustomerId())
                .eq(ObjUtil.isNotNull(vo.getStatus()), TmsStoreBalanceOfflineRecharge::getStatus, vo.getStatus())
                .in(CollUtil.isNotEmpty(vo.getIds()), TmsStoreBalanceOfflineRecharge::getId, vo.getIds())
                .between(ObjUtil.isNotNull(vo.getVerifyStartTime()) && ObjUtil.isNotNull(vo.getVerifyStartTime()),
                        TmsStoreBalanceOfflineRecharge::getVerifyTime, vo.getVerifyStartTime(), vo.getVerifyEndTime())
                .between(ObjUtil.isNotNull(vo.getCreateStartTime()) && ObjUtil.isNotNull(vo.getCreateEndTime()),
                        TmsStoreBalanceOfflineRecharge::getCreateTime, vo.getCreateStartTime(), vo.getCreateEndTime())
                .orderByDesc(TmsStoreBalanceOfflineRecharge::getId)
                .logicDelToOn();
        return selectJoinList(TmsStoreBalanceOfflineRechargeExportDto.class, wrapper);
    }
}