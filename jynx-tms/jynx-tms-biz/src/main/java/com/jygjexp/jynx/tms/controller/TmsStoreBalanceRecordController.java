package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceRecordExportDto;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceRecordEntity;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceRecordService;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceRecordPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 门店余额变更记录表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:44:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/store/balance/record")
@Tag(description = "tmsStoreBalanceRecord", name = "门店余额变更记录表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreBalanceRecordController {

    private final TmsStoreBalanceRecordService tmsStoreBalanceRecordService;

    /**
     * 分页查询
     *
     * @param vo 门店余额变更记录表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/search")
//    @PreAuthorize("@pms.hasPermission('StoreBalanceRecord_view')")
    public R<Page<TmsStoreBalanceRecordEntity>> search(@RequestBody @Valid TmsStoreBalanceRecordPageVo vo) {
        return R.ok(tmsStoreBalanceRecordService.search(vo));
    }

    /**
     * 导出excel 表格
     *
     * @param ids                   导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
//    @PreAuthorize("@pms.hasPermission('StoreBalanceRecord_export')")
    public List<TmsStoreBalanceRecordExportDto> export(@RequestBody TmsStoreBalanceRecordPageVo vo) {
        return tmsStoreBalanceRecordService.export(vo);
    }
}
