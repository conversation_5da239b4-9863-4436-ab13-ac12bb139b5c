package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceOfflineRechargeExportDto;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceOfflineRecharge;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceOfflineRechargePageVo;

import java.util.List;

public interface TmsStoreBalanceOfflineRechargeService extends MPJBaseService<TmsStoreBalanceOfflineRecharge> {

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    Page<TmsStoreBalanceOfflineRecharge> search(TmsStoreBalanceOfflineRechargePageVo vo);

    /**
     * 审批
     *
     * @param id
     * @param verifyRemarks
     * @return
     */
    Boolean verify(Long id, String verifyRemarks);

    /**
     * 驳回
     *
     * @param id
     * @param verifyRemarks
     * @return
     */
    Boolean reject(Long id, String verifyRemarks);

    /**
     * 导出
     *
     * @param vo
     * @return
     */
    List<TmsStoreBalanceOfflineRechargeExportDto> export(TmsStoreBalanceOfflineRechargePageVo vo);
}