package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderMapper;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.*;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.api.FreightDataItem;
import com.jygjexp.jynx.tms.vo.api.FreightRoot;
import com.jygjexp.jynx.tms.vo.api.PackageOrderRoot;
import com.jygjexp.jynx.zxoms.excel.ExcelReader;
import com.jygjexp.jynx.zxoms.excel.exception.ExcelValidatedException;
import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 快递客户订单
 *
 * <AUTHOR>
 * @date 2025-07-14 17:34:55
 */
@Service
@RequiredArgsConstructor
public class TmsStoreOrderServiceImpl extends ServiceImpl<TmsStoreOrderMapper, TmsStoreOrderEntity> implements TmsStoreOrderService {
    private static final Logger logger = LoggerFactory.getLogger(TmsStoreOrderServiceImpl.class);
    private final TmsStoreOrderGoodsService storeOrderGoodsService;
    private final TmsStoreCustomerService storeCustomerService;
    private final TmsStoreProviderRelationService storeProviderRelationService;
    private final TmsServiceProviderService serviceProviderService;
    private final TmsStoreService storeService;
    private final TmsRoutePlanService routePlanService;
    private final Validator validator;
    private final TmsStoreUserService storeUserService;
    private final TmsStoreOrderExceptionService tmsStoreOrderExceptionService;
    private final TmsStoreOrderPkgPayService tmsStoreOrderPkgPayService;
    private final TmsCustomerService customerService;
    private final TmsCustomerOrderService customerOrderService;
    private final TmsReceivableService tmsReceivableService;
    private final TmsStoreOrderTraceService tmsStoreOrderTraceService;
    private final TmsStoreBalanceService storeBalanceService;
    private final ObjectMapper mapper = new ObjectMapper();

    @Override
    public Page<StoreOrderVO> selectPage(Page page, StoreOrderQueryDTO storeOrderQueryDTO) {
        Long customerId = storeOrderQueryDTO.getStoreCustomerId();
        if(null == customerId){
            Long userId = SecurityUtils.getUser().getId();
            Long storeCustomerId = storeCustomerService.getStoreCustomerIdByUserId(userId);
            storeOrderQueryDTO.setStoreCustomerId(storeCustomerId);
        }
        // 使用 XML 中的连表查询，直接返回分页结果
        IPage<StoreOrderVO> result = baseMapper.selectPageWithJoin(page, storeOrderQueryDTO);
        if(null == result){
            return new Page<>();
        }
        // 根据单位制转换
        result.getRecords().forEach(e->{
            Integer unitType = e.getUnitType();
            unitType = unitType == null ? UnitConvertUtil.UNIT_INTERNATIONAL : unitType;
            // 法英制度 单位换算
            if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
                BigDecimal totalWeight = e.getTotalWeight();
                if(null != totalWeight){
                    totalWeight = UnitConvertUtil.weightConvert(totalWeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
                    e.setTotalWeight(totalWeight);
                }
                BigDecimal totalVolume = e.getTotalVolume();
                if(null != totalVolume){
                    totalVolume = UnitConvertUtil.cubicInchesToCubicMeters(totalVolume);
                    e.setTotalVolume(totalVolume);
                }
            }
        });
        return (Page<StoreOrderVO>) result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean saveStoreOrder(StoreOrderDTO storeOrderDTO) {
        Long storeCustomerId = storeOrderDTO.getStoreCustomerId();
        TmsStoreCustomerEntity storeCustomer = null;
        if(null == storeCustomerId){
            Long userId = SecurityUtils.getUser().getId();
            storeCustomer = storeCustomerService.getStoreCustomerByUserId(userId);
            if(null == storeCustomer){
                throw new CustomBusinessException("客户不存在,不支持下单");
            }
            storeOrderDTO.setStoreCustomerId(storeCustomer.getId());
        }else{
            storeCustomer = storeCustomerService.getStoreCustomerById(storeCustomerId);
            if(null == storeCustomer){
                throw new CustomBusinessException("客户不存在,不支持下单");
            }
            storeOrderDTO.setStoreCustomerId(storeCustomer.getId());
        }
        List<StoreOrderGoodsDTO> storeOrderGoodsDTOS = storeOrderDTO.getStoreOrderGoods();
        if(CollUtil.isEmpty(storeOrderGoodsDTOS)){
            throw new CustomBusinessException("货物信息为空，不支持下单");
        }
        BigDecimal freightAmount = storeOrderDTO.getTotalFreightAmount();
        if(null == freightAmount || freightAmount.equals(BigDecimal.ZERO)){
            throw new CustomBusinessException("运费异常,不支持下单");
        }
        // 查询主单未核销订单金额-当前客户
        BigDecimal totalFreightAmountSum = baseMapper.getTotalFreightAmountSum(storeOrderDTO.getStoreCustomerId());
        TmsStoreBalanceEntity storeCustomerBalance = storeBalanceService.getByStoreCustomerId(storeOrderDTO.getStoreCustomerId());
        if(null == storeCustomerBalance){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额不存在,不支持下单");
        }
        BigDecimal amount = storeCustomerBalance.getAmount();
        BigDecimal checkAmount = freightAmount.add(totalFreightAmountSum);
        if(checkAmount.compareTo(amount) > 0){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额不足,请前往充值");
        }
        Long orderCount = baseMapper.getSubOrderCount();
        int orderCounts = orderCount != null ? orderCount.intValue() : 0;
        String mainOrderNo = ExpressOrderNoUtil.generateMainOrderNo(orderCounts);
        TmsStoreOrderEntity mainStoreOrder = BeanUtil.toBean(storeOrderDTO, TmsStoreOrderEntity.class);
        mainStoreOrder.setEntrustedOrderNumber(mainOrderNo);
        mainStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.MAIN.getValue());
        List<TmsStoreOrderEntity> saveStoreOrders = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);

        AtomicInteger index = new AtomicInteger(0);
        saveStoreOrders.add(mainStoreOrder);
        for (StoreOrderGoodsDTO storeOrderGoodsDTO : storeOrderGoodsDTOS) {
            TmsStoreOrderEntity subStoreOrder = BeanUtil.toBean(storeOrderDTO, TmsStoreOrderEntity.class);
            String subOrderNo = ExpressOrderNoUtil.generateSubOrderNo(mainOrderNo, index.incrementAndGet());
            subStoreOrder.setEntrustedOrderNumber(subOrderNo);
            subStoreOrder.setMainEntrustedOrder(mainOrderNo);
            subStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.SUB.getValue());
            saveStoreOrders.add(subStoreOrder);

            TmsStoreOrderGoodsEntity storeOrderGood = BeanUtil.toBean(storeOrderGoodsDTO,TmsStoreOrderGoodsEntity.class);
            storeOrderGood.setUnitType(storeOrderDTO.getUnitType());
            storeOrderGood.setSubEntrustedOrder(subOrderNo);
            storeOrderGood.setMainEntrustedOrder(mainOrderNo);
            storeOrderGoods.add(storeOrderGood);

            TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
            traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getValue());
            traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getEName());
            traceEntity.setSubEntrustedOrder(subOrderNo);
            traceEntity.setMainEntrustedOrder(mainOrderNo);
            storeOrderTraces.add(traceEntity);
        }
        baseMapper.insert(saveStoreOrders);
        storeOrderGoodsService.saveStoreOrderGoods(storeOrderGoods);
        tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        StoreProviderRelationVO storeProviderRelationVO = storeOrderDTO.getStoreProviderRelationVO();
        TmsStoreProviderRelationEntity saveBean = BeanUtil.toBean(storeProviderRelationVO, TmsStoreProviderRelationEntity.class);
        saveBean.setMainEntrustedOrder(mainOrderNo);
        storeProviderRelationService.saveStoreOrderProvider(saveBean);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreOrderById(StoreOrderDTO storeOrderDTO) {
        Long id = storeOrderDTO.getId();
        if(null == id){
            throw new CustomBusinessException("订单id不能为空");
        }
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            throw new CustomBusinessException("客户订单不存在");
        }
        // 货物信息
        List<StoreOrderGoodsDTO> storeOrderGoodsDTOS = storeOrderDTO.getStoreOrderGoods();
        if(CollUtil.isEmpty(storeOrderGoodsDTOS)){
            // 预留全部删除的逻辑
            throw new CustomBusinessException("订单信息不能为空,不支持操作");
        }
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        TmsStoreOrderEntity mainOrder = new TmsStoreOrderEntity();
        BeanUtil.copyProperties(storeOrderDTO,mainOrder);
        mainOrder.setId(storeOrder.getId());
        mainOrder.setStoreCustomerId(storeOrder.getStoreCustomerId());
        mainOrder.setMainEntrustedOrder(entrustedOrderNumber);
        baseMapper.updateById(mainOrder);
        // 删除所有子单
        baseMapper.deleteByMainEntrustedNo(entrustedOrderNumber);
        // 删除所有子单 删除所有子单货物信息
        storeOrderGoodsService.deleteByMainEntrustedOrder(entrustedOrderNumber);
        List<TmsStoreOrderEntity> saveStoreOrders = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        AtomicInteger index = new AtomicInteger(0);
        for (StoreOrderGoodsDTO storeOrderGoodsDTO : storeOrderGoodsDTOS) {
            TmsStoreOrderEntity subStoreOrder = BeanUtil.copyProperties(storeOrderDTO, TmsStoreOrderEntity.class,"id");
            String subOrderNo = ExpressOrderNoUtil.generateSubOrderNo(entrustedOrderNumber, index.incrementAndGet());
            subStoreOrder.setEntrustedOrderNumber(subOrderNo);
            subStoreOrder.setStoreCustomerId(storeOrder.getStoreCustomerId());
            subStoreOrder.setMainEntrustedOrder(entrustedOrderNumber);
            subStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.SUB.getValue());
            saveStoreOrders.add(subStoreOrder);

            TmsStoreOrderGoodsEntity storeOrderGood = BeanUtil.toBean(storeOrderGoodsDTO,TmsStoreOrderGoodsEntity.class);
            storeOrderGood.setUnitType(storeOrderDTO.getUnitType());
            storeOrderGood.setSubEntrustedOrder(subOrderNo);
            storeOrderGood.setMainEntrustedOrder(entrustedOrderNumber);
            storeOrderGoods.add(storeOrderGood);
        }
        baseMapper.insert(saveStoreOrders);
        storeOrderGoodsService.saveStoreOrderGoods(storeOrderGoods);
        // 订单服务商
        StoreProviderRelationVO storeProviderRelationVO = storeOrderDTO.getStoreProviderRelationVO();
        if(null != storeProviderRelationVO) {
            storeProviderRelationService.deleteByMainEntrustedOrder(entrustedOrderNumber);
            TmsStoreProviderRelationEntity saveBean = BeanUtil.toBean(storeProviderRelationVO, TmsStoreProviderRelationEntity.class);
            saveBean.setMainEntrustedOrder(entrustedOrderNumber);
            storeProviderRelationService.saveStoreOrderProvider(saveBean);
        }
        return true;
    }

    @Override
    public StoreOrderDetailVO getStoreOrderById(Long id) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            return null;
        }
        // 基本信息
        StoreOrderDetailVO detailVO = BeanUtil.toBean(storeOrder, StoreOrderDetailVO.class);
        String mainEntrustedNo = storeOrder.getEntrustedOrderNumber();
        // 基础费用
        TmsStoreProviderRelationEntity entity = storeProviderRelationService.getByMainEntrustedOrder(mainEntrustedNo);
        if(null != entity){
            StoreProviderRelationVO providerRelationVO = BeanUtil.toBean(entity, StoreProviderRelationVO.class);
            detailVO.setStoreProviderRelationVO(providerRelationVO);
        }
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = storeOrderGoodsService.getByMainEntrustedOrder(mainEntrustedNo);
        if(CollUtil.isNotEmpty(storeOrderGoods)){
            List<StoreOrderGoodsVO> goodsVOS = BeanUtil.copyToList(storeOrderGoods, StoreOrderGoodsVO.class);
            detailVO.setStoreOrderGoods(goodsVOS);
            // 货物单位
            StoreOrderGoodsVO storeOrderGoodsVO = goodsVOS.get(0);
            detailVO.setUnitType(storeOrderGoodsVO.getUnitType());
        }
        // 异常信息
        List<TmsStoreOrderExceptionEntity> storeOrderExceptions = tmsStoreOrderExceptionService.getStoreOrderExceptionsByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderExceptions)){
            List<TmsStoreOrderExceptionVo> exceptionVOS = BeanUtil.copyToList(storeOrderExceptions, TmsStoreOrderExceptionVo.class);
            detailVO.setExceptionList(exceptionVOS);
        }
        // 包裹赔付信息
        List<TmsStoreOrderPkgPayEntity> storeOrderPkgPays = tmsStoreOrderPkgPayService.getStoreOrderPkgPaysByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderPkgPays)){
            List<TmsStoreOrderPkgPayVo> pkgPayVOS = BeanUtil.copyToList(storeOrderPkgPays, TmsStoreOrderPkgPayVo.class);
            detailVO.setPkgPayList(pkgPayVOS);
        }
        return detailVO;
    }

    @Override
    public List<ImportStoreGoodsDTO> importStoreGoods(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            List<ImportStoreGoodsDTO> importData = new ArrayList<>();
            ExcelReader.Meta<ImportStoreGoodsDTO> meta = new ExcelReader.Meta<>();
            meta.setDomain(ImportStoreGoodsDTO.class);
            meta.setExcelStream(inputStream);
            meta.setHeadRowNumber(1);
            meta.setConsumer(importData::addAll);
            meta.setExceptionConsumer(ExcelValidatedException::new);
            ExcelReader excelReader = new ExcelReader(validator);
            excelReader.read(meta);

            boolean hasEmpty = importData.stream()
                    .anyMatch(dto -> dto.getLength() == null || dto.getWidth() == null || dto.getHeight() == null);
            if(hasEmpty){
                // TODO @国际化处理@
                throw new CustomBusinessException("货物长度/宽度/高度不能为空");
            }
            return importData;
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<ChannelPriceVO> getChannelPrice(ChannelPriceQueryDTO channelPriceQueryDTO) {
        /**
         * 1.校验数据
         * 1.查询系统服务商（启用）
         * 2.货物服务商列表和名称
         * 3.计算一票多件和核算数据
         * 4.调用小包的接口获取其中询价的数据
         */
        List<ChannelPriceVO> channelPriceVOS = new ArrayList<>();
        List<StoreOrderGoodsDTO> storeOrderGoods = channelPriceQueryDTO.getStoreOrderGoods();
        BigDecimal totalLength = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getLength)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalWidth = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getWidth)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalHeight = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getHeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalWeight = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getWeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer unitType = channelPriceQueryDTO.getUnitType();
        unitType = (unitType == null) ? UnitConvertUtil.UNIT_INTERNATIONAL : unitType;
        // 法英制度 单位换算
        if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
            totalLength = UnitConvertUtil.volumeConvert(totalLength,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWidth  = UnitConvertUtil.volumeConvert(totalWidth,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalHeight = UnitConvertUtil.volumeConvert(totalHeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWeight = UnitConvertUtil.weightConvert(totalWeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
        }
        List<TmsServiceProviderEntity> allValidChannel = serviceProviderService.getAllValid();
        if(CollUtil.isEmpty(allValidChannel)){
            return channelPriceVOS;
        }
        Map<String, TmsServiceProviderEntity> channelMap = allValidChannel
                .stream()
                .collect(Collectors.toMap(TmsServiceProviderEntity::getProviderCode, Function.identity(), (v1, v2) -> v1));
        FeeRequest feeRequest = new FeeRequest();
        // 所有报价渠道
        List<String> channelCodes = allValidChannel
                .stream()
                .filter(Objects::nonNull)
                .map(TmsServiceProviderEntity::getProviderCode)
                .filter(code -> code.startsWith("CA"))
                .distinct()
                .collect(Collectors.toList());

        List<String> channelCodesNB = allValidChannel
                .stream()
                .filter(Objects::nonNull)
                .map(TmsServiceProviderEntity::getProviderCode)
                .filter(code -> code.equals(StoreConstants.NB_SERVICE_PROVIDER))
                .distinct()
                .collect(Collectors.toList());

        // 收货人邮编
        String receiverPostalCode = channelPriceQueryDTO.getReceiverPostalCode();
        String shipperPostalCode = channelPriceQueryDTO.getShipperPostalCode();
        try {
            // NB服务商询价
            if(CollUtil.isNotEmpty(channelCodesNB)){
                BigDecimal totalVolume = totalLength.multiply(totalWidth).multiply(totalHeight).divide(StoreConstants.VOLUME_CM3_CONVERT_M3);
                String channelNB = channelCodesNB.get(0);
                TmsOrderPriceCalculationVo order = new TmsOrderPriceCalculationVo();
                order.setDestPostalCode(receiverPostalCode);
                order.setShipperPostalCode(shipperPostalCode);
                order.setTotalWeight(totalWeight);
                order.setTotalVolume(totalVolume);
                PriceCalculationRequestVo priceCalculationRequestVo = new PriceCalculationRequestVo();
                priceCalculationRequestVo.setOrders(Collections.singletonList(order));
                priceCalculationRequestVo.setProviderName(channelNB);
                PriceCalculationResultVo priceCalculationResultVo = tmsReceivableService.calculatePrice(priceCalculationRequestVo);
                if(null != priceCalculationResultVo) {
                    ChannelPriceVO channelPriceVO = new ChannelPriceVO();
                    channelPriceVO.setProviderCode(channelNB);
                    channelPriceVO.setFreightAmount(priceCalculationResultVo.getTotalPrice());
                    TmsServiceProviderEntity tmsServiceProviderEntity = channelMap.get(channelNB);
                    if (null != tmsServiceProviderEntity) {
                        channelPriceVO.setProviderName(tmsServiceProviderEntity.getProviderName());
                    }
                    channelPriceVOS.add(channelPriceVO);
                }
            }
            // packet服务商询价
            if(CollUtil.isNotEmpty(channelCodes)){
                feeRequest.setChannelCode(channelCodes);
                feeRequest.setLength(totalLength);
                feeRequest.setWidth(totalWidth);
                feeRequest.setHeight(totalHeight);
                feeRequest.setWeight(totalWeight);
                feeRequest.setPostCode(receiverPostalCode);
                feeRequest.setIso2(StoreConstants.CA);
                ResponseResult<FreightRoot> freightRootResponseResult = PostBusinessUtils.calculateFreight(feeRequest);
                if(null != freightRootResponseResult){
                    FreightRoot freightRoot = freightRootResponseResult.getData();
                    if(null != freightRoot || CollUtil.isNotEmpty(freightRoot.getData())){
                        List<FreightDataItem> data = freightRoot.getData();
                        for (FreightDataItem item : data) {
                            ChannelPriceVO channelPriceVO = new ChannelPriceVO();
                            channelPriceVO.setProviderCode(item.getChannelCode());
                            String totalFee = item.getTotalFee();
                            if(StrUtil.isNotBlank(totalFee)){
                                channelPriceVO.setFreightAmount(BigDecimal.valueOf(Double.parseDouble(totalFee)));
                            }
                            TmsServiceProviderEntity serviceProvider = channelMap.get(item.getChannelCode());
                            if(null != serviceProvider){
                                channelPriceVO.setProviderName(serviceProvider.getProviderName());
                            }
                            channelPriceVOS.add(channelPriceVO);
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("【服务商询价】,请求参数body={},操作时间time={}",feeRequest,System.currentTimeMillis(),e);
        }
        return channelPriceVOS;
    }

    @Override
    public SuggestStoreDetailVO getSuggestStore(SuggestStoreQueryDTO storeQueryDTO) {
        /**
         * 1. 验证参数
         * 2. 获取启用的门店信息，剔除经纬度为空的门店
         * 3. 计算发货地的经纬度与门店的经纬度距离,默认计算所有的门店(门店可控)
         * 4. 根据距离进行排序，最近的门店为推荐门店
         */
        SuggestStoreDetailVO suggestStoreDetailVO = new SuggestStoreDetailVO();
        List<TmsStoreEntity> allStore = storeService.getAllStoreEntity();
        if(CollUtil.isEmpty(allStore)){
            return suggestStoreDetailVO;
        }
        List<TmsStoreEntity> filterStore = allStore.stream()
                .filter(e->ObjectUtil.isNotEmpty(e.getStoreLatLng()))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(filterStore)){
            return suggestStoreDetailVO;
        }

        String shipperOrigin = storeQueryDTO.getShipperOrigin();
        String shipperAddress = storeQueryDTO.getShipperAddress();
        String shipperPostalCode = storeQueryDTO.getShipperPostalCode();
        String finalAddress = AddressUtil.normalizeAddress(shipperAddress);
        String addressDetail = finalAddress + " " + shipperPostalCode;
        try {
            String latLngByAddress = routePlanService.getLatLngByAddress(addressDetail);
            if(StrUtil.isBlank(latLngByAddress)){
                return suggestStoreDetailVO;
            }
            String[] split = latLngByAddress.split(",");
            if(split.length != 2){
                return suggestStoreDetailVO;
            }
            double lat = Double.parseDouble(split[0]);
            double lng = Double.parseDouble(split[1]);

            suggestStoreDetailVO.setShipperOrigin(shipperOrigin);
            suggestStoreDetailVO.setShipperAddress(shipperAddress);
            suggestStoreDetailVO.setShipperPostalCode(shipperPostalCode);
            suggestStoreDetailVO.setLat(lat);
            suggestStoreDetailVO.setLng(lng);
            // 根据经纬度计算距离
            List<SuggestStoreVO> suggestStores = new ArrayList<>();
            HaversineUtil.LatLng p1 = new HaversineUtil.LatLng(lat,lng);
            for (TmsStoreEntity item : filterStore) {
                String[] splitItem = item.getStoreLatLng().split(",");
                if(splitItem.length != 2){
                    continue;
                }
                double destLat = Double.parseDouble(splitItem[0]);
                double destLng = Double.parseDouble(splitItem[1]);
                HaversineUtil.LatLng p2 = new HaversineUtil.LatLng(destLat,destLng);
                double distance = HaversineUtil.distanceKm(p1, p2);

                SuggestStoreVO suggestStoreVO = new SuggestStoreVO();
                suggestStoreVO.setId(item.getId());
                suggestStoreVO.setStoreAddress(item.getStoreAddress());
                suggestStoreVO.setStoreName(item.getStoreName());
                suggestStoreVO.setStoreCode(item.getStoreCode());
                suggestStoreVO.setDistance(distance);
                suggestStoreVO.setLat(destLat);
                suggestStoreVO.setLng(destLng);
                suggestStores.add(suggestStoreVO);
            }
            // 推荐前11，第一个默认为推荐门店
            List<SuggestStoreVO> suggest = suggestStores.stream()
                    .filter(e -> ObjectUtil.isNotNull(e.getDistance()))
                    .sorted(Comparator.comparingDouble(SuggestStoreVO::getDistance))
                    .limit(11)
                    .collect(Collectors.toList());
            suggestStoreDetailVO.setSuggestStores(suggest);
        }catch (Exception e){
            logger.error("推荐门店计算异常",e);
        }
        return suggestStoreDetailVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void printScript(Long id, HttpServletResponse response) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            // TODO 国际化处理
            throw new CustomBusinessException("订单不存在,不支持操作");
        }
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        try {
            InputStream inputStream = new URL(StoreConstants.TemplateConstants.OrderRedemptionVoucher).openStream();
            PDDocument document = PDDocument.load(inputStream);
            PDPage page = document.getPage(StoreConstants.ZERO);
            // 2. 获取表单
            PDAcroForm acroForm = document.getDocumentCatalog().getAcroForm();
            // 3.填充文本
            PDField orderField = acroForm.getField(StoreConstants.TemplateVariableConstants.ENTRUSTED_ORDER_NUMBER);
            if (orderField != null) {
                orderField.setValue(entrustedOrderNumber);
            }
            BufferedImage qrImage = GenerateUtils.generateQRCode(entrustedOrderNumber, 350, 300);
            BufferedImage barImage = GenerateUtils.generateBarCode(entrustedOrderNumber, 350, 100);
            // 3. 获取图片字段的位置信息
            GenerateUtils.insertImageToField(document, page, acroForm, StoreConstants.TemplateVariableConstants.QR_ORDER_IMAGE, qrImage);
            GenerateUtils.insertImageToField(document, page, acroForm, StoreConstants.TemplateVariableConstants.BAR_ORDER_IMAGE, barImage);
            acroForm.flatten();
            // 5. 输出PDF
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=\"RedemptionVoucher.pdf\"");
            document.save(response.getOutputStream());
            document.close();
        }catch (Exception e){
            logger.error("[快递业务订单][打印凭证]",e);
            // TODO 国际化处理
            throw new CustomBusinessException("打印凭证失败");
        }
        TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
        updateEntity.setId(storeOrder.getId());
        updateEntity.setPrintTime(LocalDateTime.now());
        updateEntity.setOrderStatus(StoreEnums.StoreOrder.PrintStatus.PRINTED.getValue());
        baseMapper.updateById(updateEntity);
    }

    @Override
    public IPage<StoreOrderVO> getTmsStoreOrderStorePage(StoreOrderQueryDTO storeOrderQueryDTO) {
        Long userId = SecurityUtils.getUser().getId();
        // 获取门店ID
        TmsStoreUserEntity storeUser = storeUserService.getOne(Wrappers.<TmsStoreUserEntity>lambdaQuery().eq(TmsStoreUserEntity::getUserId, userId));
        if (storeUser == null) {
            return new Page<>();
        }
        Long storeId = storeUser.getStoreId();
        if (storeId == null) {
            return new Page<>();
        }
        Page<TmsStoreOrderEntity> page = new Page<>(storeOrderQueryDTO.getCurrent(),storeOrderQueryDTO.getSize());
        // 构建分页条件
        return this.baseMapper.getTmsStoreOrderStorePage(page, storeOrderQueryDTO, storeId);
    }

    @Override
    public IPage<StoreOrderVO> getTmsStoreOrderAdminPage(StoreOrderQueryDTO storeOrderQueryDTO) {
        Page<TmsStoreOrderEntity> page = new Page<>(storeOrderQueryDTO.getCurrent(),storeOrderQueryDTO.getSize());
        return this.baseMapper.getTmsStoreOrderAdminPage(page, storeOrderQueryDTO);
    }


    // 推送订单到小包系统 - 返回外部系统跟踪单号
    private String pushOrderToPacket(TmsStoreOrderEntity storeOrder,TmsStoreOrderGoodsEntity storeOrderGood,String providerCode,String entrustedOrderNumber){
        // 是否购买保险
        Integer insured = storeOrder.getInsuranceSign();
        // 件数
        Integer pieces = StoreConstants.ONE;
        BigDecimal totalWeight = storeOrderGood.getWeight();
        BigDecimal totalLength = storeOrderGood.getLength();
        BigDecimal totalWidth = storeOrderGood.getWidth();
        BigDecimal totalHeight = storeOrderGood.getHeight();
        Integer unitType = storeOrderGood.getUnitType();
        unitType = (unitType == null) ? UnitConvertUtil.UNIT_INTERNATIONAL : unitType;
        // 法英制度 单位换算
        if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
            totalLength = UnitConvertUtil.volumeConvert(totalLength,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWidth  = UnitConvertUtil.volumeConvert(totalWidth,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalHeight = UnitConvertUtil.volumeConvert(totalHeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWeight = UnitConvertUtil.weightConvert(totalWeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
        }

        // 收件人必填信息
        String consigneeName = storeOrder.getReceiverName();
        String receiverDest = storeOrder.getReceiverDest();
        String[] splitDest = receiverDest.split(StoreConstants.ADDRESS_SPLIT_SIGN);
        if(splitDest.length != 3){
            return null;
        }
        String consigneeCountryCode = splitDest[0];
        String consigneeProvince = splitDest[1];
        String consigneeCity = splitDest[2];
        String consigneeAddress = storeOrder.getReceiverAddress();
        String consigneePostcode = storeOrder.getReceiverPostalCode();
        String consigneePhone = storeOrder.getReceiverPhone();

        // 发货人信息
        String shipperName = storeOrder.getShipperName();
        String shipperOrigin = storeOrder.getShipperOrigin();
        String[] splitOrigin = shipperOrigin.split(StoreConstants.ADDRESS_SPLIT_SIGN);
        if(splitOrigin.length != 3){
            return null;
        }
        String shipperCountryCode = splitOrigin[0];
        String shipperProvince = splitOrigin[1];
        String shipperCity = splitOrigin[2];
        String shipperAddress = storeOrder.getReceiverAddress();
        String shipperPostcode = storeOrder.getReceiverPostalCode();
        String shipperPhone = storeOrder.getReceiverPhone();

        // 申报明细 - 固定写死
        ApiOrderItemVO apiOrderItemVO = new ApiOrderItemVO();
        apiOrderItemVO.setEname(StoreConstants.OrderItemConstants.ENAME);
        apiOrderItemVO.setCname(StoreConstants.OrderItemConstants.CNAME);
        apiOrderItemVO.setSku(StoreConstants.OrderItemConstants.SKU);
        apiOrderItemVO.setPrice(StoreConstants.OrderItemConstants.PRICE);
        apiOrderItemVO.setWeight(totalWeight);
        apiOrderItemVO.setQuantity(StoreConstants.OrderItemConstants.QUANTITY);

        // 构建材积参数 - 考虑核算一个 -适配小包的一票一件
        ApiOrderVolumeVO apiOrderVolumeVO = new ApiOrderVolumeVO();
        apiOrderVolumeVO.setLength(totalLength);
        apiOrderVolumeVO.setWidth(totalWidth);
        apiOrderVolumeVO.setHeight(totalHeight);
        apiOrderVolumeVO.setQuantity(pieces);
        apiOrderVolumeVO.setRweight(totalWeight);

        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setChannelCode(providerCode);
        orderRequest.setReferenceNo(entrustedOrderNumber);
        orderRequest.setProductType(StoreConstants.OrderItemConstants.PRODUCT_TYPE);
        orderRequest.setPweight(totalWeight);
        orderRequest.setPieces(pieces);
        orderRequest.setInsured(insured);
        orderRequest.setConsigneeName(consigneeName);
        orderRequest.setConsigneeCountryCode(StoreConstants.CA);
        // orderRequest.setConsigneeCountryCode(consigneeCountryCode);
        orderRequest.setConsigneeProvince(consigneeProvince);
        orderRequest.setConsigneeCity(consigneeCity);
        orderRequest.setConsigneeAddress(consigneeAddress);
        orderRequest.setConsigneePostcode(consigneePostcode);
        orderRequest.setConsigneePhone(consigneePhone);
        orderRequest.setShipperName(shipperName);
        orderRequest.setShipperCountryCode(StoreConstants.CA);
        // orderRequest.setShipperCountryCode(shipperCountryCode);
        orderRequest.setShipperProvince(shipperProvince);
        orderRequest.setShipperCity(shipperCity);
        orderRequest.setShipperAddress(shipperAddress);
        orderRequest.setShipperPostcode(shipperPostcode);
        orderRequest.setShipperPhone(shipperPhone);

        orderRequest.setApiOrderItemList(Collections.singletonList(apiOrderItemVO));
        orderRequest.setApiOrderVolumeList(Collections.singletonList(apiOrderVolumeVO));
        try {
            ResponseResult<PackageOrderRoot> request = PostBusinessUtils.createRequest(orderRequest);
            int code = request.getCode();
            if(StoreConstants.ZERO == code){
                return null;
            }
            return request.getData().getPackageOrderDataItem().getTrackingNo();
        }catch (Exception e){
            logger.error("[小包系统][推送订单失败],params={}",orderRequest,e);
            throw new CustomBusinessException("推送小包订单失败");
        }
    }

    // 推送订单到NB系统 - 返回NB系统主单号作为跟踪单号
    private ZdjApiVo pushOrderToNB(List<TmsStoreOrderGoodsEntity> storeOrderGoods,TmsStoreOrderEntity storeOrder,String mainOrderNumber){
        TmsCustomerEntity tmsCustomerEntity = customerService.getCustomerByCustomerName(StoreConstants.NB_EXPRESS_CUSTOMER_NAME);
        if(null == tmsCustomerEntity){
            throw new CustomBusinessException("NB系统快递业务客户不存在,推送订单失败");
        }
        /**
         * 产品提 写死
         * 运输类型：零担运输
         * 订单类型：包裹
         * 货物类型：普通货品
         * 业务模式：中大件
         * 收货方式：根据快递所选则的方式
         */
        // NB系统快递业务客户id -委托客户
        Long nbCustomerId = tmsCustomerEntity.getId();

        TmsCustomerOrderEntity tmsCustomerOrder = new TmsCustomerOrderEntity();
        tmsCustomerOrder.setCustomerId(nbCustomerId);
        tmsCustomerOrder.setCustomerOrderNumber(mainOrderNumber);
        // 运输类型：零担运输
        tmsCustomerOrder.setTransportType(StoreConstants.TWO);
        // 货物类型：普通货品
        tmsCustomerOrder.setCargoType(StoreConstants.ONE);
        // 订单类型：包裹
        tmsCustomerOrder.setOrderType(StoreConstants.TWO);
        // 业务模式: 中大件
        tmsCustomerOrder.setBusinessModel(StoreConstants.TWO);
        // 发货方式
        Integer sendType = storeOrder.getSendType();
        if(StoreEnums.StoreOrder.SendType.DELIVERY_TO_STORE.getValue().equals(sendType)){
            tmsCustomerOrder.setReceiveType(StoreConstants.ONE);
        }else if(StoreEnums.StoreOrder.SendType.COLLECTING_FROM_NB.getValue().equals(sendType)){
            tmsCustomerOrder.setReceiveType(StoreConstants.TWO);
        }
        // 发货信息
        String shipperName = storeOrder.getShipperName();
        String shipperPhone = storeOrder.getShipperPhone();
        // 始发地
        String shipperOrigin = storeOrder.getShipperOrigin();
        String shipperPostalCode = storeOrder.getShipperPostalCode();
        String shipperAddress = storeOrder.getShipperAddress();
        tmsCustomerOrder.setShipperName(shipperName);
        tmsCustomerOrder.setShipperPhone(shipperPhone);
        tmsCustomerOrder.setShipperAddress(shipperAddress);
        tmsCustomerOrder.setShipperPostalCode(shipperPostalCode);
        tmsCustomerOrder.setOrigin(shipperOrigin);

        // 收件信息
        String receiverName = storeOrder.getReceiverName();
        String receiverPhone = storeOrder.getReceiverPhone();
        String receiverDest = storeOrder.getReceiverDest();
        String receiverPostalCode = storeOrder.getReceiverPostalCode();
        String receiverAddress = storeOrder.getReceiverAddress();
        tmsCustomerOrder.setReceiverName(receiverName);
        tmsCustomerOrder.setReceiverPhone(receiverPhone);
        tmsCustomerOrder.setDestination(receiverDest);
        tmsCustomerOrder.setDestAddress(receiverAddress);
        tmsCustomerOrder.setDestPostalCode(receiverPostalCode);
        // 备注
        tmsCustomerOrder.setRemark(storeOrder.getRemark());
        // 单位
        TmsStoreOrderGoodsEntity tmsStoreOrderGoodsEntity = storeOrderGoods.get(0);
        Integer unitType = tmsStoreOrderGoodsEntity.getUnitType();
        tmsCustomerOrder.setUnits(unitType);

        List<TmsCargoInfoEntity> cargoInfoEntityList = new ArrayList<>(storeOrderGoods.size());

        for (TmsStoreOrderGoodsEntity storeOrderGood : storeOrderGoods) {
            BigDecimal length = storeOrderGood.getLength();
            BigDecimal width = storeOrderGood.getWidth();
            BigDecimal height = storeOrderGood.getHeight();
            BigDecimal weight = storeOrderGood.getWeight();
            String remark = storeOrderGood.getRemark();
            String goodInfo = storeOrderGood.getGoodInfo();

            TmsCargoInfoEntity cargoInfoEntity = new TmsCargoInfoEntity();
            cargoInfoEntity.setLength(length);
            cargoInfoEntity.setWidth(width);
            cargoInfoEntity.setHeight(height);
            cargoInfoEntity.setWeight(weight);
            cargoInfoEntity.setRemark(remark);
            cargoInfoEntity.setCargoDescription(goodInfo);
            // 箱号比较关键,映射子单
            cargoInfoEntity.setBoxNum(storeOrderGood.getSubEntrustedOrder());
            cargoInfoEntityList.add(cargoInfoEntity);
        }
        tmsCustomerOrder.setCargoInfoEntityList(cargoInfoEntityList);
        try {
            R zdjOrder = customerOrderService.createZDJOrder(tmsCustomerOrder, Boolean.TRUE);
            if(zdjOrder.isOk()){
                Object data = zdjOrder.getData();
                if(ObjectUtil.isNotNull(data)){
                    String json = mapper.writeValueAsString(data);
                    return mapper.readValue(json, ZdjApiVo.class);
                }
            }else{
                throw new CustomBusinessException("NB系统创建订单失败,推送订单失败");
            }
        }catch (Exception e){
           logger.error("[快递端业务下单][退单NB]推送失败,params={}",tmsCustomerOrder,e);
        }
        return null;
    }

    @Override
    public R importCargoInfo(MultipartFile file) {
        List<String> errorMessages = new ArrayList<>();
        List<TmsImportStoreDTO> dataList = new ArrayList<>();

        try {
            EasyExcel.read(file.getInputStream(), TmsImportStoreDTO.class, new PageReadListener<TmsImportStoreDTO>((List<TmsImportStoreDTO> dataBatch) -> {
                int rowIndexStart = 0; // 批次起始行号
                for (TmsImportStoreDTO data : dataBatch) {
                    int batchRowIndex = ++rowIndexStart; // 当前行号
                    // 校验字段，若为空则加入 errorMessages
                    validateField(data.getShipperOrigin(), "始发地", batchRowIndex, errorMessages);
                    validateField(data.getReceiverDest(), "目的地", batchRowIndex, errorMessages);
                    validateField(data.getShipperName(), "发件联系人", batchRowIndex, errorMessages);
                    validateField(data.getShipperPhone(), "发件人电话", batchRowIndex, errorMessages);
                    validateField(data.getShipperAddress(), "发件详细地址", batchRowIndex, errorMessages);
                    validateField(data.getShipperPostalCode(), "发件邮编", batchRowIndex, errorMessages);
                    validateField(data.getReceiverName(), "收件联系人", batchRowIndex, errorMessages);
                    validateField(data.getReceiverPhone(), "收件人电话", batchRowIndex, errorMessages);
                    validateField(data.getReceiverAddress(), "收件详细地址", batchRowIndex, errorMessages);
                    validateField(data.getReceiverPostalCode(), "收件邮编", batchRowIndex, errorMessages);
                    validateField(data.getLength().toString(), "长", batchRowIndex, errorMessages);
                    validateField(data.getWidth().toString(), "宽", batchRowIndex, errorMessages);
                    validateField(data.getHeight().toString(), "高", batchRowIndex, errorMessages);
                    validateField(data.getWeight().toString(), "重量", batchRowIndex, errorMessages);

                    data.convertSignFlags();

                    // 处理数据
                    dataList.add(data);
                }
            })).sheet().doRead();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 统一抛出错误信息
        if (!errorMessages.isEmpty()) {
            return R.failed("导入出现错误：" + String.join("; ", errorMessages));
        }

        return R.ok(dataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(Long id) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(storeOrder == null){
            throw new CustomBusinessException("订单不存在，不支持操作");
        }
        Integer orderStatus = storeOrder.getOrderStatus();
        Integer writeOffFlag = storeOrder.getWriteOffFlag();

        if(!StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getValue().equals(orderStatus)
        && !StoreEnums.StoreOrder.WriteOffFlag.UnWriteOff.getValue().equals(writeOffFlag)){
            throw new CustomBusinessException("订单状态非待发货且已核销状态,不支持取消订单");
        }
        TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
        updateEntity.setId(id);
        updateEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.CANCEL.getValue());
        baseMapper.updateById(updateEntity);
        String mainEntrustedNo = updateEntity.getMainEntrustedOrder();
        baseMapper.updateOrderStatusByMainEntrustedNo(mainEntrustedNo,StoreEnums.StoreOrder.OrderStatus.CANCEL.getValue());
        // 轨迹跟踪
        List<TmsStoreOrderEntity> subStoreOrder = baseMapper.getSubOrderByMainEntrustedOrder(mainEntrustedNo);
        if(CollUtil.isNotEmpty(subStoreOrder)){
            List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(subStoreOrder.size());
            for (TmsStoreOrderEntity item : subStoreOrder) {
                TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.CANCEL.getValue());
                traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.CANCEL.getEName());
                traceEntity.setSubEntrustedOrder(item.getEntrustedOrderNumber());
                traceEntity.setMainEntrustedOrder(item.getMainEntrustedOrder());
                storeOrderTraces.add(traceEntity);
            }
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean writeOffOrder(Long id) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            throw new CustomBusinessException("订单不存在,不支持操作");
        }
        id = storeOrder.getId();
        String mainEntrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        BigDecimal freightAmount = storeOrder.getTotalFreightAmount();
        TmsStoreBalanceEntity storeBalance = storeBalanceService.getByStoreCustomerId(storeOrder.getStoreCustomerId());
        if(null == storeBalance){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额异常,不支持核销");
        }
        BigDecimal amount = storeBalance.getAmount();
        if(freightAmount.compareTo(amount) > 0){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额不足,请前往充值");
        }
        storeBalanceService.executeOrderDeduction(storeBalance,freightAmount,mainEntrustedOrderNumber);
        // 获取所有货物信息
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = storeOrderGoodsService.getByMainEntrustedOrder(mainEntrustedOrderNumber);
        TmsStoreProviderRelationEntity storeProviderRelationVO = storeProviderRelationService.getByMainEntrustedOrder(mainEntrustedOrderNumber);
        String providerCode = storeProviderRelationVO.getProviderCode();
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        LocalDateTime pushTime = LocalDateTime.now();
        if(StoreConstants.NB_SERVICE_PROVIDER.equals(providerCode)){
            // 推送NB订单
            ZdjApiVo zdjApiVo = pushOrderToNB(storeOrderGoods, storeOrder, mainEntrustedOrderNumber);
            if(null == zdjApiVo){
                throw new CustomBusinessException("推送NB系统订单失败,订单核销失败");
            }
            // 主单的跟踪单号 保持一致性
            String trackingNo = zdjApiVo.getTrackingNo();
            TmsStoreOrderEntity mainStoreOrder = new TmsStoreOrderEntity();
            mainStoreOrder.setId(id);
            mainStoreOrder.setExternalOrderNumber(trackingNo);
            mainStoreOrder.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
            mainStoreOrder.setPushTime(pushTime);
            mainStoreOrder.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
            mainStoreOrder.setExternalType(StoreEnums.StoreOrder.ExternalType.NB.getValue());
            baseMapper.updateById(mainStoreOrder);

            Map<String, TmsStoreOrderEntity> subStoreOrderMap = baseMapper.getSubOrderMapByMainEntrustedOrder(mainEntrustedOrderNumber);
            HashMap<String, String> subOrderNosMap = zdjApiVo.getSubOrderNos();
            List<TmsStoreOrderEntity> subStoreOrderUpdate = new ArrayList<>(subStoreOrderMap.size());
            List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(subStoreOrderMap.size());
            for (Map.Entry<String, TmsStoreOrderEntity> entityEntry : subStoreOrderMap.entrySet()) {
                String subEntrustedOrderNumber = entityEntry.getKey();
                TmsStoreOrderEntity subStoreOrderEntity = entityEntry.getValue();
                String subTrackingNo = subOrderNosMap.get(subEntrustedOrderNumber);
                if(StrUtil.isBlank(subTrackingNo)){

                    TmsStoreOrderEntity subStoreOrder = new TmsStoreOrderEntity();
                    subStoreOrder.setId(subStoreOrderEntity.getId());
                    subStoreOrder.setExternalOrderNumber(subTrackingNo);
                    subStoreOrder.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                    mainStoreOrder.setPushTime(pushTime);
                    subStoreOrder.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
                    subStoreOrder.setExternalType(StoreEnums.StoreOrder.ExternalType.NB.getValue());
                    subStoreOrderUpdate.add(subStoreOrder);

                    TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                    traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                    traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getEName());
                    traceEntity.setSubEntrustedOrder(subStoreOrderEntity.getEntrustedOrderNumber());
                    traceEntity.setMainEntrustedOrder(subStoreOrderEntity.getMainEntrustedOrder());
                    storeOrderTraces.add(traceEntity);
                }
            }
            baseMapper.updateById(subStoreOrderUpdate);
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        }else{
            Map<String, TmsStoreOrderEntity> subStoreOrderMap = baseMapper.getSubOrderMapByMainEntrustedOrder(entrustedOrderNumber);
            Map<String, TmsStoreOrderGoodsEntity> mapByMainEntrustedOrder = storeOrderGoodsService.getMapByMainEntrustedOrder(entrustedOrderNumber);
            if(null == subStoreOrderMap || null == mapByMainEntrustedOrder){
                throw new CustomBusinessException("订单信息异常,订单核销失败");
            }
            List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(subStoreOrderMap.size());
            for (Map.Entry<String, TmsStoreOrderEntity> entityEntry : subStoreOrderMap.entrySet()) {
                TmsStoreOrderEntity storeOrderEntity = entityEntry.getValue();
                TmsStoreOrderGoodsEntity tmsStoreOrderGoodsEntity = mapByMainEntrustedOrder.get(storeOrderEntity.getEntrustedOrderNumber());
                if(null == tmsStoreOrderGoodsEntity){
                    continue;
                }
                try {
                    String trackingNo = pushOrderToPacket(storeOrderEntity,tmsStoreOrderGoodsEntity,providerCode,entrustedOrderNumber);
                    if(StrUtil.isBlank(trackingNo)){
                        throw new CustomBusinessException("推送小包系统订单失败,订单核销失败");
                    }
                    TmsStoreOrderEntity updateBean = new TmsStoreOrderEntity();
                    updateBean.setId(storeOrderEntity.getId());
                    updateBean.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                    updateBean.setPushTime(pushTime);
                    updateBean.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
                    updateBean.setExternalType(StoreEnums.StoreOrder.ExternalType.PACKET.getValue());
                    updateBean.setExternalOrderNumber(trackingNo);
                    baseMapper.updateById(updateBean);

                    TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                    traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                    traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getEName());
                    traceEntity.setSubEntrustedOrder(storeOrderEntity.getEntrustedOrderNumber());
                    traceEntity.setMainEntrustedOrder(storeOrderEntity.getMainEntrustedOrder());
                    storeOrderTraces.add(traceEntity);
                }catch (Exception e){
                    // TODO 如果出现异常,需要回滚所有推送订单
                    throw new CustomBusinessException("推送小包系统订单失败,订单核销失败");
                }
            }
            TmsStoreOrderEntity mainStoreOrder = new TmsStoreOrderEntity();
            mainStoreOrder.setId(id);
            mainStoreOrder.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
            mainStoreOrder.setPushTime(pushTime);
            mainStoreOrder.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
            mainStoreOrder.setExternalType(StoreEnums.StoreOrder.ExternalType.PACKET.getValue());
            baseMapper.updateById(mainStoreOrder);
            // 门店已经取货
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        }
        return true;
    }

    @Override
    public List<TmsStoreOrderEntity> selectSubStoreOrderByPushTime(List<Integer> orderStatus, LocalDateTime pushTime) {
        if(CollUtil.isEmpty(orderStatus) || null == pushTime){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(TmsStoreOrderEntity::getMainEntrustedOrder);
        queryWrapper.isNotNull(TmsStoreOrderEntity::getExternalOrderNumber);
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag,StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        queryWrapper.in(TmsStoreOrderEntity::getOrderStatus,orderStatus);
        queryWrapper.ge(TmsStoreOrderEntity::getPushTime,pushTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatusByEntrustedOrderNumber(String entrustedOrderNumber,Integer newOrderStatus) {
        if(StrUtil.isBlank(entrustedOrderNumber)){
            return;
        }
        LambdaUpdateWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getEntrustedOrderNumber,entrustedOrderNumber);
        queryWrapper.set(TmsStoreOrderEntity::getOrderStatus,newOrderStatus);
        baseMapper.update(queryWrapper);
    }

    @Override
    public ChannelPriceVO getImportOrderChannelPrice(List<ChannelPriceQueryDTO> channelPriceQueryDTOList, String channelCode) {
        if (CollUtil.isEmpty(channelPriceQueryDTOList)) {
            return new ChannelPriceVO();
        }

        // 汇总结果
        ChannelPriceVO totalResult = new ChannelPriceVO();
        totalResult.setProviderCode(channelCode);
        BigDecimal totalFreightAmount = BigDecimal.ZERO;

        // 按收发地邮编分组，key格式：发货邮编-收货邮编
        Map<String, List<ChannelPriceQueryDTO>> groupedOrders = channelPriceQueryDTOList.stream()
                .filter(dto -> dto != null && StrUtil.isNotBlank(dto.getShipperPostalCode()) && StrUtil.isNotBlank(dto.getReceiverPostalCode()))
                .collect(Collectors.groupingBy(dto -> dto.getShipperPostalCode() + "-" + dto.getReceiverPostalCode()));

        // 对每个分组进行询价
        for (Map.Entry<String, List<ChannelPriceQueryDTO>> entry : groupedOrders.entrySet()) {
            try {
                String[] postalCodes = entry.getKey().split("-");
                String shipperPostalCode = postalCodes[0];
                String receiverPostalCode = postalCodes[1];
                List<ChannelPriceQueryDTO> sameRouteOrders = entry.getValue();

                // 汇总该分组内所有订单的货物信息
                BigDecimal totalLength = BigDecimal.ZERO;
                BigDecimal totalWidth = BigDecimal.ZERO;
                BigDecimal totalHeight = BigDecimal.ZERO;
                BigDecimal totalWeight = BigDecimal.ZERO;

                for (ChannelPriceQueryDTO orderDTO : sameRouteOrders) {
                    List<StoreOrderGoodsDTO> storeOrderGoods = orderDTO.getStoreOrderGoods();
                    if (CollUtil.isEmpty(storeOrderGoods)) {
                        continue;
                    }

                    // 计算该订单的尺寸和重量
                    BigDecimal orderLength = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getLength)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderWidth = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getWidth)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderHeight = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getHeight)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderWeight = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getWeight)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 累加到分组总量
                    totalLength = totalLength.add(orderLength);
                    totalWidth = totalWidth.add(orderWidth);
                    totalHeight = totalHeight.add(orderHeight);
                    totalWeight = totalWeight.add(orderWeight);
                }

                // 对该分组进行一次询价
                ChannelPriceVO groupResult = calculatePrice(
                    totalLength, totalWidth, totalHeight, totalWeight,
                    receiverPostalCode, shipperPostalCode, channelCode
                );

                // 汇总价格
                if (groupResult != null && groupResult.getFreightAmount() != null) {
                    totalFreightAmount = totalFreightAmount.add(groupResult.getFreightAmount());
                }

                logger.info("分组询价完成，路线: {}，订单数量: {}，分组运费: {}",
                    entry.getKey(), sameRouteOrders.size(),
                    groupResult != null ? groupResult.getFreightAmount() : "询价失败");

            } catch (Exception e) {
                logger.error("分组询价异常，路线: {}, 渠道: {}", entry.getKey(), channelCode, e);
            }
        }

        totalResult.setFreightAmount(totalFreightAmount);
        logger.info("批量询价完成，渠道: {}，总分组数: {}，总运费: {}",
            channelCode, groupedOrders.size(), totalFreightAmount);

        return totalResult;
    }

    @Override
    public StoreOrderDetailVO getStoreOrderByOrderNumber(String orderNumber) {
        if (StrUtil.isBlank(orderNumber) || orderNumber.length() < 13){
            return null;
        }
        // 判断子单还是主单
        if(orderNumber.length() != 13){
            orderNumber = orderNumber.substring(0, 13);
        }
        TmsStoreOrderEntity storeOrder = baseMapper.selectOne(Wrappers.<TmsStoreOrderEntity>lambdaQuery()
                .eq(TmsStoreOrderEntity::getEntrustedOrderNumber, orderNumber));
        if(storeOrder == null){
            return null;
        }
        // 基本信息
        StoreOrderDetailVO detailVO = BeanUtil.toBean(storeOrder, StoreOrderDetailVO.class);
        // 基础费用
        TmsStoreProviderRelationEntity entity = storeProviderRelationService.getByMainEntrustedOrder(orderNumber);
        if(null != entity){
            StoreProviderRelationVO providerRelationVO = BeanUtil.toBean(entity, StoreProviderRelationVO.class);
            detailVO.setStoreProviderRelationVO(providerRelationVO);
        }
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = storeOrderGoodsService.getByMainEntrustedOrder(orderNumber);
        if(CollUtil.isNotEmpty(storeOrderGoods)){
            List<StoreOrderGoodsVO> goodsVOS = BeanUtil.copyToList(storeOrderGoods, StoreOrderGoodsVO.class);
            detailVO.setStoreOrderGoods(goodsVOS);
        }
        // 异常信息
        List<TmsStoreOrderExceptionEntity> storeOrderExceptions = tmsStoreOrderExceptionService.getStoreOrderExceptionsByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderExceptions)){
            List<TmsStoreOrderExceptionVo> exceptionVOS = BeanUtil.copyToList(storeOrderExceptions, TmsStoreOrderExceptionVo.class);
            detailVO.setExceptionList(exceptionVOS);
        }
        // 包裹赔付信息
        List<TmsStoreOrderPkgPayEntity> storeOrderPkgPays = tmsStoreOrderPkgPayService.getStoreOrderPkgPaysByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderPkgPays)){
            List<TmsStoreOrderPkgPayVo> pkgPayVOS = BeanUtil.copyToList(storeOrderPkgPays, TmsStoreOrderPkgPayVo.class);
            detailVO.setPkgPayList(pkgPayVOS);
        }
        // 客户信息
        StoreCustomerVO customerInfo = storeCustomerService.getStoreCustomerByStoreId(storeOrder.getStoreCustomerId());
        if (customerInfo != null){
            detailVO.setCustomerInfo(customerInfo);
        }
        return detailVO;
    }

    /**
     * 导入订单询价
     */
    public ChannelPriceVO calculatePrice(BigDecimal totalLength, BigDecimal totalWidth, BigDecimal totalHeight,
                                         BigDecimal totalWeight, String receiverPostalCode, String shipperPostalCode,
                                         String channelCode) {

        ChannelPriceVO channelPriceVO = new ChannelPriceVO();
        channelPriceVO.setProviderCode(channelCode);

        try {
            // NB服务商询价
            if (StoreConstants.NB_SERVICE_PROVIDER.equals(channelCode)) {
                BigDecimal totalVolume = totalLength.multiply(totalWidth).multiply(totalHeight)
                        .divide(StoreConstants.VOLUME_CM3_CONVERT_M3);

                TmsOrderPriceCalculationVo order = new TmsOrderPriceCalculationVo();
                order.setDestPostalCode(receiverPostalCode);
                order.setShipperPostalCode(shipperPostalCode);
                order.setTotalWeight(totalWeight);
                order.setTotalVolume(totalVolume);

                PriceCalculationRequestVo priceCalculationRequestVo = new PriceCalculationRequestVo();
                priceCalculationRequestVo.setOrders(Collections.singletonList(order));
                priceCalculationRequestVo.setProviderName(channelCode);

                PriceCalculationResultVo priceCalculationResultVo = tmsReceivableService.calculatePrice(priceCalculationRequestVo);
                if (priceCalculationResultVo != null) {
                    channelPriceVO.setFreightAmount(priceCalculationResultVo.getTotalPrice());
                }
            }
            // packet服务商询价（CA开头的渠道）
            else if (channelCode.startsWith("CA")) {
                FeeRequest feeRequest = new FeeRequest();
                feeRequest.setChannelCode(Collections.singletonList(channelCode));
                feeRequest.setLength(totalLength);
                feeRequest.setWidth(totalWidth);
                feeRequest.setHeight(totalHeight);
                feeRequest.setWeight(totalWeight);
                feeRequest.setPostCode(receiverPostalCode);
                feeRequest.setIso2(StoreConstants.CA);

                ResponseResult<FreightRoot> freightRootResponseResult = PostBusinessUtils.calculateFreight(feeRequest);
                if (freightRootResponseResult != null && freightRootResponseResult.getData() != null) {
                    FreightRoot freightRoot = freightRootResponseResult.getData();
                    if (freightRoot != null && CollUtil.isNotEmpty(freightRoot.getData())) {
                        List<FreightDataItem> data = freightRoot.getData();
                        for (FreightDataItem item : data) {
                            if (channelCode.equals(item.getChannelCode())) {
                                String totalFee = item.getTotalFee();
                                if (StrUtil.isNotBlank(totalFee)) {
                                    channelPriceVO.setFreightAmount(BigDecimal.valueOf(Double.parseDouble(totalFee)));
                                }
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("询价异常，渠道: {}", channelCode, e);
        }

        return channelPriceVO;
    }

    /**
     * 校验单个字段是否为空
     */
    private void validateField(String fieldValue, String fieldName, int rowIndex, List<String> errorMessages) {
        if (fieldValue == null || fieldValue.trim().isEmpty()) {
            errorMessages.add("第【" + rowIndex + "】行：" + fieldName + "不能为空");
        }
    }


}
