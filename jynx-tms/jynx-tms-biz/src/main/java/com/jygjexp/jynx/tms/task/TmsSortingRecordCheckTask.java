package com.jygjexp.jynx.tms.task;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.dto.DeliveryRecordGroupDTO;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsReceivableEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingRecordEntity;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.service.TmsReceivableService;
import com.jygjexp.jynx.tms.service.TmsSortingRecordService;
import com.jygjexp.jynx.tms.vo.MasterOrderPriceCalculationVo;
import com.jygjexp.jynx.tms.vo.PriceCalculationYingShouRequestVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class TmsSortingRecordCheckTask {
    private static final Logger logger = LoggerFactory.getLogger(TmsSortingRecordCheckTask.class);
    private final TmsCustomerService customerService;
    private final TmsSortingRecordService sortingRecordService;
    private final TmsCustomerOrderService customerOrderService;
    private final TmsReceivableService receivableService;

    @SneakyThrows
    @XxlJob("tmsSortingRecordCheckTask")
    public void executeTask(){
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        String jobParam = XxlJobHelper.getJobParam();
        if(StrUtil.isNotBlank(jobParam)){
            startTime = LocalDate.parse(jobParam.trim()).atStartOfDay();
            endTime = LocalDate.parse(jobParam.trim()).atTime(LocalTime.MAX);
        }else{
            Date yesterday = DateUtil.yesterday();
            startTime = DateUtil.beginOfDay(yesterday).toLocalDateTime();
            endTime = DateUtil.endOfDay(yesterday).toLocalDateTime();
        }
        if(null == startTime || null == endTime){
            return;
        }
        if(logger.isInfoEnabled()){
            logger.info("[核算价格][包裹扫描入仓] 开始执行,startTime={},endTime={}", startTime, endTime);
        }
        List<DeliveryRecordGroupDTO> deliveryRecords = sortingRecordService.getDeliveryRecordByScanTime(startTime, endTime);
        List<MasterOrderPriceCalculationVo> masterOrderPriceCalculationVos = new ArrayList<>();
        if(CollUtil.isNotEmpty(deliveryRecords)){
            for (DeliveryRecordGroupDTO deliveryRecord : deliveryRecords) {
                // 主单维度
                String orderNo = deliveryRecord.getOrderNo();
                if(StrUtil.isBlank(orderNo)){
                    continue;
                }
                TmsReceivableEntity receivableEntity = receivableService.getReceivableByEntrustedOrderNo(orderNo);
                if(null != receivableEntity){
                    continue;
                }
                List<TmsSortingRecordEntity> deliveryRecordByMain = sortingRecordService.getDeliveryRecordByMain(orderNo);
                List<TmsCustomerOrderEntity> customerOrderByMain = customerOrderService.getCustomerOrderByMain(orderNo);

                if(CollUtil.isEmpty(deliveryRecordByMain) || CollUtil.isEmpty(customerOrderByMain)){
                    continue;
                }
                TmsCustomerOrderEntity tmsCustomerOrder = customerOrderByMain.get(0);
                Long customerId = tmsCustomerOrder.getCustomerId();
                TmsCustomerEntity customer = customerService.getCustomerById(customerId);
                if(null == customer){
                    continue;
                }
                Integer volumeCoefficient = customer.getVolumeCoefficient();

                List<String> sortOrderNoSub = deliveryRecordByMain
                        .stream()
                        .map(TmsSortingRecordEntity::getOrderNo)
                        .distinct()
                        .collect(Collectors.toList());
                List<String> customerOrderNoSub = customerOrderByMain
                        .stream()
                        .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                        .distinct()
                        .collect(Collectors.toList());
                boolean equalCollection = CollectionUtils.isEqualCollection(sortOrderNoSub, customerOrderNoSub);
                // 所有子单的扫码记录成功
                if(equalCollection){
                    LocalDateTime maxScanTime = null;

                    BigDecimal totalVolume = deliveryRecordByMain.stream()
                            .map(TmsSortingRecordEntity::getPackageVolume)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalWeight = deliveryRecordByMain.stream()
                            .map(TmsSortingRecordEntity::getPackageWeight)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    Optional<TmsSortingRecordEntity> maxScanTimeEntity = deliveryRecordByMain
                            .stream()
                            .filter(record -> record.getScanTime() != null)
                            .max(Comparator.comparing(TmsSortingRecordEntity::getScanTime));

                    if(maxScanTimeEntity.isPresent()){
                        TmsSortingRecordEntity recordEntity = maxScanTimeEntity.get();
                        maxScanTime = recordEntity.getScanTime();
                    }
                    MasterOrderPriceCalculationVo masterOrderPriceCalculationVo = new MasterOrderPriceCalculationVo();
                    masterOrderPriceCalculationVo.setOrderNumber(orderNo);
                    masterOrderPriceCalculationVo.setActualWeight(totalWeight);
                    masterOrderPriceCalculationVo.setScanTime(maxScanTime);
                    // 体积重：分拣体积 / 客户
                    if(null != volumeCoefficient && volumeCoefficient > 0){
                        // 体积从 m³ 转换为 cm³
                        BigDecimal volumeCm3 = totalVolume.multiply(StoreConstants.VOLUME_CM3_CONVERT_M3);
                        // 体积重 = 分拣体积 ÷ 客户材积重系数
                        BigDecimal volumeWeight = volumeCm3.divide(new BigDecimal(volumeCoefficient), 2, RoundingMode.HALF_UP)
                                .setScale(0, RoundingMode.CEILING);
                        masterOrderPriceCalculationVo.setVolumeWeight(volumeWeight);
                    }
                    masterOrderPriceCalculationVos.add(masterOrderPriceCalculationVo);
                }
            }
        }
        if(CollUtil.isNotEmpty(masterOrderPriceCalculationVos)){
            if(logger.isInfoEnabled()){
                logger.info("[核算价格][包裹扫描入仓] 核算数据={}",masterOrderPriceCalculationVos);
            }
            PriceCalculationYingShouRequestVo priceCalculationDTO = new PriceCalculationYingShouRequestVo();
            priceCalculationDTO.setOrders(masterOrderPriceCalculationVos);
            priceCalculationDTO.setProviderName("NB_YYZ");
            priceCalculationDTO.setSourceChannel(2);
            receivableService.calculatePriceAndCreateReceivable(priceCalculationDTO);
        }
        if(logger.isInfoEnabled()){
            logger.info("[核算价格][包裹扫描入仓] 执行完毕,startTime={},endTime={}", startTime, endTime);
        }
    }

}
