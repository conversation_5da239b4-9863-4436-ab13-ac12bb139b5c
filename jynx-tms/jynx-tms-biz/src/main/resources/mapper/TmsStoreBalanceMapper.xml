<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreBalanceMapper">

    <resultMap id="tmsStoreBalanceMap" type="com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity">
        <id property="id" column="id"/>
        <result property="storeCustomerId" column="store_customer_id"/>
        <result property="amount" column="amount"/>
        <result property="amountHash" column="amount_hash"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <update id="exchangeDeductAmount" parameterType="int">
        UPDATE tms_store_balance
        SET amount = amount - #{changeAmount}
        WHERE store_customer_id = #{storeCustomerId}
        AND amount_hash = #{amountHash}
    </update>
</mapper>
