package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderMainTracesVO {
    @Schema(description = "主单-跟踪单号")
    private String entrustedOrderNumber;

    @Schema(description = "子单的轨迹")
    private List<StoreTracesSubDetailVO> subTracesDetail;
}
