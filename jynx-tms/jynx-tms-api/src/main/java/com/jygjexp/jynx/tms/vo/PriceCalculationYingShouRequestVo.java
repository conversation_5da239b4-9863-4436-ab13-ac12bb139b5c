package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 价格计算插入应收记录请求参数
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@Schema(description = "价格计算插入应收记录请求参数")
public class PriceCalculationYingShouRequestVo {

    /**
     * 主单订单列表 - 改为主单维度的数据结构
     */
    @Valid
    @NotEmpty(message = "订单列表信息不能为空")
    @Schema(description = "主单订单列表信息")
    private List<MasterOrderPriceCalculationVo> orders;

    /**
     * 服务商名称（可选，默认为"NB"）
     */
    @Schema(description = "服务商名称，默认为NB")
    private String providerName = "NB";

    /**
     * 来源渠道
     */
    @Schema(description = "来源渠道 1-询价 2-分拣计算价格")
    private Integer sourceChannel;
}
