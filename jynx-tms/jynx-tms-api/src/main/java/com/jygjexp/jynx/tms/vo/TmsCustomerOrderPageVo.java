package com.jygjexp.jynx.tms.vo;

import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsLmdDriverEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 客户订单分页参数
 * @Date: 2025/3/13 15:26
 */
@Data
@FieldNameConstants
@Schema(description = "客户订单分页参数")
public class TmsCustomerOrderPageVo extends TmsCustomerOrderEntity {

    @Schema(description = "联系人")
    private String contacts;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "下单开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "下单结束时间")
    private LocalDateTime endTime;

    @Schema(description = "发货开始时间")
    private LocalDateTime shippingBeginTime;

    @Schema(description = "发货结束时间")
    private LocalDateTime shippingEndTime;

    @Schema(description = "到货开始时间")
    private LocalDateTime arrivalBeginTime;

    @Schema(description = "到货结束时间")
    private LocalDateTime arrivalEndTime;

    @Schema(description = "完成开始时间")
    private LocalDateTime finishBeginTime;

    @Schema(description = "完成结束时间")
    private LocalDateTime finishEndTime;

    @Schema(description="委托客户")
    private String entrustedCustomer;

    @Schema(description="客户推送渠道")
    private String customerIsPush;

    @Schema(description = "客户单量")
    private Integer count;

    @Schema(description="仓库名称")
    private String warehouseName;

    @Schema(description="仓库类型：0：一级仓，1：二级仓，3：驿站")
    private Integer warehouseType;

    @Schema(description="库存ID")
    private Long inventoryId;

    @Schema(description = "类型（0：全部，1：待指派，2：已指派）")
    private Integer type;

    @Schema(description="批量查询状态使用该参数")
    private String strOrderStatus;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "入库时间-开始时间")
    private LocalDateTime storageStartTime;

    @Schema(description = "入库时间-结束时间")
    private LocalDateTime storageEndTime;

    @Schema(description="出库开始时间")
    private LocalDateTime outboundStartTime;

    @Schema(description="出库结束时间")
    private LocalDateTime outboundEndTime;

    @Schema(description="运输任务单id")
    private Long taskOrderId;

    @Schema(description = "跟踪单号列表")
    private List<String> entrustedOrderNoList;

    @Schema(description = "司机信息")
    private Map<String,List<TmsLmdDriverPageVo>> tmsLmdDriverEntityMap;

    // -------------------------------------------------------------------- 揽收指派返回参数
    @Schema(description = "订单数量")
    private Integer orderCount;

    @Schema(description = "货物数量")
    private Integer totalCargoQuantity;

    @Schema(description = "货物已揽数量")
    private Integer collectedCount;

    @Schema(description = "货物未揽数量")
    private Integer unCollectedCount;

    @Schema(description = "货物揽收总重量")
    private BigDecimal collectedTotalWeight;

    @Schema(description = "主单号")
    private String entrustedOrderNumber;

    @Schema(description = "POD证明")
    private List<PodVoBySub> podList;

}
