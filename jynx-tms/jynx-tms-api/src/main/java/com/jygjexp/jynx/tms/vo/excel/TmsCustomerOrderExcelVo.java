package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: chenchang
 * @Description: 客户订单导出实体
 * @Date: 2025/3/30 19:37
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@Schema(description = "客户订单导出实体")
public class TmsCustomerOrderExcelVo {

    @ColumnWidth(10)
    @ExcelProperty("(CustomerOrderNumber)客户单号")
    @Schema(description="客户单号")
    private String customerOrderNumber;

    @ColumnWidth(10)
    @ExcelProperty("(EntrustedOrderNumber)委托单号")
    @Schema(description="委托单号")
    private String entrustedOrderNumber;

    @ColumnWidth(10)
    @ExcelProperty("(EntrustedCustomer)委托客户")
    @Schema(description="委托客户")
    private String entrustedCustomer;

    @ColumnWidth(10)
    @ExcelProperty("(CustomerId)客户ID")
    @Schema(description = "客户ID")
    private Long customerId;

    @ColumnWidth(6)
    @ExcelProperty("(OrderStatus)订单状态")
    @Schema(description="订单状态")
    private Integer orderStatus;

    @ColumnWidth(6)
    @ExcelProperty("(AuditStatus)审核状态")
    @Schema(description = "审核状态")
    private Integer auditStatus;

    @ColumnWidth(8)
    @ExcelProperty("(ShipperName)发货人姓名")
    @Schema(description="发货人姓名")
    private String shipperName;

    @ColumnWidth(15)
    @ExcelProperty("(ShipperPhone)发货人电话")
    @Schema(description="发货人电话")
    private String shipperPhone;

    @ColumnWidth(20)
    @ExcelProperty("(Origin)始发地")
    @Schema(description="始发地")
    private String origin;

    @ColumnWidth(10)
    @ExcelProperty("(ShipperPostalCode)发货邮编")
    @Schema(description="发货邮编")
    private String shipperPostalCode;

    @ColumnWidth(22)
    @ExcelProperty("(ShipperAddress)发货详细地址")
    @Schema(description="发货详细地址")
    private String shipperAddress;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedShippingTimeStart)预计发货时间开始")
    @Schema(description="预计发货时间开始")
    private LocalDateTime estimatedShippingTimeStart;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedShippingTimeEnd)预计发货时间结束")
    @Schema(description="预计发货时间结束")
    private LocalDateTime estimatedShippingTimeEnd;

    @ColumnWidth(10)
    @ExcelProperty("(IsTailgatePickup)尾板提货：0 No，1 Yes")
    @Schema(description="是否尾板提货：0 否，1 是")
    private Integer isTailgatePickup;

    @ColumnWidth(10)
    @ExcelProperty("(ReceiverName)到货人姓名")
    @Schema(description="到货人姓名")
    private String receiverName;

    @ColumnWidth(15)
    @ExcelProperty("(ReceiverPhone)到货人电话")
    @Schema(description="到货人电话")
    private String receiverPhone;

    @ColumnWidth(20)
    @ExcelProperty("(Destination)目的地")
    @Schema(description="目的地")
    private String destination;

    @ColumnWidth(10)
    @ExcelProperty("(DestPostalCode)到货邮编")
    @Schema(description="到货邮编")
    private String destPostalCode;

    @ColumnWidth(22)
    @ExcelProperty("(DestAddress)到货详细地址")
    @Schema(description="到货详细地址")
    private String destAddress;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedArrivalTimeStart)预计到货时间开始")
    @Schema(description="预计到货时间开始")
    private LocalDateTime estimatedArrivalTimeStart;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedArrivalTimeEnd)预计到货时间结束")
    @Schema(description="预计到货时间结束")
    private LocalDateTime estimatedArrivalTimeEnd;

    @ColumnWidth(10)
    @ExcelProperty("(IsTailgateUnloaded)尾板卸货：0 No，1 Yes")
    @Schema(description="是否尾板卸货：0 否，1 是")
    private Integer isTailgateUnloaded;

    @ColumnWidth(10)
    @ExcelProperty("(CarrierId)所属承运商ID")
    @Schema(description="所属承运商")
    private Long carrierId;

    @ColumnWidth(10)
    @ExcelProperty("(OrderType)订单类型：1 tray，2 package")
    @Schema(description="订单类型：1=托盘，2=包裹")
    private Integer orderType;

    @ColumnWidth(10)
    @ExcelProperty("(TransportType)运输类型")
    @Schema(description="运输类型：1=整车运输，2=零担运输")
    private Integer transportType;

    @ColumnWidth(10)
    @ExcelProperty("(CargoType)货物类型")
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;

    @ColumnWidth(10)
    @ExcelProperty("(AddressType)地址类型")
    @Schema(description="地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板，4=商业地址-不需要尾板")
    private Integer addressType;

    @ColumnWidth(10)
    @ExcelProperty("(BusinessModel)业务模式")
    @Schema(description = "业务模式：1 揽收，2 中大件，3 卡派")
    private Integer businessModel;

    @ColumnWidth(10)
    @ExcelProperty("(CargoQuantity)货品总数量")
    @Schema(description="货品总数量")
    private Integer cargoQuantity;

    @ColumnWidth(8)
    @ExcelProperty("(TotalWeight)总重量(kg)")
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

    @ColumnWidth(8)
    @ExcelProperty("(TotalVolume)总体积(m³)")
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;


    @ColumnWidth(8)
    @Schema(description = "forecastedPrice 预估价格")
    private BigDecimal forecastedPrice;

    @ColumnWidth(10)
    @ExcelProperty("(Operator)操作人")
    @Schema(description="操作人")
    private String operator;

    @ColumnWidth(18)
    @ExcelProperty("(Order time)下单时间")
    @Schema(description = "下单时间")
    private LocalDateTime createTime;

}
