package com.jygjexp.jynx.tms.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;


/**
 * 统一支付响应参数
 */
@Data
public class PaymentResponseVo {


    private String resCode;

    private String retCode;

    // 兼容 retParams 是字符串或 URL，取决于渠道
    private String retParams;

    private String payOrderId;

    private String mchOrderNo;

    private String retDetail;

    // 兼容两种字段名 qr_code / codeUrl
    @JsonAlias({"qr_code", "codeUrl"})
    private String qrCode;

    private String sign;
}
