package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 门店余额变更记录表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:44:45
 */
@Data
@TenantTable
@TableName("tms_store_balance_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店余额变更记录表")
public class TmsStoreBalanceRecordEntity extends Model<TmsStoreBalanceRecordEntity> {


    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

    /**
     * 门店余额id
     */
    @Schema(description="门店余额id")
    private Long storeBalanceId;

    /**
     * 门店客户id
     */
    @Schema(description="门店客户id")
    private Long storeCustomerId;

    /**
     * 业务key
     */
    @Schema(description="业务key")
    private String businessKey;

    /**
     * 流水单号
     */
    @Schema(description="流水单号")
    private String businessNumber;

    /**
     * 变更前金额
     */
    @Schema(description="变更前金额")
    private BigDecimal beforeAmount;

    /**
     * 变更金额
     */
    @Schema(description="变更金额")
    private BigDecimal changeAmount;

    /**
     * 变更后金额
     */
    @Schema(description="变更后金额")
    private BigDecimal afterAmount;

    /**
     * 交易类型0:充值-线上;5:充值-线下;15:下单扣除
     */
    @Schema(description="交易类型 0:充值-线上;5:充值-线下;15:下单扣除")
    private Integer type;

    /**
     * 子类型0:充值;1:消费
     */
    @Schema(description="子类型0:充值;1:消费")
    private Integer subType;

    /**
     * 充值状态:-1:充值失败;0:充值中;1:充值成功
     */
    @Schema(description="充值状态:-1:充值失败;0:充值中;1:充值成功")
    private Integer status;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}
