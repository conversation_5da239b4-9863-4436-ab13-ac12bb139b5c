package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店余额变更记录表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:44:45
 */
@Data
public class TmsStoreBalanceRecordPageVo {

    /**
     * 当前页
     */
    @NotNull
    @Schema(description = "当前页")
    private Integer current;

    /**
     * 页大小
     */
    @NotNull
    @Schema(description = "页大小")
    private Integer size;

    /**
     * 业务key
     */
    @Schema(description = "业务key")
    private String businessKey;

    /**
     * 流水单号
     */
    @Schema(description = "流水单号")
    private String businessNumber;

    /**
     * 交易类型0:充值-线上;5:充值-线下;15:下单扣除
     */
    @Schema(description = "交易类型0:充值-线上;5:充值-线下;15:下单扣除")
    private Integer type;

    /**
     * 子类型0:充值;1:消费
     */
    @Schema(description = "子类型0:充值;1:消费")
    private Integer subType;

    /**
     * 充值状态:-1:充值失败;0:充值中;1:充值成功
     */
    @Schema(description = "充值状态:-1:充值失败;0:充值中;1:充值成功")
    private Integer status;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime createStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDateTime createEndTime;

    /**
     * ids
     */
    @Schema(description = "ids")
    private List<Long> ids;
}
