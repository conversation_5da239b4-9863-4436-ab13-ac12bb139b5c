package com.jygjexp.jynx.tms.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.tms.constants.JiaYouApiConstants;
import com.jygjexp.jynx.tms.vo.FeeRequest;
import com.jygjexp.jynx.tms.vo.OrderRequest;
import com.jygjexp.jynx.tms.vo.ResponseResult;
import com.jygjexp.jynx.tms.vo.api.*;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;


public class PostBusinessUtils {
    private static final Logger logger = LoggerFactory.getLogger(PostBusinessUtils.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    /**
     * 创建订单
     * @param request
     * @return
     */
    public static ResponseResult<PackageOrderRoot> createRequest(OrderRequest request) {
        String url = JiaYouApiConstants.BASE_URL+"/api/orderNew/createOrder";
        OkHttpClient client = new OkHttpClient();
        ObjectMapper mapper = new ObjectMapper();
        try {
            // 构建请求体和头部
            String jsonBody = mapper.writeValueAsString(request);
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));
            Headers headers = Headers.of(getHeaders());
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(body)
                    .build();
            // 发送请求
            try (Response httpResponse = client.newCall(httpRequest).execute()) {
                if (httpResponse.body() != null) {
                    String responseBody = httpResponse.body().string();
                    if(logger.isInfoEnabled()){
                        logger.info("创建订单接口返回,result:{}",responseBody);
                    }
                    PackageOrderRoot packageOrderRoot = mapper.readValue(responseBody, PackageOrderRoot.class);
                    return  buildSuccessResult(packageOrderRoot);
                } else {
                    return buildErrorResult("创建订单接口失败,响应体为空");
                }
            }
        } catch (Exception e) {
            logger.error("创建订单失败,params:{}",request,e);
            return buildErrorResult("创建订单接口失败,请求异常");
        }
    }
    /**
     * 删除订单
     * @return
     */
    public static ResponseResult<ResponseRoot> deleteRequest(String orderNumber) {
        OkHttpClient client = new OkHttpClient();
        try {
            // 构造 multipart/form-data 请求体
            MultipartBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("orderNumber", orderNumber)
                    .build();

            // 构造请求对象
            Request request = new Request.Builder()
                    .url(JiaYouApiConstants.BASE_URL + "/api/orderNew/deleteOrder")
                    .post(requestBody)
                    .headers(Headers.of(getHeaders()))
                    .build();

            // 发送请求
            try (Response response = client.newCall(request).execute()) {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    if(logger.isInfoEnabled()){
                        logger.info("删除订单接口返回,result:{}",responseBody);
                    }
                    ResponseRoot responseRoot = mapper.readValue(responseBody, ResponseRoot.class);
                    int code = responseRoot.getCode();
                    String message = responseRoot.getMessage();
                    if(code == 1 && "success".equals(message)){
                        return buildSuccessResult(responseRoot);
                    }else{
                        return buildErrorResult("删除订单失败");
                    }
                } else {
                    return buildErrorResult("删除订单接口返回,响应体为空");
                }
            }
        } catch (Exception e) {
            logger.error("删除订单失败,params:{}",orderNumber,e);
            return buildErrorResult("删除订单失败");
        }
    }
    /**
     * 渠道询价
     * @param requestPayload
     * @return
     */
    public static ResponseResult<FreightRoot> calculateFreight(FeeRequest requestPayload) {
        OkHttpClient client = new OkHttpClient();
        try {
            // 1. 将实体对象转换为 JSON 字符串
            String json = mapper.writeValueAsString(requestPayload);
            // 2. 构建请求体
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, json);
            // 3. 构建请求
            Request request = new Request.Builder()
                    .url(JiaYouApiConstants.BASE_URL+"/outerApi/costCal")
                    .post(body)
                    .addHeader("apiKey", JiaYouApiConstants.API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();
            // 4. 执行请求并解析响应
            try (Response response = client.newCall(request).execute()) {
                if (null != response.body()) {
                    String responseBody = response.body().string();
                    if(logger.isInfoEnabled()){
                        logger.info("运费试算询价结果,result:{}",responseBody);
                    }
                    FreightRoot freightRoot = mapper.readValue(responseBody, FreightRoot.class);
                    return buildSuccessResult(freightRoot);
                } else {
                    return buildErrorResult("运费试算询价失败,响应体为空");
                }
            }
        } catch (Exception e) {
            logger.error("运费试算询价失败,params:{}",requestPayload,e);
            return buildErrorResult("运费试算询价失败,请求异常");
        }
    }
    /**
     * 轨迹跟踪
     * @param orderNumbers
     * @return
     */
    public static ResponseResult<PackageOrderTraceRoot> getPackageOrderTrace(String orderNumbers){
        OkHttpClient client = new OkHttpClient();
        try {
            // 构造 multipart/form-data 请求体
            MultipartBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("orderNumber", orderNumbers)
                    .build();

            // 构造请求对象
            Request request = new Request.Builder()
                    .url(JiaYouApiConstants.BASE_URL + "/api/orderNew/tracking/query/trackInfo")
                    .post(requestBody)
                    .headers(Headers.of(getHeaders()))
                    .build();

            // 发送请求
            try (Response response = client.newCall(request).execute()) {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    if(logger.isInfoEnabled()){
                        logger.info("轨迹跟踪接口返回,result:{}",responseBody);
                    }
                    PackageOrderTraceRoot packageOrderTraceRoot = mapper.readValue(responseBody, PackageOrderTraceRoot.class);
                    return buildSuccessResult(packageOrderTraceRoot);
                } else {
                    return buildErrorResult("轨迹跟踪接口返回,响应体为空");
                }
            }
        } catch (Exception e) {
            logger.error("轨迹跟踪接口查询失败,params:{}",orderNumbers,e);
            return buildErrorResult("轨迹跟踪接口查询失败");
        }
    }
    /**
     * 拦截订单
     * @param requestPayload
     * @return
     */
    public static ResponseResult<ResponseRoot> executeInterceptOrder(InterceptOrderRequest requestPayload) {
        String url = JiaYouApiConstants.BASE_URL+"/api/orderNew/interceptOrder";
        OkHttpClient client = new OkHttpClient();
        ObjectMapper mapper = new ObjectMapper();
        try {
            // 构建请求体和头部
            String jsonBody = mapper.writeValueAsString(requestPayload);
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));
            Headers headers = Headers.of(getHeaders());
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(body)
                    .build();
            // 发送请求
            try (Response response = client.newCall(httpRequest).execute()) {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    if(logger.isInfoEnabled()){
                        logger.info("拦截订单接口返回,result:{}",responseBody);
                    }
                    ResponseRoot responseRoot = mapper.readValue(responseBody, ResponseRoot.class);
                    int code = responseRoot.getCode();
                    String message = responseRoot.getMessage();
                    if(code == 1 && "success".equals(message)){
                        return buildSuccessResult(responseRoot);
                    }else{
                        return buildErrorResult("拦截订单失败");
                    }
                } else {
                    return buildErrorResult("拦截订单失败返回,响应体为空");
                }
            }
        } catch (Exception e) {
            logger.error("拦截订单失败,params:{}",requestPayload,e);
            return buildErrorResult("拦截订单失败,请求异常");
        }

    }

    /**
     * 获取订单详情 - 跟踪单号
     * @param orderNumber
     * @return
     */
    public static ResponseResult<PackageOrderDetailRoot> getPackageOrderDetail(String orderNumber){
        String url = JiaYouApiConstants.BASE_URL+"/api/orderNew/findOrderInfos";
        OkHttpClient client = new OkHttpClient();
        ObjectMapper mapper = new ObjectMapper();
        try {
            // 构建请求体和头部
            String jsonBody = mapper.writeValueAsString(orderNumber);
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));
            Headers headers = Headers.of(getHeaders());
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(body)
                    .build();
            // 发送请求
            try (Response httpResponse = client.newCall(httpRequest).execute()) {
                if (httpResponse.body() != null) {
                    String responseBody = httpResponse.body().string();
                    if(logger.isInfoEnabled()){
                        logger.info("查询订单详情,result:{}",responseBody);
                    }
                    PackageOrderDetailRoot packageOrderRoot = mapper.readValue(responseBody, PackageOrderDetailRoot.class);
                    return  buildSuccessResult(packageOrderRoot);
                } else {
                    return buildErrorResult("查询订单详情失败,响应体为空");
                }
            }
        } catch (Exception e) {
            logger.error("查询订单详情失败,params:{}",orderNumber,e);
            return buildErrorResult("查询订单详情失败,请求异常");
        }


    }


    private static <T> ResponseResult<T> buildErrorResult(String message) {
        ResponseResult<T> result = new ResponseResult<>();
        result.setCode(0);
        result.setMessage(message);
        result.setData(null);
        return result;
    }

    private static <T> ResponseResult<T> buildSuccessResult(T data) {
        ResponseResult<T> result = new ResponseResult<>();
        result.setCode(1);
        result.setMessage("操作成功");
        result.setData(data);
        return result;
    }
    /**
     * 获取请求头
     */
    private static Map<String, String>  getHeaders() {
        // 准备请求头
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("code", JiaYouApiConstants.CODE);
        headerMap.put("apiKey", JiaYouApiConstants.API_KEY);
        // 获取当前时间并格式化（非 Windows 加 12 小时）
        LocalDateTime now = LocalDateTime.now();
        if (!System.getProperty("os.name").toLowerCase().contains("win")) {
            now = now.plusHours(12);
        }
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        headerMap.put("timestamp", timestamp);
        headerMap.put("sign",getMD5(JiaYouApiConstants.CODE + JiaYouApiConstants.API_KEY));
        return headerMap;
    }
    /**
     * 计算MD5值
     */
    public static String getMD5(String input) {
        try {
            // 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 将输入字符串转换为字节数组并计算哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为16进制格式的字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                // 每个字节转换为2位16进制数
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

}
