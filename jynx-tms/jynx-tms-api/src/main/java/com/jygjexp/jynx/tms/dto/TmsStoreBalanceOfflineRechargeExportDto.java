package com.jygjexp.jynx.tms.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;

/**
 * 手工调账
 *
 * <AUTHOR>
 * @date 2025-07-30 03:25:12
 */
@Data
public class TmsStoreBalanceOfflineRechargeExportDto {

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式", converter = PaymentMethodConverter.class)
    @Schema(description = "支付方式 1 银行转账 2 EMT支付")
    private Integer paymentMethod;

    /**
     * 付款信息
     */
    @ExcelProperty("付款信息")
    @Schema(description = "付款信息")
    private String paymentInfo;

    /**
     * 付款账号
     */
    @ExcelProperty("付款账号")
    @Schema(description = "付款账号")
    private String paymentAccount;

    /**
     * 付款方
     */
    @ExcelProperty("付款方")
    @Schema(description = "付款方")
    private String payer;

    /**
     * 收款账号
     */
    @ExcelProperty("收款账号")
    @Schema(description = "收款账号")
    private String receiveAccount;

    /**
     * 收款方
     */
    @ExcelProperty("收款方")
    @Schema(description = "收款方")
    private String payee;

    /**
     * 金额
     */
    @ExcelProperty("金额")
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ExcelProperty("币种")
    @Schema(description = "币种")
    private String currency;

    /**
     * 交易日期
     */
    @ExcelProperty("交易日期")
    @NotBlank(message = "交易日期不能为空")
    @Schema(description = "交易日期")
    private LocalDate transactionDate;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    @Schema(description = "备注")
    private String notes;

    /**
     * 审批人
     */
    @ExcelProperty("审批人")
    @Schema(description = "审批人")
    private String verifyer;

    /**
     * 审批时间
     */
    @ExcelProperty("审批时间")
    @Schema(description = "审批时间")
    private LocalDateTime verifyTime;

    /**
     * 审批备注
     */
    @ExcelProperty("审批备注")
    @Schema(description = "审批备注")
    private String verifyRemarks;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态",converter = StatusConverter.class)
    @Schema(description = "状态 0:待审核;1:已审核;2:已驳回")
    private Integer status;

    /**
     * 创建人
     */
    @ExcelProperty("创建人")
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 门店名称
     */
    @ExcelProperty("门店名称")
    @Schema(description = "门店名称")
    private String storeName;


    public static class PaymentMethodConverter implements Converter<Integer>{

        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "银行转账");
            map.put(2, "EMT支付");

            Integer value = context.getValue();
            if(map.containsKey(value)){
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }

    public static class StatusConverter implements Converter<Integer>{

        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "待审核");
            map.put(1, "已审核");
            map.put(2, "已驳回");
            Integer value = context.getValue();
            if(map.containsKey(value)){
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }
}