package com.jygjexp.jynx.tms.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;

/**
 * 门店余额变更记录表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:44:45
 */
@Data
public class TmsStoreBalanceRecordExportDto {

    /**
     * 业务key
     */
    @ExcelProperty("跟踪单号(Tracking Number)")
    @Schema(description = "业务key")
    private String businessKey;

    /**
     * 流水单号
     */
    @ExcelProperty("流水单号(Serial Number)")
    @Schema(description = "流水单号")
    private String businessNumber;

    /**
     * 变更金额
     */
    @ExcelProperty("变更金额(Change Amount)")
    @Schema(description = "变更金额")
    private BigDecimal changeAmount;


    /**
     * 交易类型0:充值-线上;5:充值-线下;15:下单扣除
     */
    @ExcelProperty(value = "交易类型(Transaction type)", converter = TypeConverter.class)
    @Schema(description = "交易类型 0:充值-线上;5:充值-线下;15:下单扣除")
    private Integer type;

    /**
     * 子类型0:充值;1:消费
     */
    @ExcelProperty(value = "子类型(Sub Transaction type)", converter = SubTypeConverter.class)
    @Schema(description = "子类型0:充值;1:消费")
    private Integer subType;

    /**
     * 充值状态:-1:充值失败;0:充值中;1:充值成功
     */
    @ExcelProperty(value = "充值状态(Status)", converter = StatusConverter.class)
    @Schema(description = "充值状态:-1:充值失败;0:充值中;1:充值成功")
    private Integer status;

    /**
     * 备注
     */
    @ExcelProperty("备注(Remark)")
    @Schema(description = "备注")
    private String remark;

    public static class TypeConverter implements Converter<Integer> {

        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "充值-线上");
            map.put(5, "充值-线下");
            map.put(15, "下单扣除");

            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }

    public static class SubTypeConverter implements Converter<Integer> {

        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "充值");
            map.put(1, "消费");

            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }

    public static class StatusConverter implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(-1, "充值失败");
            map.put(0, "充值中");
            map.put(1, "充值成功");

            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }
}
