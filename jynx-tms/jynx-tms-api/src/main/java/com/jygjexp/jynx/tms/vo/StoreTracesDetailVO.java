package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class StoreTracesDetailVO {

    @Schema(description = "跟踪子单号")
    private String subEntrustedOrder;

    @Schema(description = "主跟踪单号")
    private String mainEntrustedOrder;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "订单状态内容描述")
    private String orderStatusContext;

    @Schema(description = "操作时间")
    private LocalDateTime operateTime;
}
