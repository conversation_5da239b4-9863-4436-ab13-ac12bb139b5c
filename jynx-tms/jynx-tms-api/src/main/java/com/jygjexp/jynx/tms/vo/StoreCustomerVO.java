package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: daiyuxuan
 * @create: 2025/8/1
 */

@Data
@Schema(description = "门店客户")
public class StoreCustomerVO {
    @Schema(description = "客户ID")
    private Long id;

    @Schema(description = "客户代码")
    private String code;

    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "客户类型")
    private Integer type;

    @Schema(description = "客户等级")
    private Integer level;
}
