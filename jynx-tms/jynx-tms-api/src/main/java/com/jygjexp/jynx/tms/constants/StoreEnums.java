package com.jygjexp.jynx.tms.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface StoreEnums {

    interface AddressBook {
        @AllArgsConstructor
        @Getter
        enum Type{
            EXPRESS(0,"快递");
            private Integer value;
            private String name;
        }

        @AllArgsConstructor
        @Getter
        enum SubType{
            SHIPPER(0,"发货");
            private Integer value;
            private String name;
        }
    }

    interface StoreOrder{
        @AllArgsConstructor
        @Getter
        enum SendType{
            DELIVERY_TO_STORE(0,"送货到店"),
            COLLECTING_FROM_NB(5,"NB上门揽收"),
            COLLECTING_FROM_PROVIDER(10,"服务商上门揽收");
            private Integer value;
            private String name;
        }

        @AllArgsConstructor
        @Getter
        enum OrderStatus{
            AWAITING_SHIPMENT(0,"待发货","AWAITING_SHIPMENT"),
            STORE_PICKUP(5,"门店取货","STORE_PICKUP"),
            PROVIDER_PICKUP(10,"服务商已取货","PROVIDER_PICKUP"),
            PRINT_SCRIPT(15,"已打印","PRINT_SCRIPT"), // 面单已打印
            IN_TRANSIT(20,"运输中","IN_TRANSIT"),
            COMPLETED(25,"已完成","COMPLETED"),
            EXCEPTION(30,"订单异常","EXCEPTION"),
            CANCEL(35,"已取消","CANCEL"),
            REFUND(40,"已退款","REFUND");
            private Integer value;
            private String name;
            private String eName;
        }

        @AllArgsConstructor
        @Getter
        enum ExternalType{
            NB(0,"NB系统推单"),
            PACKET(1,"小包系统退单");
            private Integer value;
            private String name;
        }

        @AllArgsConstructor
        @Getter
        enum PrintStatus{
            UNPRINTED(0,"未打印"),
            PRINTED(1,"已打印");
            private Integer value;
            private String name;
        }

        @AllArgsConstructor
        @Getter
        enum WriteOffFlag{
            UnWriteOff(0,"未核销"),
            WriteOff(1,"已核销");
            private Integer value;
            private String name;
        }

        @AllArgsConstructor
        @Getter
        enum SubFlag{
            MAIN(0,"主单"),
            SUB(1,"子单");
            private Integer value;
            private String name;
        }

    }

    interface TemplateChangeRecord{
        @AllArgsConstructor
        @Getter
        enum OperationType{
            DISABLE(0,"停用"),
            ENABLE(1,"启用"),
            MODIFY(2,"修改"),
            DELETE(3,"删除");
            private Integer value;
            private String name;
        }
    }

    interface StoreBalanceRecord{

        @AllArgsConstructor
        @Getter
        enum BusinessType{
            RECHARGE_ONLINE(0,"充值-线上"),
            RECHARGE_OFFLINE(5,"充值-线下"),
            ORDER_DEDUCT(10,"下单扣除");
            private Integer value;
            private String name;
        }

        @AllArgsConstructor
        @Getter
        enum SubType{
            RECHARGE(0,"充值"),
            CONSUME(1,"消费");
            private Integer value;
            private String name;
        }
    }

    interface PackageSystem{

        @AllArgsConstructor
        @Getter
        enum PackageOrderStatus{
            PACKAGE_500("500","末端提取",""),
            PACKAGE_501("501","末端转运",""),
            PACKAGE_510("510","开始派送","Item out for delivery"),
            PACKAGE_512("512","到达待取",""),
            PACKAGE_520("520","签收","Delivered"),
            PACKAGE_511("511","派送失败",""),
            PACKAGE_535("535","地址错误",""),
            PACKAGE_542("542","退件到仓",""),
            PACKAGE_548("548","包裹丢失","");
            private String value;
            private String name;
            private String eName;
        }
    }



}
