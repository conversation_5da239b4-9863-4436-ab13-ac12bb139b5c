package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderTracesVO {

    @Schema(description = "轨迹信息")
    private List<StoreOrderMainTracesVO> orderTraces;

    @Schema(description = "订单状态统计")
    private List<TracesOrderStatusVO> orderStatus;

    @Schema(description = "总订单数量-子单维度")
    private Integer orderCounts;
}
