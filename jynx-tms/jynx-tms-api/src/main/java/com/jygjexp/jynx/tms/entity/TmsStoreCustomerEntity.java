package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.sensitive.annotation.Sensitive;
import com.jygjexp.jynx.common.sensitive.core.SensitiveTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 门店客户表
 *
 * <AUTHOR>
 * @date 2025-07-14 17:45:27
 */
@Data
@TenantTable
@TableName("tms_store_customer")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店客户表")
public class TmsStoreCustomerEntity extends Model<TmsStoreCustomerEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description="用户ID")
    private Long userId;

    /**
     * 门店id
     */
    @Schema(description="门店id")
    private Long storeId;

    /**
     * 客户代码
     */
    @Schema(description="客户代码")
    private String code;

    /**
     * 客户名称
     */
    @Schema(description="客户名称")
    private String name;

    /**
     * 联系电话
     */
    @Sensitive(type = SensitiveTypeEnum.MOBILE_PHONE)
    @Schema(description="联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @Sensitive(type = SensitiveTypeEnum.EMAIL)
    @Schema(description="邮箱")
    private String email;

    /**
     * 客户等级:0:normal 1:vip 2:svip
     */
    @Schema(description="客户等级:0:normal 1:vip 2:svip")
    private Integer level;

    /**
     * 客户类型:0:网上客户 1：门店客户
     */
    @Schema(description="客户类型:0:网上客户 1：门店客户")
    private Integer type;

    /**
     * 状态（0、禁用；1、启用）
     */
    @Schema(description="状态（0、禁用；1、启用）")
    private Integer status;

    /**
     * 拓展
     */
    @Schema(description="拓展")
    private String extend;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Long revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}
