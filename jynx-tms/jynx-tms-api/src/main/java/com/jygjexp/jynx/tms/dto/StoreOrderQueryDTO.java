package com.jygjexp.jynx.tms.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderQueryDTO {

    @Schema(description = "跟踪单号")
    private String entrustedOrderNumber;

    @Schema(description = "下单开始时间")
    private LocalDateTime orderTimeStart;

    @Schema(description = "下单结束时间")
    private LocalDateTime orderTimeEnd;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "收发件-联系人")
    private String contacts;

    @Schema(description = "收发件-联系电话")
    private String phone;

    @Schema(description = "始发地")
    private String shipperOrigin;

    @Schema(description = "目的地")
    private String receiverDest;

    @Schema(description = "客户ID")
    private Long storeCustomerId;

    @Schema(description = "门店ID")
    private Long storeId;

    @Schema(description = "服务商ID")
    private Long providerId;

    @Schema(description = "分页查询-数量")
    private long size;

    @Schema(description = "分页查询-页码")
    private long current;
}
