package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderDetailVO {
    @Schema(description = "订单ID")
    private Long id;

    // 发货信息
    @Schema(description = "发货-联系人")
    private String shipperName;

    @Schema(description = "始发地")
    private String shipperOrigin;

    @Schema(description = "发货-地址")
    private String shipperAddress;

    @Schema(description = "发货-联系电话")
    private String shipperPhone;

    @Schema(description = "始发地-邮编")
    private String shipperPostalCode;


    // 收货信息
    @Schema(description = "收货-联系人")
    private String receiverName;

    @Schema(description = "目的地")
    private String receiverDest;

    @Schema(description = "收货-地址")
    private String receiverAddress;

    @Schema(description = "收货-联系电话")
    private String receiverPhone;

    @Schema(description = "收货地-邮编")
    private String receiverPostalCode;

    @Schema(description = "货物单位")
    private Integer unitType;

    // 货物信息
    @Schema(description = "货物信息")
    List<StoreOrderGoodsVO> storeOrderGoods;

    // 服务商
    @Schema(description = "服务商")
    private String providerName;

    @Schema(description = "服务商-服务方式")
    private String providerServiceWay;

    @Schema(description = "服务商-运输时效")
    private String providerTransportTime;

    // 基本配置
    @Schema(description = "是否POD/面签")
    private Integer podSign;

    @Schema(description = "是否保险")
    private Integer insuranceSign;

    @Schema(description = "是否带磁带电")
    private Integer batterySign;

    @Schema(description = "发货方式0:送货到店;5:NB上门揽收;10:服务商上门揽收")
    private Integer sendType;

    @Schema(description = "门店名称")
    private String storeName;

    // 费用信息
    @Schema(description = "服务商&费用")
    private StoreProviderRelationVO storeProviderRelationVO;

    // 备注信息
    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "异常信息")
    private List<TmsStoreOrderExceptionVo> exceptionList;

    @Schema(description = "赔付信息")
    private List<TmsStoreOrderPkgPayVo> pkgPayList;

    @Schema(description = "客户信息")
    private StoreCustomerVO customerInfo;

    @Schema(description = "订单状态")
    private String orderStatus;
}
