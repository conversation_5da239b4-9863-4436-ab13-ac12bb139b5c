package com.jygjexp.jynx.tms.dto;

import com.jygjexp.jynx.tms.vo.StoreProviderRelationVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderDTO {
    @Schema(description = "主键id,修改时必传")
    private Long id;

    @Schema(description = "门店用户id")
    private Long storeCustomerId;
    // 地址信息
    @Schema(description = "发货-联系人")
    private String shipperName;

    @Schema(description = "发货-联系电话")
    private String shipperPhone;

    @Schema(description = "始发地")
    private String shipperOrigin;

    @Schema(description = "始发地-邮编")
    private String shipperPostalCode;

    @Schema(description = "始发地-地址")
    private String shipperAddress;

    @Schema(description = "收货-联系人")
    private String receiverName;

    @Schema(description = "收货-联系电话")
    private String receiverPhone;

    @Schema(description = "收货地")
    private String receiverDest;

    @Schema(description = "收货地-邮编")
    private String receiverPostalCode;

    @Schema(description = "收货地-地址")
    private String receiverAddress;

    @Schema(description = "单位制:0公制 1:英制")
    private Integer unitType;
    // 货物信息
    @Schema(description = "货物信息")
    private List<StoreOrderGoodsDTO> storeOrderGoods;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否POD/面签:0否;1是")
    private Integer podSign;

    @Schema(description = "是否保险:0否;1是")
    private Integer insuranceSign;

    @Schema(description = "是否带磁带电:0否;1是")
    private Integer batterySign;

    @Schema(description = "门店id")
    private Long storeId;

    @Schema(description = "运费总额")
    private BigDecimal totalFreightAmount;

    @Schema(description = "供应商运费详情")
    private StoreProviderRelationVO storeProviderRelationVO;

    @Schema(description = "发货方式:0:送货到店;5:NB上门揽收;10:服务商上门揽收")
    private Integer sendType;
}
