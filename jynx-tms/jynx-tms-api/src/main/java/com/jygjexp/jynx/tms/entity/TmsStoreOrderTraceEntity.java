package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 门店订单轨迹跟踪
 *
 * <AUTHOR>
 * @date 2025-07-31 15:12:18
 */
@Data
@TenantTable
@TableName("tms_store_order_trace")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店订单轨迹跟踪")
public class TmsStoreOrderTraceEntity extends Model<TmsStoreOrderTraceEntity> {


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 跟踪子单号
	*/
    @Schema(description="跟踪子单号")
    private String subEntrustedOrder;

	/**
	* 跟踪主单号
	*/
    @Schema(description="跟踪主单号")
    private String mainEntrustedOrder;

	/**
	* 订单状态：0: 待发货；5: 门店已取货；10: 服务商已取货；15: 已打印；20: 运输中；25: 已完成；30: 订单异常；35: 已取消;40:已退款
	*/
    @Schema(description="订单状态：0: 待发货；5: 门店已取货；10: 服务商已取货；15: 已打印；20: 运输中；25: 已完成；30: 订单异常；35: 已取消;40:已退款")
    private Integer orderStatus;

	/**
	* 订单状态内容描述
	*/
    @Schema(description="订单状态内容描述")
    private String orderStatusContext;

	/**
	* 外部系统状态
	*/
    @Schema(description="外部系统状态")
    private String externalExtend;

	/**
	 *操作时间
	 */
	@Schema(description="操作时间")
	private LocalDateTime operateTime;

	/**
	 * 同步最新时间
	 */
	@Schema(description="同步最新时间")
	private LocalDateTime syncTime;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记：0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}
