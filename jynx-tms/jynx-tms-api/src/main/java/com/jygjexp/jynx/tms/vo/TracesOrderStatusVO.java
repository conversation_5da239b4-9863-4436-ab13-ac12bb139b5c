package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class TracesOrderStatusVO {

    @Schema(description = "状态")
    private Integer orderStatus;

    @Schema(description = "状态名称")
    private String orderStatusName;

    @Schema(description = "状态数量")
    private Integer orderStatusCount;
}
