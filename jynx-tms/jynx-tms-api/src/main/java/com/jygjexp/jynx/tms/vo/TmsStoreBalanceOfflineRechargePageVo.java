package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 手工调账
 *
 * <AUTHOR>
 * @date 2025-07-30 03:25:12
 */
@Data
public class TmsStoreBalanceOfflineRechargePageVo {

    /**
     * 页大小
     */
    @NotNull
    @Schema(description = "页大小")
    private Integer size;

    /**
     * 当前页
     */
    @NotNull
    @Schema(description = "当前页")
    private Integer current;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 创建开始时间
     */
    @Schema(description = "创建开始时间")
    private LocalDateTime createStartTime;

    /**
     * 创建结束时间
     */
    @Schema(description = "创建结束时间")
    private LocalDateTime createEndTime;

    /**
     * 审核开始时间
     */
    @Schema(description = "审核开始时间")
    private LocalDateTime verifyStartTime;

    /**
     * 审核结束时间
     */
    @Schema(description = "审核结束时间")
    private LocalDateTime verifyEndTime;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * ids
     */
    @Schema(description = "ids")
    private List<Long> ids;
}