package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 价格计算请求参数
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@Schema(description = "价格计算请求参数")
public class PriceCalculationRequestVo {

    /**
     * 订单列表
     */
    @Valid
    @NotEmpty(message = "订单列表不能为空")
    @Schema(description = "订单列表")
    private List<TmsOrderPriceCalculationVo> orders;

    /**
     * 服务商名称（可选，默认为"NB_YYZ"）
     */
    @Schema(description = "服务商名称，默认为NB_YYZ")
    private String providerName = "NB_YYZ";

    /**
     * 来源渠道
     */
    @Schema(description = "来源渠道 1-询价 2-分拣计算价格，默认为询价")
    private Integer sourceChannel= 1;
}
